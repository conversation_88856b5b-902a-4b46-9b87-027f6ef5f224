import React, { useContext, useEffect, useRef, useState } from 'react';
import {
  ScrollView,
  SafeAreaView,
  Platform,
  View,
  StyleSheet,
  Image,
  Pressable,
  StatusBar,
  Dimensions,
} from 'react-native';
import { Appbar, useTheme } from 'react-native-paper';
import { useScrollToTop } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import Icon from 'react-native-vector-icons/Feather';

import {
  changeWindowHeightWidth,
  localimage,
  reTimeDarkTheme,
  reTimeTheme,
  windowWidth,
} from '_utils';
import { THEME_LIGHT } from '../../store/reducers/theme';
import { LinearGradientButton } from '_theme_components';
import { AppThemeContext } from '../../hooks/themeContext';
import { useDebounce } from '_helpers';

const MainLayout = ({
  children,
  hideScroll,
  navigation,
  isFocused,
  paddingtop,
  paddinghorizontal,
  pageName,
  headerShow,
  back,
  title,
  bgColor,
  ShowDarkMode,
  drawerOption,
  showRecover,
  submitData,
  disableButton,
}) => {
  const ref = useRef(null);

  const { colors, dark } = useTheme();
  const dispatch = useDispatch();
  const { theme, setTheme, themeChange } = useContext(AppThemeContext);
  const { debounce } = useDebounce();

  const [size, setSize] = useState(0);

  useEffect(() => {
    if (isFocused) {
      try {
        ref.current?.scrollTo({
          y: 0,
          animated: true,
        });
      } catch (error) {
        console.error('Error in scrollTo function:', error);
      }
    }
  }, [isFocused]);
  useScrollToTop(ref);

  useEffect(() => {
    const updateWindowHeight = () => {
      const newWindowHeight = Dimensions.get('window').height;
      const newWindowWidth = Dimensions.get('window').width;
      changeWindowHeightWidth(newWindowHeight, newWindowWidth);
      setSize(newWindowWidth);
    };

    var dim = Dimensions.addEventListener('change', updateWindowHeight);

    return () => {
      // dim.remove('change', updateWindowHeight);
    };
  }, []);

  return (
    <>
      <SafeAreaView style={{ backgroundColor: colors.primary }}>
        <StatusBar
          hidden={false}
          animated
          backgroundColor={colors.primary}
          barStyle="light-content"
        />
      </SafeAreaView>
      <SafeAreaView
        style={{
          flex: 1,
          paddingBottom: Platform.OS === 'ios' ? 100 : 0,
          backgroundColor: bgColor && !dark ? '#fff' : colors.onSurface,
        }}
      >
        {headerShow && (
          <>
            <View
              style={{
                paddingTop: 10,
                paddingHorizontal: 15,
                justifyContent: 'space-between',
                flexDirection: 'row',
              }}
            >
              {back ? (
                <Appbar.BackAction
                  size={30}
                  color={colors.primary}
                  style={[
                    styles.backIconStyle,
                    { backgroundColor: dark ? 'rgba(0, 0, 0, 0.5)' : 'rgba(255, 255, 255, 0.5)' },
                  ]}
                  onPress={() => navigation.goBack()}
                />
              ) : drawerOption ? (
                <Appbar.Action
                  size={45}
                  animated={false}
                  icon={() => <Image source={localimage.menu} style={styles.menuIcon} />}
                  onPress={() => {
                    navigation.openDrawer();
                  }}
                />
              ) : null}
              <Appbar.Content title={title} />
              {showRecover && (
                <>
                  <View style={{ width: windowWidth * 0.2, marginTop: 15, marginRight: 15 }}>
                    <LinearGradientButton
                      disabled={disableButton}
                      fontSize={13}
                      btnStyle={{ ...styles.studyButton, fontSize: 12, width: windowWidth * 0.2 }}
                      contentStyles={{ paddingVertical: 10 }}
                      title={'submit Data'}
                      onPress={() => {
                        submitData();
                      }}
                    />
                  </View>
                  <View style={{ width: windowWidth * 0.2, marginTop: 15 }}>
                    <LinearGradientButton
                      disabled={disableButton}
                      fontSize={13}
                      btnStyle={{ ...styles.studyButton, marginLeft: 5, width: windowWidth * 0.2 }}
                      title={'Download Data'}
                      onPress={() => {
                        submitData(true);
                      }}
                    />
                  </View>
                </>
              )}

              {ShowDarkMode && (
                <View
                  style={[
                    styles.showDarModeStyle,
                    { backgroundColor: dark ? 'rgba(0, 0, 0, 0.5)' : 'rgba(255, 255, 255, 1)' },
                  ]}
                >
                  <Pressable
                    onPress={() =>
                      theme === reTimeDarkTheme
                        ? debounce(() => themeChange(reTimeTheme))
                        : debounce(() => themeChange(reTimeDarkTheme))
                    }
                    style={styles.themeButtonStyle}
                  >
                    {theme === reTimeDarkTheme ? (
                      <>
                        <Icon name={'moon'} color={'#fff'} size={30} />
                        <Image
                          source={localimage.dots}
                          style={[styles.dotStyle, { tintColor: '#fff' }]}
                        />
                      </>
                    ) : (
                      <>
                        <Image
                          source={localimage.dots}
                          style={[styles.dotStyle, { tintColor: colors.primary }]}
                        />
                        <Icon name={'sun'} color={colors.primary} size={27} />
                      </>
                    )}
                  </Pressable>
                </View>
              )}
            </View>
          </>
        )}

        <View
          style={[
            styles.viewContainerStyle,
            {
              paddingTop: paddingtop ?? 15,
              paddingHorizontal: paddinghorizontal ?? 15,
            },
          ]}
        >
          {hideScroll ? (
            <View
              style={[
                styles.viewContainer,
                { flex: 1, width: windowWidth * 0.87, marginTop: windowWidth * 0.01 },
              ]}
            >
              {children}
            </View>
          ) : (
            <ScrollView
              showsVerticalScrollIndicator={false}
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={[
                styles.viewContainer,
                { width: windowWidth * 0.87, marginTop: windowWidth * 0.01, flexGrow: 1 },
              ]}
              ref={ref}
            >
              {children}
            </ScrollView>
          )}
        </View>
      </SafeAreaView>
    </>
  );
};

export default MainLayout;

const styles = StyleSheet.create({
  studyButton: {
    marginTop: 1,
  },
  menuIcon: {
    resizeMode: 'contain',
    width: 37,
    height: 37,
  },
  backIconStyle: {
    height: 60,
    width: 60,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
  },
  showDarModeStyle: {
    borderWidth: 0.8,
    borderColor: '#c2c2c2',
    borderRadius: 25,
    padding: 4,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    alignSelf: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 1.84,

    elevation: Platform.OS == 'ios' ? 0 : 5,
  },
  themeButtonStyle: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    margin: 2,
  },
  dotStyle: {
    resizeMode: 'contain',
    height: 35,
    width: 35,
  },
  viewContainerStyle: {
    flex: 1,
  },
  viewContainer: {
    paddingHorizontal: 15,
    paddingTop: 15,
    alignSelf: 'center',
    paddingBottom: 50,
  },
});
