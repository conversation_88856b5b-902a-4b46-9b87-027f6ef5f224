import { Keyboard, Platform, StyleSheet, View } from 'react-native';
import React, { useState } from 'react';
import { useSelector } from 'react-redux';

import Icon from 'react-native-vector-icons/Feather';
import { AText, Textinputs, IconButton, StudyHeader, StudyNextCard } from '_theme_components';
import { FontStyle, windowHeight, windowWidth } from '_utils';
import { isEmpty } from '_helpers';
import { useTheme } from 'react-native-paper';

const FrequencyScreen = ({
  navigation,
  nextElementData,
  navigateScreen,
  setFreqInput,
  freqInput,
  navigateToStudyPage,
}) => {
  const [keyBoardFocus, setKeyBoardFocus] = useState(true);
  const { colors } = useTheme();
  const { elementSelect, taskSelect } = useSelector((state) => state.createStudy);
  const { controllingElementID } = taskSelect;
  const goToScreen = async (screen, elementContinue = false, element) => {
    Keyboard.dismiss();
    setKeyBoardFocus(false);
    navigateScreen(screen, elementContinue, element);
    setTimeout(() => {
      setKeyBoardFocus(true);
    }, 500);
  };
  const ItemView = ({ item }) => {
    return (
      <View
        style={{ width: windowWidth > windowHeight ? '85%' : '90%', alignSelf: 'center' }}
        key={item.id}
      >
        <StudyNextCard
          ShowEC={true}
          controllingElementID={
            !isEmpty(controllingElementID) && controllingElementID == item._id ? true : false
          }
          data={item}
          icon={
            !isEmpty(controllingElementID) && controllingElementID == item._id
              ? 'target'
              : 'arrow-right'
          }
          navigateNext={(val) => {
            goToScreen('nextElement', val, item);
          }}
        />
      </View>
    );
  };

  return (
    <>
      <StudyHeader
        navigation={navigation}
        Title={'Frequency'}
        FrequencyOUM={elementSelect.unitOfMeasure ?? ''}
        SubTitle={'Enter frequency '}
        showBack={true}
        bachkHandler={() => navigateToStudyPage()}
        icon={'sort'}
        searchPress={''}
        ShowIcon={false}
      />

      <View style={[styles.container, { width: windowWidth * 0.72 }]}>
        <Textinputs
          autofocus={keyBoardFocus}
          placeholder="Frequency number"
          onerror={false}
          keyboardtype={Platform.OS == 'ios' ? 'numbers-and-punctuation' : 'number-pad'}
          mode={'Flat'}
          maxLength={5}
          value={freqInput}
          stylesTextInput={{ backgroundColor: 'rgba(255, 255, 255, 0)', width: windowWidth * 0.7 }}
          onchange={(txt) => {
            let numberRegex = /^\d+$/;
            if (numberRegex.test(txt) || isEmpty(txt)) {
              setFreqInput(txt);
            }
          }}
        />
        <IconButton
          icon={<Icon name="minus-circle" style={styles.iconStyle} size={35} />}
          btnStyle={styles.minusbuttonStyle}
          bgColor={colors.secondary}
          onPress={() => {
            let fre = parseInt(freqInput);
            if (!isEmpty(freqInput) && freqInput > 0) {
              let frI = fre - 1;
              setFreqInput(frI.toString());
            }
          }}
        />

        <IconButton
          icon={<Icon name="plus-circle" style={styles.iconStyle} size={35} />}
          btnStyle={styles.plusbuttonStyle}
          bgColor={colors.secondary}
          onPress={() => {
            let fre = isEmpty(freqInput) ? 0 : parseInt(freqInput);
            fre = fre + 1;
            setFreqInput(fre.toString());
          }}
        />
      </View>
      {!isEmpty(nextElementData) && (
        <>
          <View style={styles.contentContainer}>
            <AText
              fontWeight={FontStyle.fontBold}
              styleText={{ color: '#D0D0D0' }}
              fontSize={'title'}
            >
              Next Elements
            </AText>
          </View>
          {nextElementData.map((item, index) => (
            <ItemView key={index.toString()} item={item} />
          ))}
        </>
      )}
    </>
  );
};

export default FrequencyScreen;

const styles = StyleSheet.create({
  plusbuttonStyle: {
    borderRadius: 40,
    width: 60,
    height: 60,
    position: 'absolute',
    right: 25,
    top: 10,
    padding: 0,
  },
  minusbuttonStyle: {
    borderRadius: 40,
    width: 60,
    height: 60,
    position: 'absolute',
    right: 105,
    top: 10,
    padding: 0,
  },
  container: {
    marginTop: 5,
    alignItems: 'center',
    paddingBottom: 18,
    // flexDirection: 'row',
  },
  contentContainer: {
    marginTop: windowHeight * 0.045,
    width: '75%',
  },
  iconStyle: {
    alignSelf: 'center',
    marginHorizontal: 7,
  },
});
