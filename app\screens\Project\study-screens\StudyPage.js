import {
  ActivityIndicator,
  Button,
  Dimensions,
  Keyboard,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import React, { useState, useEffect } from 'react';
import { useIsFocused } from '@react-navigation/native';
import { Modal, useTheme } from 'react-native-paper';
import { useDispatch, useSelector } from 'react-redux';
import AIcon from 'react-native-vector-icons/AntDesign';

import { AuthLoading, FabButton, StudyLayout, StudyPoPUP } from '_theme_components';
import {
  APP_PRIMARY_COLOR,
  APP_SECONDARY_COLOR,
  changeWindowHeightWidth,
  tables,
  windowHeight,
  windowWidth,
} from '_utils';
import {
  LoadingProgressBar,
  OpenCameraView,
  ProgressBar,
  RateScreenFooter,
  getAllStudyData,
  getStudyData,
  insertIntoTable,
  isEmpty,
  launchCamera,
  updateStudyData,
} from '_helpers';
import {
  ContinueSelectedElement,
  canContinueElement,
  checkNotesEC,
  checkTaskQuestionTrigger,
  isYesOrNo,
  loadElementDataFetch,
} from '_provider';
import AreaScreen from './area';
import TaskScreen from './task';
import ElementScreen from './element';
import RateScreen from './rate';
import RateNFrequencyScreen from './rate&Frequency';

const StudyPage = ({ navigation }) => {
  const isFocused = useIsFocused();
  const dispatch = useDispatch();

  const {
    projectSelect,
    areaSelect,
    taskSelect,
    elementSelect,
    saveImage,
    saveImageLocal,
    studyID,
    obsrvationData,
    obsStart,
    ECclick,
    loading,
    studyData,
    cameraLoading,
    localId,
  } = useSelector((state) => state.createStudy);
  const { dbDetail } = useSelector((state) => state.user);

  const { show_on_task_elements } = useSelector((state) => state.questions);
  const { allElementList, elementList, createEntry } = useSelector((state) => state.serverReducer);

  const [studyPage, setstudyPage] = useState(global.STUDYPAGESELECT);

  const [viewCamera, setViewCamera] = useState(false);
  const [checkReRate, setCheckReRate] = useState(false);
  const [loader, setLoader] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [pageDataRefresh, setPageDataRefresh] = useState({
    refreshing: false,
    onRefreshPage: false,
  });
  const [areaModal, setAreaModal] = useState(false);
  const [showQuestionPopUP, setShowQuestionPopUP] = useState(false);

  const [size, setSize] = useState(0);

  const [questionCheckList, setQuestionCheckList] = useState([]);
  const [showQuestion, setShowQuestion] = useState({});

  const [ECNotes, setECNotes] = useState('');
  const [elementNotes, setElementNotes] = useState('');
  const [startTime, setStartTime] = useState('');
  const [areaFieldValue, setAreaFieldValue] = useState('');
  const [freetext, setFreetext] = useState('');
  const [selectedVal, setSelectedVal] = useState('');
  const [selectedCheckboxVal, setselectedCheckboxVal] = useState([]);
  const [elementRate, setElementRate] = useState({
    ratingSlect: '100',
    avgratingSlect: 0,
    freqInput: '',
  });
  const [reRatingPromptTime, setReRatingPromptTime] = useState(0);

  const [changingScreenTimer, setChangingScreenTimer] = useState({
    changingScreen: false,
    resetRatingTime: false,
    elementChange: false,
  });

  const [areaErrorsField, setAreaErrorsField] = useState('');

  // ------element Creation ---------------------
  const [errorValue, setErrorValue] = useState({
    elementName: '',
    category: '',
    orderAfter: '',
    rating: '',
    relaxationAllowance: '',
    contingencyAllowance: '',
  });
  const [elementViewNext, setElementViewNext] = useState(false);
  const [closeCanCreateModal, setCloseCanCreateModal] = useState(false);
  const [elementValue, setElementValue] = useState({
    elementName: '',
    category: '',
    ordeAfterrName: 'Add last',
    orderAfter: 'addLast',
    rating: '',
    categoryModalShow: false,
    elementModalShow: false,
    count: true,
    contingencyAllowance: '',
    relaxationAllowance: '',
  });

  useEffect(() => {
    const updateWindowHeight = () => {
      const newWindowHeight = Dimensions.get('window').height;
      const newWindowWidth = Dimensions.get('window').width;
      changeWindowHeightWidth(newWindowHeight, newWindowWidth);
      setSize(newWindowWidth);
    };

    Dimensions.addEventListener('change', updateWindowHeight);

    return () => {
      // Dimensions.remove('change', updateWindowHeight);
    };
  }, [Dimensions]);

  useEffect(() => {
    if (
      isFocused &&
      projectSelect.rerateElement &&
      !isEmpty(projectSelect.promptTriggerTime) &&
      projectSelect.rating
    ) {
      let promptTriggerTime = JSON.parse(projectSelect.promptTriggerTime);
      let minutes = promptTriggerTime.minutes || 0;
      let seconds = promptTriggerTime.seconds || 0;
      let time = minutes * 60 + seconds;
      setReRatingPromptTime(time);
    }
  }, [isFocused]);

  const checkTaskQuestions = (elementID) => {
    var questionCheck = [];
    if (!isEmpty(show_on_task_elements)) {
      questionCheck = checkTaskQuestionTrigger(
        show_on_task_elements,
        elementID ?? elementSelect._id,
        taskSelect._id
      );
    }
    if (!isEmpty(questionCheck)) {
      setShowQuestionPopUP(true);
      setShowQuestion(questionCheck[0]);
      setQuestionCheckList(questionCheck);
    }
  };
  const updateAnswer = async (submit, val) => {
    setShowQuestionPopUP(false);
    if (submit) {
      const payload = {
        question: showQuestion.text,
        answer: val,
        studyID: studyID,
        time: new Date().getTime(),
        questionID: showQuestion._id,
        triggerType: showQuestion.triggerType,
        // observationID: 1675774350670
      };
      insertIntoTable(tables.ANSWERS_TABLE, [payload], dbDetail);
    }
    setShowQuestion({});
    let ind = questionCheckList.findIndex((n) => n._id === showQuestion._id);
    let nextQuestion = ind >= 0 ? questionCheckList[ind + 1] : '';
    if (!isEmpty(ind) && ind !== -1 && !isEmpty(nextQuestion)) {
      setShowQuestion(nextQuestion);
      setShowQuestionPopUP(true);
    }
  };
  useEffect(() => {
    if (ECclick && !isEmpty(elementSelect)) {
      CheckECNote();
    }
  }, [ECclick, elementSelect]);

  const CheckECNote = () => {
    if (ECclick) {
      let note = checkNotesEC(elementSelect._id, areaSelect._id, taskSelect._id, obsrvationData);
      setECNotes(note);
      if (!isEmpty(note)) {
        setElementNotes(note);
      }
    }
  };
  useEffect(() => {
    if (
      // studyPage == 'frequencyScreen' ||
      // studyPage == 'nextElementScreen' ||
      studyPage == 'RateNFrequencyScreen'
      // || (studyPage == 'RateScreen' && elementSelect.count == 2)
    ) {
      setNextElement();
    }
  }, [studyPage, elementSelect]);

  useEffect(() => {
    if (!isEmpty(elementSelect)) {
      if (!obsStart) {
        setStartTime(new Date());
        dispatch({
          type: 'ELEMENT_SCREEN_OPENED',
        });
        setChangingScreenTimer({
          ...changingScreenTimer,
          elementChange: true,
        });
      }
      checkTaskQuestions();
    }
  }, [elementSelect]);

  useEffect(() => {
    if (!isEmpty(taskSelect)) {
      dispatch(loadElementDataFetch(allElementList, taskSelect._id));
    }
  }, [taskSelect, allElementList]);

  const setNextElement = async () => {
    canElementContinue();

    setTimeout(() => {
      setChangingScreenTimer({
        elementChange: false,
        resetRatingTime: false,
        changingScreen: false,
      });
    }, 500);

    // this.content.resize();
  };

  const canElementContinue = (nextElementDatas = elementList) => {
    if (!isEmpty(nextElementDatas)) {
      nextElementDatas.forEach((item) => {
        if (!isEmpty(item) && !isYesOrNo(item.name)) {
          item.canContinueElement = dispatch(
            canContinueElement(item._id, areaSelect._id, taskSelect._id, obsrvationData)
          );
        }
      });
      setSize(43);
    }
    return nextElementDatas;
  };

  useEffect(() => {
    if (!isEmpty(areaSelect) && !isEmpty(elementSelect)) {
      canElementContinue();
    }
  }, [areaSelect]);

  useEffect(() => {
    (async () => {
      const a = await getStudyData(tables.STUDY_DATA_TABLE, localId, studyID, dbDetail);
      if (!isEmpty(a)) {
        let photo;
        let photoLocal;
        if (!isEmpty(a[0].photo)) {
          photo = a[0].photo + ',' + saveImage;
          photoLocal = a[0].photoLocal + ',' + saveImageLocal;
        } else {
          photo = saveImage;
          photoLocal = saveImageLocal;
        }
        updateStudyData(tables.STUDY_DATA_TABLE, photo, photoLocal, localId, studyID, dbDetail);
        dispatch({ type: 'CLEAR_IMAGE' });
      }
    })();
  }, [localId]);

  const goToScreen = async (screen, elementContinue = false, element, elementScreen = false) => {
    // console.log('1st log', screen, element, element, elementScreen);
    if (changingScreenTimer.changingScreen) {
      return;
    }
    let storeData = isEmpty(obsrvationData) ? [] : [...obsrvationData];
    const elementRating = elementSelect.rating;
    const projectRating = projectSelect.rating;
    const projectReRating = projectSelect.rerateElement;

    if (!elementScreen || screen == 'YesNoElement') {
      // setFreqInput('');
      setChangingScreenTimer({
        ...changingScreenTimer,
        changingScreen: true,
        resetRatingTime: true,
      });
      if (!elementScreen) {
        let continiousObs = '';
        if (ECclick && !isEmpty(elementSelect) && !isYesOrNo(elementSelect.name)) {
          continiousObs = await ContinueSelectedElement(
            elementSelect._id,
            areaSelect._id,
            taskSelect._id,
            storeData
          );
        }
        let ratin =
          elementRate.ratingSlect == 125 || elementRate.ratingSlect == 0
            ? 'Not Rated'
            : elementRate.ratingSlect;
        ratin =
          elementRating == 3 &&
          projectRating == 1 &&
          projectReRating &&
          (elementRate.avgratingSlect > 0 || elementRate.avgratingSlect == 'Not Rated')
            ? elementRate.avgratingSlect
            : ratin;

        storeData.push({
          localID: storeData.length + 1,
          area: areaSelect._id,
          areaName: areaSelect.name,
          task: taskSelect._id,
          taskName: taskSelect.name,
          element: elementSelect._id,
          elementName: elementSelect.name,
          startTime: new Date(startTime).getTime(),
          endTime: new Date().getTime(),
          rating: ratin,
          frequency:
            isEmpty(elementRate.freqInput) && isEmpty(continiousObs)
              ? 1
              : isEmpty(elementRate.freqInput) && !isEmpty(continiousObs)
                ? 0
                : elementRate.freqInput,
          notes: elementNotes,
          photo: !isEmpty(saveImage) ? saveImage.toString() : '',
          photoLocal: !isEmpty(saveImageLocal) ? saveImageLocal.toString() : '',
          duration: new Date().getTime() - new Date(startTime).getTime(),
          continuesObservation: continiousObs,
          studyID: studyID,
          isElementCreated: elementSelect._id.indexOf('element') > -1 ? true : false,
          isAreaCreated: areaSelect._id.indexOf('area') > -1 ? true : false,
        });
      }
      if (
        !isEmpty(element) &&
        (screen === 'nextElement' || screen == 'YesNoElement') &&
        isYesOrNo(element.name)
      ) {
        checkTaskQuestions(element._id);
        storeData.push({
          localID: storeData.length + 1,
          area: areaSelect._id,
          areaName: areaSelect.name,
          task: taskSelect._id,
          taskName: taskSelect.name,
          element: element._id,
          elementName: element.name,
          notes: screen == 'YesNoElement' ? elementNotes : '',
          photo: screen == 'YesNoElement' && !isEmpty(saveImage) ? saveImage.toString() : '',
          photoLocal:
            screen == 'YesNoElement' && !isEmpty(saveImageLocal) ? saveImageLocal.toString() : '',
          startTime: new Date().getTime(),
          endTime: new Date().getTime(),
          rating: element.rating === 2 || element.rating === 3 ? 100 : 'Not Rated',
          frequency: 1,
          studyID: studyID,
          duration: 0,
          isElementCreated: element._id.indexOf('element') > -1 ? true : false,
          isAreaCreated: areaSelect._id.indexOf('area') > -1 ? true : false,
        });
        if (!obsStart) {
          dispatch({
            type: 'ELEMENT_SCREEN_OPENED',
          });
        }
      }
      dispatch({
        type: 'STORE_OBSERVATION',
        payload: storeData,
      });
      setElementNotes('');
      setStartTime(new Date());
    }
    setCheckReRate(false);
    if (screen !== 'quick-area') {
      setElementRate({
        ratingSlect: '100',
        avgratingSlect: 0,
        freqInput: '',
      });
    }

    let navigateTo;
    if (screen === 'nextElement') {
      if (!isEmpty(element) && isYesOrNo(element.name)) {
        navigateTo = 'task';
      } else {
        const { rating, count } = element;
        let projectRating = projectSelect.rating;
        let rate = rating === 1 ? 'Not Rated' : 100;
        navigateTo =
          // (projectRating == 1 && rating === 3) || count === 1
          //   ?
          'RateNFrequencyScreen';
        // : projectRating == 1 && rating === 3 && count !== 1
        //   ? 'RateScreen'
        //   : count === 1
        //     ? 'frequencyScreen'
        //: 'nextElementScreen';
        setElementRate({
          avgratingSlect: 0,
          ratingSlect: rate,
          freqInput: '',
        });
        dispatch({
          type: 'STORE_RATE_ELEMENT_FREQ',
          payload: { element: element, ECclick: elementContinue },
        });
      }
    } else if (screen === 'quick-area') {
      if (studyPage !== 'nextElementScreen') {
        setElementRate({
          avgratingSlect: 0,
          ratingSlect: elementSelect.rating === 1 ? 'Not Rated' : 100,
          freqInput: '',
        });
      }
      navigateTo = studyPage;
    } else if (!isEmpty(screen)) {
      let chngeScreen = screen == 'YesNoElement' ? 'task' : screen;
      dispatch({ type: `CLEAR_UPTO_${chngeScreen.toUpperCase()}` });
      navigateTo = chngeScreen;
      // setNextElementData([]);
    }

    navigateToStudyPage(navigateTo, elementScreen);

    if (!elementScreen || screen == 'YesNoElement') {
      let storStudyData =
        !isEmpty(element) && isYesOrNo(element.name) && screen !== 'YesNoElement'
          ? storeData.slice(-2)
          : storeData.slice(-1);
      try {
        await Promise.all([
          insertIntoTable(tables.STUDY_DATA_TABLE, storStudyData, dbDetail),
          insertIntoTable(tables.RECOVERY_DATA_TABLE, storStudyData, dbDetail),
        ]);
      } catch (error) {}
    }
  };
  const navigateToStudyPage = (item, elementScreen = false) => {
    if (item !== 'RateNFrequencyScreen') {
      setCheckReRate(false);
    }

    if (!elementScreen) {
      setTimeout(() => {
        setChangingScreenTimer({
          elementChange: false,
          resetRatingTime: false,
          changingScreen: false,
        });
      }, 200);
    }
    global.STUDYPAGESELECT = item;
    setShowCreateModal(false);
    setstudyPage(item);
  };
  useEffect(() => {
    if (
      !isEmpty(elementSelect) &&
      projectSelect.rerateElement &&
      reRatingPromptTime > 0 &&
      elementSelect.rating == 3 &&
      projectSelect.rating == 1
    ) {
      checkReRatePrompt();
    }
  }, [elementSelect, studyPage, areaSelect]);

  const checkReRatePrompt = () => {
    if (studyPage === 'RateNFrequencyScreen' && projectSelect.rerateElement) {
      setCheckReRate(true);
    }
  };

  const navigationFromHeader = (val) => {
    if (
      studyPage == 'frequencyScreen' ||
      studyPage == 'nextElementScreen' ||
      studyPage == 'RateNFrequencyScreen' ||
      studyPage == 'RateScreen'
    ) {
      if (!isEmpty(ECNotes)) {
        setElementNotes('');
      }
      setECNotes('');
    }
    dispatch({ type: `CLEAR_UPTO_${val.toUpperCase()}` });
    navigateToStudyPage(val);
  };

  const openCamera = async () => {
    let camerpermision = await launchCamera();
    if (camerpermision) {
      setViewCamera(true);
    }
  };
  return (
    <>
      {loader || loading ? <AuthLoading /> : null}
      {cameraLoading ? (
        <View style={styles.load}>
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <AIcon name="cloudupload" size={18} color={'#000'} />
            <Text style={{ fontSize: 16, marginLeft: 5 }}>Image is uploading</Text>
          </View>
          <LoadingProgressBar dontShowPercent />
        </View>
      ) : null}
      <StudyLayout
        onRefresh={() => {
          setPageDataRefresh({
            onRefreshPage: true,
            refreshing: true,
          });
        }}
        navigateToStudyPage={(val, navBreadCrumb) => {
          navBreadCrumb ? navigationFromHeader(val) : navigateToStudyPage(val);
        }}
        studyPage={studyPage}
        refreshing={pageDataRefresh.refreshing}
        navigation={navigation}
        setElementNotes={(val) => {
          setElementNotes(val);
        }}
        ElementNote={elementNotes}
        rating={elementRate.ratingSlect == 125 ? 'Not Rated' : elementRate.ratingSlect}
        stopObservation={() => goToScreen('study-End')}
        resetRatingTimer={changingScreenTimer.resetRatingTime}
        resetFrequency={() => {
          setElementRate({
            ...elementRate,
            ratingSlect: 100,
            // freqInput: '',
          });
        }}
        elementChangedResetTimer={changingScreenTimer.elementChange}
        checkReRate={checkReRate}
        reRatingPromptTime={reRatingPromptTime}
        selectYESorNO={(selectedItem) => goToScreen('YesNoElement', false, selectedItem, true)}
      >
        {/* <Button onPress={() => getData()} title="click" /> */}
        {studyPage == 'area' ? (
          <AreaScreen
            modalView={false}
            areaFieldValue={areaFieldValue}
            setAreaFieldValue={(val) => setAreaFieldValue(val)}
            onRefreshPage={pageDataRefresh.onRefreshPage}
            setRefreshFalse={() => {
              setPageDataRefresh({
                onRefreshPage: false,
                refreshing: false,
              });
            }}
            setShowCreateModal={() => {
              setShowCreateModal(false);
            }}
            navigateTo={(val) => {
              navigateToStudyPage(val);
            }}
            showCreateModal={showCreateModal}
            navigation={navigation}
            areaErrorsField={areaErrorsField}
            setAreaErrorsField={(val) => setAreaErrorsField(val)}
          />
        ) : studyPage == 'task' ? (
          <TaskScreen
            onRefreshPage={pageDataRefresh.onRefreshPage}
            setRefreshFalse={() => {
              setPageDataRefresh({
                onRefreshPage: false,
                refreshing: false,
              });
            }}
            setLoader={(val) => {
              setLoader(val);
            }}
            navigateTo={(val) => {
              navigateToStudyPage(val);
            }}
            loader={loader}
            navigation={navigation}
          />
        ) : studyPage == 'element' ? (
          <ElementScreen
            onRefreshPage={pageDataRefresh.onRefreshPage}
            loader={loader}
            setRefreshFalse={() => {
              setPageDataRefresh({
                onRefreshPage: false,
                refreshing: false,
              });
            }}
            errorValue={errorValue}
            elementViewNext={elementViewNext}
            closeCanCreateModal={closeCanCreateModal}
            elementValue={elementValue}
            setErrorValue={(val) => setErrorValue(val)}
            setElementViewNext={(val) => setElementViewNext(val)}
            setCloseCanCreateModal={(val) => setCloseCanCreateModal(val)}
            setElementValue={(val) => setElementValue(val)}
            setLoader={(val) => setLoader(val)}
            CanElementContinue={() => canElementContinue()}
            showCreateModal={showCreateModal}
            setShowCreateModal={() => setShowCreateModal(false)}
            navigateScreen={(screen, elementContinue, element, elemScreen) => {
              goToScreen(screen, elementContinue, element, elemScreen);
            }}
            navigateTo={(val) => navigateToStudyPage(val)}
            navigation={navigation}
          />
        ) : studyPage == 'RateNFrequencyScreen' ? (
          <RateNFrequencyScreen
            setFreqInput={(val) =>
              setElementRate({
                ...elementRate,
                freqInput: val,
              })
            }
            freqInput={elementRate.freqInput}
            CanElementContinue={() => canElementContinue()}
            selectedElementIndex={elementList.indexOf(elementSelect)}
            ratingSlect={elementRate.ratingSlect}
            setRatingsAndFreq={(rating, avgRating, setFreq) => {
              setElementRate({
                ratingSlect: rating,
                avgratingSlect: avgRating,
                freqInput: elementRate.freqInput,
              });
              // setFreq ? navigateToStudyPage('frequencyScreen') : '';
            }}
            navigateScreen={(screen, elementContinue, element) => {
              goToScreen(screen, elementContinue, element);
            }}
            navigation={navigation}
            reRatingPromptTime={reRatingPromptTime}
          />
        ) : studyPage == 'RateScreen' ? (
          <RateScreen
            setFreqInput={(val) =>
              setElementRate({
                ...elementRate,
                freqInput: val,
              })
            }
            freqInput={elementRate.freqInput}
            // nextElementData={nextElementData}
            ratingSlect={elementRate.ratingSlect}
            setRatingsAndFreq={(rating, avgRating, setFreq) => {
              setElementRate({
                ratingSlect: rating,
                avgratingSlect: avgRating,
                freqInput: setFreq ? '' : elementRate.freqInput,
              });
              setFreq ? navigateToStudyPage('frequencyScreen') : '';
            }}
            navigateScreen={(screen, elementContinue, element) => {
              goToScreen(screen, elementContinue, element);
            }}
            navigation={navigation}
          />
        ) : null}
      </StudyLayout>
      {/* ----Area modal start------ */}
      <Modal
        animationType="slide"
        transparent={true}
        onDismiss={() => setAreaModal(false)}
        dismissable={false}
        contentContainerStyle={{
          alignSelf: 'center',
          justifyContent: 'center',
        }}
        style={{ alignSelf: 'center', justifyContent: 'center' }}
        onRequestClose={() => {
          try {
            setAreaModal(false);
          } catch (e) {
            console.log(e, ' error on request close studypage');
          }
        }}
        visible={areaModal}
      >
        <View
          style={[
            styles.changeAreaModal,
            {
              width: windowWidth > windowHeight ? windowHeight * 0.72 : windowWidth * 0.72,
              height: windowWidth > windowHeight ? windowHeight * 0.7 : windowWidth * 0.85,
            },
          ]}
        >
          <StudyLayout
            refreshControl={true}
            studyPage={'area'}
            onRefresh={() => {
              setPageDataRefresh({
                ...pageDataRefresh,
                onRefreshPage: true,
              });
              setTimeout(() => {
                setPageDataRefresh({
                  onRefreshPage: false,
                  refreshing: false,
                });
              }, 2500);
            }}
            refreshing={pageDataRefresh.refreshing}
            stopObservation={() => {}}
            navigation={navigation}
            title={'Assistant 1'}
            modalView={true}
          >
            <AreaScreen
              modalView={true}
              setShowCreateModal={() => {}}
              navigateTo={(val) => {
                setAreaModal(false), goToScreen('quick-area');
              }}
              showCreateModal={false}
              navigation={navigation}
            />
          </StudyLayout>
        </View>
      </Modal>
      {/* ----Area modal end----- */}
      {(studyPage == 'frequencyScreen' ||
        studyPage == 'nextElementScreen' ||
        studyPage == 'RateNFrequencyScreen' ||
        studyPage == 'RateScreen') && (
        <RateScreenFooter
          modalOpen={areaModal}
          navigateTo={(val) => {
            val == 'quick-area'
              ? (Keyboard.dismiss(),
                setChangingScreenTimer({
                  ...changingScreenTimer,
                  resetRatingTime: true,
                }),
                setAreaModal(true))
              : goToScreen(val);
          }}
        />
      )}
      {/* ----Question-ReminderPopup */}
      {!isEmpty(showQuestion) && (
        <StudyPoPUP
          question={showQuestion}
          modalShow={showQuestionPopUP}
          freetext={freetext}
          setFreetext={(val) => setFreetext(val)}
          selectedVal={selectedVal}
          setSelectedVal={(val) => setSelectedVal(val)}
          selectedCheckboxVal={selectedCheckboxVal}
          setselectedCheckboxVal={(val) => setselectedCheckboxVal(val)}
          onPhotoSubmit={() => {
            openCamera();
            !showQuestion.answerRequired && updateAnswer(false);
          }}
          onDismiss={() => {
            showQuestion.answerRequired ? '' : updateAnswer(false);
          }}
          onConfirm={(val) => {
            updateAnswer(true, val);
          }}
        />
      )}
      {viewCamera ? (
        <OpenCameraView
          study={obsrvationData}
          projectID={projectSelect._id}
          customerID={projectSelect.customerID}
          cancel={() => setViewCamera(false)}
          ImageName={studyData.customerName + `_` + studyData.name}
        />
      ) : null}
      {/* ----Question-ReminderPopup */}
      {(studyPage == 'role' && createEntry.roleCreate) ||
      (studyPage == 'area' && createEntry.areaCreate) ||
      (studyPage == 'element' && createEntry.elementCreate) ? (
        <FabButton icons="add" style={styles.fab} onClick={() => setShowCreateModal(true)} />
      ) : null}
    </>
  );
};

export default StudyPage;

const styles = StyleSheet.create({
  load: {
    backgroundColor: '#0C9FEF',
    alignSelf: 'center',
    position: 'absolute',
    width: '73%',
    height: 50,
    paddingVertical: 5,
    zIndex: 10,
    bottom: 10,
    paddingLeft: 10,
    borderRadius: 8,
  },
  fab: {
    position: 'absolute',
    marginHorizontal: 50,
    right: 0,
    bottom: windowHeight * 0.035,
    padding: 5,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: windowHeight * 0.05,
  },
  changeAreaModal: {
    alignSelf: 'center',
  },
});
