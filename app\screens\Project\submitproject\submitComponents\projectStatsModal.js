import { StyleSheet, Text, View } from 'react-native';
import React from 'react';
import { AButton, AText, BottomModalUI, LinearGradientButton } from '_theme_components';
import { FontStyle, windowHeight, windowWidth } from '_utils';
import Icon from 'react-native-vector-icons/Ionicons';
import { useTheme } from 'react-native-paper';

const ProjectStatsModal = ({
  setShowStudyPermissionModal,
  studyExport,
  projectStatsShow,
  showStudyPermissionModal,
  setEmailNproject,
  emailSubmit,
  projectStat,
  submitStudy,
  cancelPress,
}) => {
  const { colors, dark } = useTheme();

  return (
    <BottomModalUI
      width={'100%'}
      closeModal={() => {
        setShowStudyPermissionModal(false);
      }}
      height={
        windowHeight > 1000 && studyExport && projectStatsShow
          ? // ?)
            windowHeight * 0.43
          : windowHeight > 1000 && (studyExport === 1 || projectStatsShow === 1)
            ? // ?)
              windowHeight * 0.35
            : windowHeight * 0.3
      }
      modalShow={showStudyPermissionModal}
      showScroll={true}
      closeShow
      fontSize={'title'}
      styleTitle={{ marginHorizontal: 0, marginVertical: 5 }}
      title={' Data Submission'}
    >
      <View style={styles.submitContainer}>
        <AText styleText={{ paddingTop: 12 }} fontWeight={FontStyle.fontMedium} fontSize={'medium'}>
          You are about to submit all data for this study.{' '}
        </AText>
        <AText styleText={{ paddingTop: 9 }} fontWeight={FontStyle.fontMedium} fontSize={'medium'}>
          If you would like to edit this data before you sync it then please tap 'Cancel'. If you
          are happy to continue, then please tap 'Submit'
        </AText>
        <AText styleText={{ paddingTop: 9 }} fontWeight={FontStyle.fontMedium} fontSize={'medium'}>
          Please Make sure you have strong internet connection before submitting study.
        </AText>
      </View>
      <View style={styles.checkBoxView}>
        {studyExport ? (
          <AButton
            mode={'text'}
            onPress={() => {
              setEmailNproject('emailSubmit');
            }}
            btnStyle={styles.checkBoxViewLine}
            icon={
              <Icon
                name={emailSubmit ? 'checkbox-outline' : 'square-outline'}
                color={
                  emailSubmit
                    ? colors.primary
                    : dark
                      ? 'rgba(255, 255, 255, 0.9)'
                      : 'rgba(0, 0, 0, 0.9)'
                }
                style={styles.iconStyle}
                size={30}
              />
            }
            fontSize={'medium'}
            fontWeight={FontStyle.fontRegular}
            styleText={{
              textTransform: 'capitalize',
              color: emailSubmit
                ? colors.primary
                : dark
                  ? 'rgba(255, 255, 255, 0.9)'
                  : 'rgba(0, 0, 0, 0.9)',
            }}
            title={'Send study to email'}
          />
        ) : null}
        {projectStatsShow ? (
          <AButton
            mode={'text'}
            activeOpacity={0.9}
            onPress={() => {
              setEmailNproject('projectStat');
            }}
            btnStyle={styles.checkBoxViewLine}
            icon={
              <Icon
                name={projectStat ? 'checkbox-outline' : 'square-outline'}
                color={
                  projectStat
                    ? colors.primary
                    : dark
                      ? 'rgba(255, 255, 255, 0.9)'
                      : 'rgba(0, 0, 0, 0.9)'
                }
                style={styles.iconStyle}
                size={30}
              />
            }
            fontSize={'medium'}
            fontWeight={FontStyle.fontRegular}
            styleText={{
              textTransform: 'capitalize',
              color: projectStat
                ? colors.primary
                : dark
                  ? 'rgba(255, 255, 255, 0.9)'
                  : 'rgba(0, 0, 0, 0.9)',
            }}
            title={'Send project stats report'}
          />
        ) : null}
      </View>
      <View style={styles.submitStatButtonContainer}>
        <LinearGradientButton
          disabled={false}
          btnStyle={{ width: windowWidth * 0.27 }}
          contentStyles={{ paddingVertical: 10 }}
          onPress={() => {
            cancelPress();
          }}
          title={'cancel'}
          fontSize={'small'}
          compact={true}
        />
        <LinearGradientButton
          disabled={false}
          btnStyle={{ width: windowWidth * 0.27 }}
          contentStyles={{ paddingVertical: 10 }}
          onPress={() => {
            submitStudy();
          }}
          title={'submit'}
          fontSize={'small'}
        />
      </View>
    </BottomModalUI>
  );
};

export default ProjectStatsModal;

const styles = StyleSheet.create({
  submitStatButtonContainer: {
    paddingVertical: 19,
    width: '87%',
    flexDirection: 'row',
    alignSelf: 'center',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  submitContainer: {
    marginTop: -15,
    width: '95%',
    alignSelf: 'center',
  },
  checkBoxView: {
    borderColor: '#E8EAED',
    paddingHorizontal: 25,
    width: '100%',
    alignSelf: 'center',
  },
  checkBoxViewLine: {
    flexDirection: 'row',
    alignSelf: 'flex-start',
    alignItems: 'center',
    marginTop: 15,
  },
});
