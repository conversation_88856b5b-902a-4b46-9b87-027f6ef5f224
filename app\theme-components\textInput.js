import { StyleSheet, View } from 'react-native';
import React, { createRef } from 'react';
import { TextInput, useTheme } from 'react-native-paper';
import { useEffect } from 'react';
import { AText } from '_theme_components';
import { isEmpty } from '_helpers';
import { FontStyle } from '_utils';
import PropTypes from 'prop-types';

const Textinputs = ({
  label,
  onerror,
  secureTextEntry,
  placeholder,
  value,
  onchange,
  onblur,
  mode,
  keyboardtype,
  backgroundColor,
  stylesTextInput,
  textViewInputStyle,
  autofocus,
  disabled,
  maxLength,
  placeholderTextColor,
  underlineColor,
  textcolor,
  error,
  Icon,
}) => {
  const inputref = createRef();
  const onFocus = () => {
    inputref.current.focus();
  };
  useEffect(() => {
    if (autofocus) onFocus();
  }, [autofocus]);
  const { dark } = useTheme();

  Textinputs.propTypes = {
    label: PropTypes.string,
    onerror: PropTypes.bool,
    secureTextEntry: PropTypes.bool,
    placeholder: PropTypes.string,
    value: PropTypes.string,
    onchange: PropTypes.func,
    onblur: PropTypes.func,
    mode: PropTypes.string,
    keyboardtype: PropTypes.string,
    backgroundColor: PropTypes.string,
    stylesTextInput: PropTypes.object,
    textViewInputStyle: PropTypes.object,
    autofocus: PropTypes.bool,
    disabled: PropTypes.bool,
    maxLength: PropTypes.number,
    placeholderTextColor: PropTypes.string,
    underlineColor: PropTypes.string,
    textcolor: PropTypes.string,
    error: PropTypes.string,
    Icon: PropTypes.Icon,
  };

  return (
    <View
      style={[
        styles.textViewInputStyle,
        { backgroundColor: backgroundColor ?? 'rgba(255, 255, 255, 0)' },
        textViewInputStyle,
      ]}
    >
      {!isEmpty(label) && (
        <AText
          styleText={{ textAlign: 'left' }}
          fontWeight={FontStyle.fontMedium}
          fontSize={'medium'}
        >
          {label}
        </AText>
      )}
      <View style={{ width: '100%', justifyContent: 'center' }}>
        <TextInput
          ref={inputref}
          onLayout={() => {
            autofocus ? onFocus() : '';
          }}
          theme={{
            roundness: 10,
            colors: {
              text: textcolor
                ? textcolor
                : dark
                  ? 'rgba(255, 255, 255, 0.9)'
                  : 'rgba(0, 0, 0, 0.9)',
            },
          }}
          value={value}
          placeholderTextColor={placeholderTextColor ?? '#878787'}
          underlineColor={underlineColor ?? '#c7c7c7'}
          error={onerror}
          maxLength={maxLength ?? 70}
          autoFocus={autofocus}
          disabled={disabled ?? false}
          keyboardType={keyboardtype}
          onBlur={onblur}
          outlineColor={'#CFCFCF'}
          secureTextEntry={secureTextEntry}
          placeholder={placeholder}
          style={[
            {
              backgroundColor: backgroundColor
                ? backgroundColor
                : dark
                  ? 'rgba(255, 255, 255, 0)'
                  : 'rgba(0, 0, 0, 0)',
            },
            stylesTextInput,
          ]}
          mode={mode ? mode : 'outlined'}
          onChangeText={onchange}
        />
        {Icon && Icon}
      </View>
      {error && (
        <AText error fontSize={'xtrasmall'}>
          {error}
        </AText>
      )}
    </View>
  );
};

export default Textinputs;

const styles = StyleSheet.create({
  textViewInputStyle: {
    marginTop: 18,
    marginBottom: 2,
    backgroundColor: '#fff',
  },
});
