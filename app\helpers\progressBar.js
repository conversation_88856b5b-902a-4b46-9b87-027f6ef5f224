import { AText } from '_theme_components';
import { FontStyle } from '_utils';
import React, { useEffect, useRef } from 'react';
import { View, StyleSheet, Animated, Easing } from 'react-native';

const ProgressBar = ({ progress, duration = 1000 }) => {
  const animatedValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.timing(animatedValue, {
      toValue: progress, // Use progress as the target value
      duration,
      easing: Easing.linear,
      useNativeDriver: false,
    }).start();
  }, [progress, 15000, animatedValue]);

  // useEffect(() => {
  //   // Animated.loop(
  //   Animated.timing(animatedValue, {
  //     toValue: 1,
  //     duration: 4500, // Adjust the duration for a smoother animation
  //     easing: Easing.linear,
  //     useNativeDriver: false,
  //   }).start();
  // }, [progress, animatedValue]);

  const barWidth = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['0%', '100%'],
  });

  return (
    <View style={styles.containerStyle}>
      <View style={styles.progressBarContainer}>
        <Animated.View style={[styles.progressBar, { width: `${progress}%` }]} />
      </View>
      <AText styleText={styles.idText} fontWeight={FontStyle.fontBold} fontSize="small">
        {progress}%
      </AText>
    </View>
  );
};

const styles = StyleSheet.create({
  progressBarContainer: {
    height: 5,
    width: '90%',
    backgroundColor: '#e0e0e0',
    borderRadius: 10,
    overflow: 'hidden',
    marginBottom: 10,
    marginTop: 10,
  },
  progressBar: {
    height: '95%',
    borderRadius: 10,
    backgroundColor: '#4caf50', // Change the color as needed
  },
  containerStyle: {
    flexDirection: 'row',
    width: '100%',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  idText: {
    textAlignVertical: 'center',
    marginStart: 5,
    // height: '95%',
    width: '15%',
  },
});

export default ProgressBar;
