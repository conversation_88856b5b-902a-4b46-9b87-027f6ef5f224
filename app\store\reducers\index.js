import { combineReducers } from 'redux';
import login from './login';
import alert from './alert';
import user from './user';
import projectReducer from './projectReducer';
import theme from './theme';
import netInfo from './netInfo';
import study from './studyReducer';
import createStudy from './createStudy';
import serverReducer from './serverReducer';
import syncData from './syncDataReducer';
import questions from './question-answerReducer';
import reminders from './reminderReducer';
const MasterReducer = combineReducers({
  login,
  alert,
  user,
  projectReducer,
  theme,
  netInfo,
  study,
  createStudy,
  serverReducer,
  syncData,
  questions,
  reminders,
});

export default MasterReducer;
