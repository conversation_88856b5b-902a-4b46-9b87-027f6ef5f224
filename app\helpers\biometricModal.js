import { Image, Platform, SafeAreaView, StyleSheet, View, Modal } from 'react-native';
import React, { useEffect, useState } from 'react';
import { AButton, AText } from '_theme_components';
import { useTheme } from 'react-native-paper';
import { FontStyle, getFontSize, localimage, windowHeight, windowWidth } from '_utils';
import LinearGradient from 'react-native-linear-gradient';
import { getData, storeData } from './storage';
import { isEmpty } from './isEmpty';
import { biometricDetail } from '_provider';

const BiometricModal = ({}) => {
  const { colors } = useTheme();
  const [showModal, setViewModal] = useState(false);
  const [biometricType, setBiometricType] = useState('FaceID');

  useEffect(() => {
    const viewModal = async () => {
      const biometricData = await biometricDetail();
      setBiometricType(biometricData.title);
      if (biometricData.biometricSupport && isEmpty(biometricData.biometricEnabled)) {
        setViewModal(true);
      }
    };
    viewModal();
  }, []);

  const saveBiometric = async () => {
    let email = await getData('mailID');
    let password = await getData('passKey');

    let storeKey = {
      email: email,
      passKey: password,
    };
    await storeData('BiometricLogin', JSON.stringify(storeKey));
    setViewModal(false);
  };

  const dismissBiometric = async () => {
    await storeData('BiometricLogin', 'false');
    setViewModal(false);
  };

  return (
    <Modal
      transparent={true}
      visible={showModal}
      animationIn="slideInLeft"
      animationOut="slideOutRight"
    >
      <SafeAreaView style={styles.safecontainer}>
        <View style={[styles.modalConatiner, styles.container, { height: 310 }]}>
          <LinearGradient
            colors={['rgba(136, 98, 210,1)', 'rgba(133, 98, 201,1)']}
            start={{ x: 0.7, y: 0.55 }}
            end={{ x: 0.5, y: 1.0 }}
            locations={[0.5, 0.6]}
            style={[styles.modalContentConatiner, {}]}
          >
            <View style={[styles.viewStyle]}>
              <Image source={localimage.question} style={[styles.watchStyle]} />
              <View style={styles.contentStyle}>
                <AText
                  fontWeight={FontStyle.fontBold}
                  styleText={{ color: '#fff', paddingTop: 15 }}
                  fontSize={'title'}
                >
                  Login with {biometricType}
                </AText>
                <AText
                  fontWeight={FontStyle.fontMedium}
                  styleText={{ color: '#fff', paddingTop: 25, textTransform: 'none' }}
                  fontSize={'large'}
                >
                  Login using {biometricType} next time.
                </AText>
              </View>
            </View>

            <View style={styles.buttonContainer}>
              <View>
                <AButton
                  mode="text"
                  styleText={{ color: '#fff' }}
                  btnStyle={{ alignSelf: 'flex-start', padding: 20 }}
                  title={'Dismiss'}
                  onPress={() => {
                    dismissBiometric();
                  }}
                />
              </View>

              <View style={{ flexDirection: 'row' }}>
                <AButton
                  mode="contained"
                  bgColor={'#fff'}
                  btnStyle={{ ...styles.submitBtnStyle, width: windowWidth * 0.25 }}
                  styleText={{ color: colors.primary }}
                  title={'ok'}
                  onPress={() => {
                    saveBiometric();
                  }}
                />
              </View>
            </View>
          </LinearGradient>
        </View>
      </SafeAreaView>
    </Modal>
  );
};

export default BiometricModal;

const styles = StyleSheet.create({
  safecontainer: {
    flex: 1,
    // backgroundColor: 'red',
  },
  modalWrapper: {
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    flex: 1,
    width: '100%',
  },
  cancelBtnStyle: {
    position: 'absolute',
    zIndex: 1,
    alignSelf: 'flex-end',
  },
  viewStyle: {
    alignSelf: 'center',
    flexDirection: 'row',
    flex: 1,
    paddingVertical: 5,
    width: '95%',
  },
  container: {
    backgroundColor: 'rgba(255,255,255,0.1)',
    height: windowHeight * 0.3,
    alignSelf: 'center',
    width: '100%',
    // paddingVertical: 10,
    position: 'absolute',
    // paddingHorizontal: 25,
    alignSelf: 'center',
    bottom: 0,
    borderTopLeftRadius: 28,
    borderTopRightRadius: 28,
    shadowColor: '#000',
    // paddingBottom:24,
    padding: 1,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 1.84,
    elevation: Platform.OS == 'ios' ? 2 : 10,
  },
  modalContentConatiner: {
    width: '100%',
    height: '100%',
    // flex: 1,
    borderTopLeftRadius: 28,
    borderTopRightRadius: 28,
    alignSelf: 'center',
  },
  modalConatiner: {
    alignSelf: 'center',
    position: 'absolute',
    width: '100%',
    bottom: 0,
  },
  contentStyle: {
    marginHorizontal: 10,
    justifyContent: 'flex-start',
    flexDirection: 'column',
    width: '60%',
    marginTop: 20,
  },

  watchStyle: {
    marginTop: 15,
    resizeMode: 'contain',
    height: 170,
    width: 150,
  },
  buttonContainer: {
    justifyContent: 'space-between',
    width: '95%',
    alignSelf: 'center',
    flexDirection: 'row',
    position: 'absolute',
    bottom: 25,
  },
  serachTextinputStyle: {
    fontSize: getFontSize('large'),
    width: '100%',
    alignself: 'center',
    marginTop: 25,
    textAlign: 'left',
  },
  textViewInputStyle: {
    marginTop: 0,
    marginBottom: 0,
  },
  radiobuttonView: {
    width: '95%',
  },
  radioLineView: {
    flexDirection: 'row',
    paddingTop: 5,
    width: '100%',
    justifyContent: 'flex-start',
  },
  iconStyle: {
    alignSelf: 'center',
    marginEnd: 12,
    marginStart: 5,
  },
  scrollContainerStyle: {
    flexGrow: 1,
    paddingBottom: 45,
    // backgroundColor: 'red'
    // justifyContent: "center",
  },
  submitBtnStyle: { borderRadius: 35, padding: 20, marginEnd: 19 },
});
