import React, { useEffect, useState } from 'react';
import { View, Text } from 'react-native';
import { useTheme } from 'react-native-paper';
import { useDispatch, useSelector } from 'react-redux';
import { useIsFocused } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/Ionicons';

import {
  AButton,
  AText,
  Textinputs,
  AuthLoading,
  LinearGradientButton,
  MainLayout,
} from '_theme_components';
import {
  DEVELOPMENT_URL,
  FontStyle,
  PRODUCTION_URL,
  DEBUG_BUILD,
  INTERNET_ERROR,
  changeDevlopmentServer,
  VERSION,
} from '_utils';
import { validationSchema } from '_validation';
import { Formiks, getData, setURL } from '_helpers';
import { biometricAuthentication, biometricDetail } from '_provider';
import { LoginAction } from '_action';

import { ALERT_ERROR } from '../../store/reducers/alert';
import { CLEAR_LOGIN } from '../../store/actions/loginAction';
import { UserrNotFound, fieldsArray, invalidData, serverList } from './constants';
import { styles } from './styles';

const LoginScreen = ({ navigation }) => {
  const { colors, dark } = useTheme();
  const dispatch = useDispatch();
  const isFocused = useIsFocused();

  const { loading, loginFail } = useSelector((state) => state.login);
  const { netConnection } = useSelector((state) => state.netInfo);
  const [selectURL, setSelectURL] = useState({
    title: 'productionServer',
    ipAddresss: DEVELOPMENT_URL,
  });
  const [biometricDetails, setBiometriDetails] = useState({
    biometricSupport: false,
    biometricBtnTittle: 'Face ID',
  });
  const [loginInfo, setLoginInfo] = useState({
    email: '',
    password: '',
  });

  useEffect(() => {
    if (isFocused) {
      if (!DEBUG_BUILD) {
        (async () => {
          await setURL(PRODUCTION_URL);
        })();
      }
      biometricLogin();
    } else {
      dispatch({
        type: CLEAR_LOGIN,
      });
    }
  }, [isFocused]);

  const sendValues = ({ email, password }) => {
    setLoginInfo({
      email,
      password,
    });
    if (netConnection) dispatch(LoginAction(email, password));
    else dispatch({ type: ALERT_ERROR, payload: INTERNET_ERROR });
  };

  const biometricLogin = async () => {
    const biometricData = await biometricDetail();
    if (biometricData.biometricEnabled) {
      setBiometriDetails({
        biometricSupport: biometricData.biometricSupport,
        biometricBtnTittle: biometricData.title,
      });
    }
  };

  const authenticateBiometrics = async () => {
    if (!netConnection) {
      dispatch({ type: ALERT_ERROR, payload: INTERNET_ERROR });
      return;
    }
    const authenticated = await biometricAuthentication();
    try {
      if (authenticated) {
        const biometricLoginData = await getData('BiometricLogin');
        let bioMetricDetails = JSON.parse(biometricLoginData);
        dispatch(LoginAction(bioMetricDetails.email, bioMetricDetails.passKey));
      }
    } catch (errors) {
      console.error('Error authenticating biometrics:', errors);
    }
  };

  return (
    <>
      {loading ? <AuthLoading /> : null}
      <MainLayout bgColor={true}>
        <View style={styles.HeaderViewStyle}>
          <AText fontSize={'Heading'} fontWeight={FontStyle.fontBold}>
            Login
          </AText>
        </View>
        <View style={styles.containerStyle}>
          <Formiks
            validation={validationSchema}
            loginPage={true}
            fieldArray={fieldsArray}
            btntitle={'LOGIN'}
            btnContentStyle={styles.submitBtnContent}
            btnStyle={styles.submitBtn}
            fontSize={'medium'}
            viewStyle={styles.submitViewBtn}
            submitChange={(values) => sendValues(values)}
          />

          {biometricDetails.biometricSupport && (
            <View style={styles.submitViewBtn}>
              <LinearGradientButton
                btnStyle={styles.submitBtn}
                contentStyles={styles.submitBtnContent}
                title={biometricDetails.biometricBtnTittle}
                onPress={authenticateBiometrics}
              />
            </View>
          )}

          {loginFail && (
            <View
              style={
                loginFail === 'USerNotFound' ? styles.paswdErrorView : styles.wrngpaswdErrorView
              }
            >
              <View
                style={
                  loginFail === 'USerNotFound' ? styles.paswdErrortextview : styles.wrngpassTextView
                }
              >
                <AText
                  fontSize={'xtrasmall'}
                  fontWeight={FontStyle.fontBold}
                  styleText={styles.invalidUserTextstyle}
                >
                  {loginFail === 'USerNotFound' ? UserrNotFound(loginInfo.email) : invalidData}
                </AText>
              </View>
              <AButton
                onPress={() =>
                  loginFail === 'USerNotFound'
                    ? dispatch({ type: CLEAR_LOGIN })
                    : navigation.navigate('ResetPassword')
                }
                title={loginFail === 'USerNotFound' ? 'Try Again' : 'Forgot password?'}
                bgColor={loginFail === 'USerNotFound' ? '#EDDADE' : '#E2D0D4'}
                fontSize={'xtrasmall'}
                styleText={styles.frgtBtnStyle}
                btnStyle={{ width: loginFail === 'USerNotFound' ? '100%' : '50%' }}
              />
            </View>
          )}
          <View style={styles.paswrdRstbtnStyle}>
            <AButton
              onPress={() => navigation.navigate('ResetPassword')}
              mode="text"
              title="Password Reset"
              fontSize={'small'}
              styleText={{ color: colors.primary }}
            />
          </View>

          {DEBUG_BUILD && (
            <View style={styles.serverChangeView}>
              <Textinputs
                label={'IP Adresss'}
                placeholder="IP"
                stylesTextInput={styles.textInputStyle}
                value={selectURL.ipAddresss}
                onchange={(val) => {
                  val = val.trim();
                  setSelectURL({
                    ...selectURL,
                    ipAddresss: val,
                  });
                  changeDevlopmentServer(val);
                }}
              />
              {serverList?.map((item) => (
                <AButton
                  mode={'text'}
                  onPress={async () => {
                    await setURL(item.url);
                    setSelectURL({
                      ...selectURL,
                      title: item.value,
                    });
                  }}
                  btnStyle={styles.serverViewLine}
                  fontSize={'small'}
                  fontWeight={FontStyle.fontRegular}
                  styleText={{
                    color: selectURL.title === item.value ? colors.primary : 'black',
                    textTransform: 'capitalize',
                  }}
                  title={item.label}
                  icon={
                    <Icon
                      name={selectURL.title === item.value ? 'radio-button-on' : 'radio-button-off'}
                      color={selectURL.title === item.value ? colors.primary : 'black'}
                      style={styles.iconStyle}
                      size={25}
                    />
                  }
                />
              ))}
            </View>
          )}
        </View>
        <Text
          style={{
            position: 'absolute',
            zIndex: 1,
            bottom: 20,
            alignSelf: 'center',
            fontSize: 20,
            color: dark ? '#fff' : '#000',
          }}
          fontWeight={FontStyle.fontBold}
          fontSize={'medium'}
        >{`Version ${VERSION}`}</Text>
      </MainLayout>
    </>
  );
};

export default LoginScreen;
