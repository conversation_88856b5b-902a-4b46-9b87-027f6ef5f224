import {
  CATEGORY_LIST,
  PROJECT_BY_TYPE,
  PROJECT_LOADING,
  PROJECT_LOADING_FAILED,
  PROJECT_LOADING_FALSE,
} from '../actions/projctAction';

export const PROJECT_LIST = 'PROJECT_LIST';
const initialState = {
  loading: false,
  projectByType: [],
  projectList: [],
  categoryList: [],
  serverFailed: false,
};

export default (state = initialState, action) => {
  switch (action.type) {
    case PROJECT_LOADING:
      return {
        ...state,
        loading: true,
        serverFailed: false,
      };
    case PROJECT_LOADING_FALSE:
      return {
        ...state,
        loading: false,
        serverFailed: false,
      };
    case PROJECT_LOADING_FAILED:
      return {
        ...state,
        loading: false,
        serverFailed: true,
      };
    case CATEGORY_LIST:
      return {
        ...state,
        loading: false,
        categoryList: action.payload,
      };
    case PROJECT_BY_TYPE:
      return {
        ...state,
        projectByType: action.payload,
        serverFailed: false,
      };
    case PROJECT_LIST:
      return {
        ...state,
        loading: false,
        projectList: action.payload,
      };
    case 'USER_LOGOUT':
      return { ...initialState };
    default: {
      return state;
    }
  }
};
