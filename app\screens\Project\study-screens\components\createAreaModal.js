import { View, Keyboard } from 'react-native';
import React, { useState } from 'react';
import { AText, BottomModalUI, LinearGradientButton, Textinputs } from '_theme_components';
import { FontStyle, windowHeight, windowWidth } from '_utils';
import { styles } from './styles';
import { BottomModal } from '_components';
import { isEmpty } from '_helpers';
import { useTheme } from 'react-native-paper';

const createAreaModal = ({
  modalShow,
  closeModal,
  createArea,
  areaFieldValue,
  setAreaFieldValue,
  errorsField,
  setErrorsField,
}) => {
  const { dark } = useTheme();
  const [closeCanCreateModal, setCloseCanCreateModal] = useState(false);
  const [errorsFieldValue, setErrorsFieldValue] = useState('');
  const [checkCreateButton, setCheckCreateButton] = useState(false);

  const areaFieldSetup = async () => {
    Keyboard.dismiss();
    setErrorsField('');
    let regex = /[^a-zA-Z0-9&\s]/;
    if (regex.test(areaFieldValue)) {
      setErrorsField('Only Letters, Numbers, Ampersand and spaces are allowed');
      return;
    }

    if (isEmpty(areaFieldValue)) {
      setErrorsField('Please enter area name');
      return;
    }
    createArea(areaFieldValue);
    clearModal();
  };
  const clearModal = () => {
    Keyboard.dismiss();
    setAreaFieldValue('');
    setErrorsField('');
    setCloseCanCreateModal(false);
  };
  return (
    <BottomModalUI
      closeShow
      showScroll
      width={'100%'}
      ModalClose={false}
      closeModal={() => {
        Keyboard.dismiss(), setCloseCanCreateModal(true);
      }}
      height={windowHeight > 1000 ? windowHeight * 0.27 : windowHeight * 0.33}
      modalShow={modalShow}
    >
      <View style={[styles.modalContainer, { width: windowWidth * 0.87 }]}>
        <AText
          fontWeight={FontStyle.fontBold}
          styleText={{ color: dark ? '#fff' : '#3C4555' }}
          fontSize={'title'}
        >
          Create Area
        </AText>
        <LinearGradientButton
          disabled={isEmpty(areaFieldValue) || checkCreateButton}
          btnStyle={{ width: windowWidth * 0.3 }}
          contentStyles={{ paddingVertical: 10 }}
          title={'Create Area'}
          onPress={() => areaFieldSetup()}
        />
      </View>
      <View style={{ width: windowWidth * 0.87, alignSelf: 'center' }}>
        <Textinputs
          label={'Area name'}
          placeholder="e.g.Warehouse"
          value={areaFieldValue}
          onchange={(val) => {
            let regex = /[^a-zA-Z0-9&\s]/;
            if (regex.test(val)) {
              setErrorsField('Only Letters, Numbers, Ampersand and spaces are allowed');
              setCheckCreateButton(true);
            } else {
              setErrorsField('');
              setCheckCreateButton(false);
            }
            setAreaFieldValue(val);
          }}
          error={errorsField ?? ''}
        />
      </View>

      <BottomModal
        height={windowHeight > 1000 ? windowHeight * 0.2 : windowHeight * 0.3}
        modalShow={closeCanCreateModal}
        confirmPress={() => {
          clearModal();
          closeModal();
        }}
        cancelShow={() => setCloseCanCreateModal(false)}
        title={'Do you want to cancel?'}
        body={''}
        confirm={'okay'}
        reject={' cancel '}
      />
    </BottomModalUI>
  );
};

export default createAreaModal;
