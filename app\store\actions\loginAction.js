import jwt_decode from 'jwt-decode';
import {
  dropAllTables,
  isEmpty,
  clearStorage,
  removeStorage,
  storeData,
  executeFetch,
} from '_helpers';
import { ALERT_ERROR, ALERT_SUCCESS } from '../reducers/alert';
import { USER } from '../reducers/user';
import { PROJECT_LOADING, PROJECT_LOADING_FALSE } from './projctAction';
import crashlytics from '@react-native-firebase/crashlytics';

export const LoginAction = (email, password) => (dispatch) => {
  dispatch({
    type: LOGIN_LOADING,
  });
  let payload = {
    email: email,
    password: password,
  };
  executeFetch('users/login', 'postWithoutAuth', payload)
    .then(async (response) => {
      let data = response.data;
      if (response.status === 200) {
        if (!isEmpty(data)) {
          let decodedData = jwt_decode(data.token);
          let users = decodedData.user;
          storeData('token', data.token);
          storeData('userID', users._id);
          storeData('userName', users.firstname + ' ' + users.lastname);
          storeData('currentUser', JSON.stringify(users));
          storeData('mailID', email);
          storeData('passKey', password);
          crashlytics().setUserId(users._id);
          dispatch({
            type: LOGIN,
            payload: { token: data.token },
          });

          dispatch({
            type: USER,
            payload: users,
          });
        }
      } else {
        if (response.data.message === 'Password has been expired, Please check your email') {
          dispatch({ type: LOGIN_FAIL });
          dispatch({
            type: ALERT_ERROR,
            payload: response.data.message,
          });
        } else {
          var LoginError =
            !isEmpty(response.data) && response.data.message == 'User not found'
              ? 'USerNotFound'
              : 'InvalidLoginDetails';
          dispatch({
            type: ERROR_LOGIN,
            payload: LoginError,
          });
        }
      }
    })
    .catch((error) => {
      dispatch({
        type: LOGIN_FAIL,
      });
      dispatch({
        type: ALERT_ERROR,
        payload: 'Something went wrong. Please try again later.',
      });
    });
};
export const resetPasswordAction = (email, navigation) => (dispatch) => {
  dispatch({
    type: LOGIN_LOADING,
  });
  executeFetch('users/password/reset/request', 'postWithoutAuth', { email: email })
    .then(async (response) => {
      if (response.status === 200) {
        dispatch({
          type: RESETPASSWORD_SUCCESS,
        });
        navigation.navigate('ResetPasswordSuccess', { email: email });
      } else {
        dispatch({
          type: LOGIN_FAIL,
        });
        dispatch({
          type: ALERT_ERROR,
          payload:
            response.data.message ||
            response.data.data.message ||
            'Something went wrong. Please try again later.',
        });
      }
    })
    .catch((error) => {
      dispatch({
        type: LOGIN_FAIL,
      });
      dispatch({
        type: ALERT_ERROR,
        payload: 'Something went wrong. Please try again later.',
      });
    });
};

export const LogoutAction = (db) => async (dispatch) => {
  global.HEADERSELECT = 'projects';
  await removeStorage();
  await clearStorage();
  await dropAllTables(db);
  dispatch({
    type: 'USER_LOGOUT',
  });
  dispatch({
    type: LOGOUT_SUCCESS,
  });
};

export const recoverAction = (payload, navigation) => async (dispatch) => {
  dispatch({
    type: PROJECT_LOADING,
  });
  executeFetch(`sync/study/local`, 'POST', payload)
    .then(async (response) => {
      if (response.status === 200) {
        dispatch({
          type: PROJECT_LOADING_FALSE,
        });
        dispatch({
          type: ALERT_SUCCESS,
          payload: response.data.message ?? 'Data submitted successfully',
        });
        navigation.replace('HomeNav', { screen: 'Home' });
      } else {
        dispatch({
          type: PROJECT_LOADING_FALSE,
        });
        dispatch({
          type: ALERT_ERROR,
          payload: 'Something went wrong. Please try again later.',
        });
      }
    })
    .catch((error) => {
      dispatch({
        type: PROJECT_LOADING_FALSE,
      });
      dispatch({
        type: ALERT_ERROR,
        payload: 'Something went wrong. Please try again later.',
      });
    });
};

export const LOGIN_LOADING = 'LOGIN_LOADING';
export const LOGIN = 'LOGIN';
export const LOGIN_FAIL = 'LOGIN_FAIL';
export const CLEAR_LOGIN = 'CLEAR_LOGIN';
export const ERROR_LOGIN = 'ERROR_LOGIN';
export const LOGOUT_SUCCESS = 'LOGOUT_SUCCESS';
export const RESETPASSWORD_SUCCESS = 'RESETPASSWORD_SUCCESS';
