import React, { useState } from 'react';
import { Image, View } from 'react-native';
import { Card } from 'react-native-paper';
import { AText } from '_theme_components';
import { FontStyle, localimage } from '_utils';
import Icon from 'react-native-vector-icons/Ionicons';
import { isEmpty } from '_helpers';
import { styles } from './styles';
const ProjectCard = React.memo(({ data, navigateTo, url }) => {
  const [isError, setIsError] = useState(false);

  return (
    <Card onPress={navigateTo} style={styles.projectCardstyle}>
      <>
        <Card.Content style={styles.projectCardContainer}>
          <View style={styles.projectcontentContainer}>
            <AText
              styleText={{ textTransform: 'capitalize' }}
              fontSize={'large'}
              fontWeight={FontStyle.fontBold}
            >
              {data.name}
            </AText>
            <View style={styles.projectCardRow}>
              <View style={styles.projectCardElement}>
                <Icon
                  name="briefcase-outline"
                  style={styles.iconStyle}
                  color={'#878787'}
                  size={18}
                />
                <AText fontSize={'small'} lightGray fontWeight={FontStyle.fontBold}>
                  {data.customer_name}{' '}
                </AText>
              </View>
              <View style={styles.projectCardElement}>
                <Icon
                  name="location-outline"
                  style={styles.iconStyle}
                  color={'#878787'}
                  size={20}
                />
                <AText fontSize={'small'} lightGray fontWeight={FontStyle.fontBold}>
                  {Math.round(data.assignedlocations)} Location
                </AText>
              </View>
            </View>
          </View>
          {!isEmpty(data.logo) && !isError && !isEmpty(url) ? (
            <Image
              source={{ uri: url + data.logo }}
              onError={({ nativeEvent: { error } }) => {
                setIsError(true);
              }}
              style={styles.imageStyle}
            />
          ) : (
            <Image source={localimage.logo} style={styles.imageStyle} />
          )}
        </Card.Content>
      </>
    </Card>
  );
});

export default ProjectCard;
