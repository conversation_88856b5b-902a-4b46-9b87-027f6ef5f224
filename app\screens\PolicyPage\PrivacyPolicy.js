import { StyleSheet, View } from 'react-native';
import React from 'react';
import { AText, MainLayout } from '_theme_components';
import { FontStyle } from '_utils';
import { Paragraph } from 'react-native-paper';

const PrivacyPolicyScreen = ({ navigation }) => {
  return (
    <MainLayout back navigation={navigation} headerShow>
      <View style={styles.contentContainer}>
        <AText fontSize={'title'} fontWeight={FontStyle.fontBold}>
          Privacy Policy
        </AText>
        <Paragraph style={styles.termsConditionsParagraph}>
          ReThink Productivity Consulting Ltd built the ReTime app as a Commercial app. This SERVICE
          is provided by ReThink Productivity Consulting Ltd and is intended for use as is.{`\n`}
          {`\n`}
          This page is used to inform ReTime app users regarding our policies with the collection,
          use, and disclosure of Personal Information if anyone decided to use our Service.{`\n`}
          {`\n`}
          If you choose to use our Service, then you agree to the collection and use of information
          in relation to this policy. The Personal Information that we collect is used for providing
          and improving the Service. We will not use or share your information with anyone except as
          described in this Privacy Policy.{`\n`}
          {`\n`}
          The terms used in this Privacy Policy have the same meanings as in our Terms of service,
          which is accessible at ReTime unless otherwise defined in this Privacy Policy.
        </Paragraph>
        <AText fontSize={'medium'} fontWeight={FontStyle.fontBold}>
          Information Collection and Use
        </AText>
        <Paragraph style={styles.termsConditionsParagraph}>
          For a better experience, while using our Service, we may require you to provide us with
          certain personally identifiable information. The information that we request will be
          retained by us and used as described in this privacy policy.
        </Paragraph>
        <AText fontSize={'medium'} fontWeight={FontStyle.fontBold}>
          Log Data
        </AText>
        <Paragraph style={styles.termsConditionsParagraph}>
          We want to inform you that whenever you use our Service, in a case of an error in the app
          we collect data and information on your phone/tablet called Log Data. This Log Data may
          include information such as your device Internet Protocol (“IP”) address, device name,
          operating system version, the configuration of the app when utilizing our Service, the
          time and date of your use of the Service, and other statistics.
        </Paragraph>
        <AText fontSize={'medium'} fontWeight={FontStyle.fontBold}>
          Cookies
        </AText>
        <Paragraph style={styles.termsConditionsParagraph}>
          Cookies are files with a small amount of data that are commonly used as anonymous unique
          identifiers. These are sent to your browser from the websites that you visit and are
          stored on your device's internal memory.{`\n`}
          {`\n`}
          This Service does not use these “cookies” explicitly. However, the app may use third party
          code and libraries that use “cookies” to collect information and improve their services.
          You have the option to either accept or refuse these cookies and know when a cookie is
          being sent to your device. If you choose to refuse our cookies, you may not be able to use
          some portions of this Service.
        </Paragraph>
        <AText fontSize={'medium'} fontWeight={FontStyle.fontBold}>
          Service Providers
        </AText>
        <Paragraph style={styles.termsConditionsParagraph}>
          We may employ third-party companies and individuals due to the following reasons:{`\n`}
          {`\n`}
          To facilitate our Service;{`\n`}
          To provide the Service on our behalf;{`\n`}
          To perform Service-related services; or{`\n`}
          To assist us in analyzing how our Service is used.{`\n`}
          {`\n`}
          We want to inform users of this Service that these third parties have access to your
          Personal Information. The reason is to perform the tasks assigned to them on our behalf.
          However, they are obligated not to disclose or use the information for any other purpose.
        </Paragraph>
        <AText fontSize={'medium'} fontWeight={FontStyle.fontBold}>
          Security
        </AText>
        <Paragraph style={styles.termsConditionsParagraph}>
          We value your trust in providing us your Personal Information, thus we are striving to use
          commercially acceptable means of protecting it. But remember that no method of
          transmission over the internet, or method of electronic storage is 100% secure and
          reliable, and we cannot guarantee its absolute security.
        </Paragraph>
        <AText fontSize={'medium'} fontWeight={FontStyle.fontBold}>
          Changes to This Privacy Policy
        </AText>
        <Paragraph style={styles.termsConditionsParagraph}>
          We may update our Privacy Policy from time to time. Thus, you are advised to review this
          page periodically for any changes. We will notify you of any changes by posting the new
          Privacy Policy on this page. These changes are effective immediately after they are posted
          on this page.
        </Paragraph>
        <AText fontSize={'medium'} fontWeight={FontStyle.fontBold}>
          Contact Us
        </AText>
        <Paragraph style={styles.termsConditionsParagraph}>
          If you have any questions or suggestions about our Privacy Policy, do not hesitate to
          contact us.
        </Paragraph>
      </View>
    </MainLayout>
  );
};

export default PrivacyPolicyScreen;

const styles = StyleSheet.create({
  contentContainer: {
    width: '90%',
    marginTop: 20,
    alignSelf: 'center',
  },
  termsConditionsParagraph: {
    marginBottom: 30,
  },
});
