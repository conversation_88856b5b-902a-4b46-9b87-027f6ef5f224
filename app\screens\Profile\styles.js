import { StyleSheet } from 'react-native';

export const styles = StyleSheet.create({
  projectCardstyle: {
    width: '97%',
    marginTop: 15,
    alignSelf: 'center',
    shadowColor: 'rgba(0,0,0,0.5)',
    shadowOffset: {
      width: 0,
      height: 5,
    },
    borderRadius: 12,
    shadowOpacity: 0.3,
    shadowRadius: 1.0,
    elevation: Platform.OS == 'ios' ? 0 : 5,
    marginVertical: 12,
    paddingHorizontal: 15,
    paddingVertical: 12,
  },

  saveChangeButton: {
    alignSelf: 'flex-end',
    // alignItems: 'center',
    marginTop: 30,
    marginBottom: 7,
  },
  HeaderContainer: {
    alignItems: 'center',
    alignSelf: 'center',
    width: '95%',
  },
  textinputView: { marginTop: 40 },
  saveChangeBtnStyle: {
    borderRadius: 40,
  },
  btnContentStyle: {
    paddingVertical: 20,
  },

  // profile page styling

  scrollViewContent: {
    padding: 15,
    width: '100%',
    flexGrow: 1,
    alignSelf: 'center',
  },
  profileProjectCardstyle: {
    width: '95%',
    marginTop: 15,
    alignSelf: 'center',
    shadowColor: 'rgba(0,0,0,0.5)',
    shadowOffset: {
      width: 0,
      height: 5,
    },
    borderRadius: 12,
    shadowOpacity: 0.3,
    shadowRadius: 1.0,
    elevation: Platform.OS == 'ios' ? 0 : 5,
    marginVertical: 12,
    paddingVertical: 20,
  },
  buttonStyle: {
    padding: 10,
    flexDirection: 'row-reverse',
    alignItems: 'center',
    width: '95%',
    justifyContent: 'space-between',
  },
  logoutButtonStyle: {
    padding: 10,
    marginTop: 15,
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
    width: '95%',
    borderTopWidth: 1,
    borderColor: '#CFCFCF',
  },
  Viewcontainer: {
    width: '100%',
    marginTop: 10,
    flex: 1,
    marginBottom: 10,
  },
  saveBtnStyle: {
    borderRadius: 40,
  },
});
