import { Safe<PERSON>reaView, ScrollView, StyleSheet, View } from 'react-native';
import React, { useEffect } from 'react';
import { Appbar, useTheme } from 'react-native-paper';
import Octicons from 'react-native-vector-icons/Octicons';
import FeatherIcon from 'react-native-vector-icons/Feather';
import { useScrollToTop } from '@react-navigation/native';

import { AText, LinearGradientButton, IconButton } from '_theme_components';
import { FontStyle, windowWidth } from '_utils';
import { useDebounce } from '_helpers';

const SumbitHeader = ({
  navigation,
  Title,
  SubTitle,
  children,
  ShowSave,
  showSmallTitle,
  SubmitDataCard,
  bgColor,
  submiteModal,
  savechanges,
  deleteData,
  isFocused,
  allowStudyNameEdit,
  showEditNameModal,
  submitDisabled,
}) => {
  const { colors, dark } = useTheme();
  const ref = React.useRef(null);
  const { debounce } = useDebounce();

  useEffect(() => {
    if (isFocused) {
      try {
        ref.current?.scrollTo({
          y: 0,
          animated: true,
        });
      } catch (error) {
        console.error('Error in scrollTo function:', error);
      }
    }
  }, [isFocused]);
  useScrollToTop(ref);

  const scrollComponent = () => {
    return (
      <ScrollView
        showsVerticalScrollIndicator={false}
        showsHorizontalScrollIndicator={false}
        style={{
          padding: 15,
          flexGrow: 1,
          backgroundColor: dark
            ? 'rgba(0, 0, 0, 0.6)'
            : submiteModal
              ? 'rgba(255, 255, 255, 0.1)'
              : 'rgba(255, 255, 255, 0.9)',
        }}
        contentContainerStyle={styles.contentContainerstyle}
        ref={ref}
      >
        {children}
      </ScrollView>
    );
  };

  return (
    <>
      {submiteModal ? (
        <View style={{ flex: 1, backgroundColor: colors.onSurface }}>
          <View
            style={[
              styles.header,
              { backgroundColor: (bgColor ?? dark) ? 'rgba(0,0,0,0.7)' : '#fff' },
            ]}
          >
            {showSmallTitle ? (
              <View>
                <AText
                  fontWeight={FontStyle.fontBold}
                  styleText={{ color: '#3C4555' }}
                  fontSize={'title'}
                >
                  {Title}
                </AText>
                <View style={{ flexDirection: 'row' }}>
                  <AText
                    fontWeight={FontStyle.fontBold}
                    lightGray
                    styleText={{ paddingRight: 10, paddingTop: 15 }}
                    fontSize={'small'}
                  >
                    {SubTitle}
                  </AText>
                  {allowStudyNameEdit ? (
                    <IconButton
                      icon={<FeatherIcon name="edit-3" color={colors.primary} size={22} />}
                      btnStyle={styles.editbuttonStyle}
                      onPress={() => {
                        showEditNameModal(false);
                      }}
                    />
                  ) : null}
                </View>
              </View>
            ) : null}

            {ShowSave ? (
              <LinearGradientButton
                disabled={submitDisabled}
                btnStyle={{ width: windowWidth * 0.25 }}
                contentStyles={{ paddingVertical: 15 }}
                onPress={SubmitDataCard}
                title={'Submit Data'}
                fontSize={'small'}
              />
            ) : null}
          </View>
          {scrollComponent()}
        </View>
      ) : (
        <View style={{ flex: 1, paddingTop: 15, backgroundColor: colors.onSurface }}>
          <SafeAreaView style={{ flex: 1, paddingBottom: Platform.OS === 'ios' ? 100 : 0 }}>
            <View style={styles.headerStyle}>
              <Appbar.BackAction
                size={30}
                color={dark ? '#fff' : '#000'}
                style={{ marginEnd: 15, borderRadius: 0 }}
                onPress={() => debounce(() => navigation.goBack())}
              />
              <View style={styles.buttonContainer}>
                <IconButton
                  icon={
                    <Octicons
                      name="trash"
                      color={colors.primary}
                      style={styles.iconStyle}
                      size={22}
                    />
                  }
                  btnStyle={styles.iconBtnStyle}
                  bgColor={'#fff'}
                  onPress={() => {
                    deleteData();
                  }}
                />
                <View style={{ width: windowWidth * 0.25, marginStart: 15 }}>
                  <LinearGradientButton
                    disabled={false}
                    btnStyle={{ width: windowWidth * 0.25 }}
                    contentStyles={{ paddingVertical: 13 }}
                    onPress={() => {
                      savechanges();
                    }}
                    title={'Save Changes'}
                    fontSize={'small'}
                  />
                </View>
              </View>
            </View>
            <View style={styles.headercontentContainer}>
              <AText
                fontWeight={FontStyle.fontBold}
                styleText={{ color: '#777778' }}
                fontSize={'title'}
              >
                {Title ?? ''}
              </AText>
              <AText fontWeight={FontStyle.fontBold} fontSize={'medium'}>
                {SubTitle}
              </AText>
            </View>
            {scrollComponent()}
          </SafeAreaView>
        </View>
      )}
    </>
  );
};

export default SumbitHeader;

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    width: '100%',
    alignItems: 'center',
    alignSelf: 'center',
    justifyContent: 'space-between',
    padding: 10,
    paddingHorizontal: 40,
    paddingVertical: 20,
  },
  iconStyle: {
    color: 'red',
  },
  headercontentContainer: {
    marginTop: 4,
    padding: 10,
    alignSelf: 'center',
    width: '100%',
  },
  headerStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '95%',
    alignSelf: 'center',
    alignItems: 'center',
  },
  headercontentContainer: {
    marginTop: 4,
    marginBottom: 10,
    padding: 10,
    alignSelf: 'center',
    width: '79%',
  },
  contentContainerstyle: {
    padding: 15,
    flexGrow: 1,
    alignItems: 'center',
    paddingBottom: 150,
  },
  buttonContainer: {
    flexDirection: 'row',
    marginEnd: 20,
  },
  editbuttonStyle: {
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
    top: 3,
    alignSelf: 'center',
    height: 35,
    width: 35,
  },
  iconBtnStyle: {
    marginRight: 25,
    padding: 10,
    height: 50,
    width: 55,
    borderRadius: 100,
  },
});
