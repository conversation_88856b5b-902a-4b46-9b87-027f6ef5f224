import { Pressable, StyleSheet, Text, View } from 'react-native';
import React from 'react';
import { useTheme } from 'react-native-paper';
import LinearGradient from 'react-native-linear-gradient';
import { FontStyle, LINEAR_GRADIENT_RATING_BUTTON, getFontSize } from '_utils';
import { AText } from '..';

const ratingButton = ({
  onPress,
  selected,
  ratingContainerStyle,
  selectedRateContainerStyle,
  item,
  styleText,
  disableBtn,
}) => {
  const { colors, dark } = useTheme();
  const getButtonStyle = () => {
    return StyleSheet.create({
      textStyle: {
        textAlign: 'center',
        fontFamily: FontStyle.fontMedium,
        textAlignVertical: 'center',
        color: dark || selected ? '#fff' : '#000',
        fontSize: getFontSize('medium'),
        textTransform: 'uppercase',
      },
    });
  };
  return (
    <Pressable
      activeOpacity={0.9}
      onPress={onPress}
      disabled={disableBtn ?? false}
      style={[styles.ratecontainer, ratingContainerStyle]}
    >
      <LinearGradient
        colors={
          selected
            ? LINEAR_GRADIENT_RATING_BUTTON
            : dark
              ? [colors.onSurface, colors.onSurface]
              : ['#fff', '#fff']
        }
        start={{ x: 0.1, y: 0.3 }}
        end={{ x: 0.5, y: 0.7 }}
        style={[styles.slectedRatecontainer, selectedRateContainerStyle]}
      >
        <Text style={[getButtonStyle().textStyle, styleText]}>{item}</Text>
      </LinearGradient>
    </Pressable>
  );
};

export default ratingButton;

const styles = StyleSheet.create({
  ratecontainer: {
    justifyContent: 'center',
    marginHorizontal: 7,
    marginVertical: 10,
    alignItems: 'center',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
    // elevation: 2,
  },
  slectedRatecontainer: {
    justifyContent: 'center',
    width: '100%',
    height: '100%',
    alignSelf: 'center',
    alignItems: 'center',
    borderRadius: 12,
    borderWidth: 3,
  },
  rateContainStyle: {
    width: '99%',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    flexWrap: 'wrap',
    paddingBottom: 55,
  },
});
