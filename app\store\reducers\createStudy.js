import { isEmpty } from '_helpers';

export const CREATESTUDY_LOADING_FALSE = 'CREATESTUDY_LOADING_FALSE';
export const CREATESTUDY_LOADING = 'CREATESTUDY_LOADING';
export const CREATESTUDY_CAMERA_LOADING = 'CREATESTUDY_CAMERA_LOADING';

const initialState = {
  loading: false,
  cameraLoading: false,
  studyID: '',
  projectSelect: {},
  locationSelect: {},
  studyData: {},
  roleSelect: {},
  areaSelect: {},
  taskSelect: {},
  elementSelect: {},
  obsrvationData: [],
  saveNote: '',
  saveImage: '',
  saveImageLocal: '',
  localId: '',
  ECclick: false,
  reRate: [100],
  storePhoto: [],
  obsStart: false,
};

export default (state = initialState, action) => {
  switch (action.type) {
    case CREATESTUDY_LOADING:
      return {
        ...state,
        loading: true,
      };
    case CREATESTUDY_CAMERA_LOADING:
      return {
        ...state,
        cameraLoading: true,
      };
    case CREATESTUDY_LOADING_FALSE:
      return {
        ...state,
        loading: false,
        cameraLoading: false,
      };
    case 'ELEMENT_SCREEN_OPENED':
      return {
        ...state,
        obsStart: true,
      };
    case 'STUDY_DATA_STORE':
      return {
        ...state,
        loading: false,
        cameraLoading: false,
        studyData: action.payload,
        studyID: action.payload.StudyID,
      };
    case 'STORE_PROJECT':
      return {
        ...state,
        loading: false,
        cameraLoading: false,
        projectSelect: action.payload,
      };
    case 'STORE_LOCATION':
      return {
        ...state,
        loading: false,
        cameraLoading: false,
        locationSelect: action.payload,
      };
    case 'STORE_ROLE':
      return {
        ...state,
        loading: false,
        cameraLoading: false,
        roleSelect: action.payload,
      };
    case 'STORE_AREA':
      return {
        ...state,
        loading: false,
        cameraLoading: false,
        areaSelect: action.payload,
        ECclick: false,
      };
    case 'STORE_TASK':
      return {
        ...state,
        loading: false,
        cameraLoading: false,
        taskSelect: action.payload,
      };
    case 'STORE_ELEMENT':
      return {
        ...state,
        loading: false,
        cameraLoading: false,
        elementSelect: action.payload.element,
        ECclick: action.payload.ECclick,
      };
    case 'STORE_RATING':
      return {
        ...state,
        loading: false,
        cameraLoading: false,
        saveNote: action.payload.notes,
      };
    case 'STORE_FREQUENCY':
      return {
        ...state,
        loading: false,
        cameraLoading: false,
      };
    case 'STORE_RATE_ELEMENT_FREQ':
      return {
        ...state,
        loading: false,
        cameraLoading: false,
        elementSelect: action.payload.element,
        ECclick: action.payload.ECclick,
      };
    case 'STORE_OBSERVATION':
      return {
        ...state,
        loading: false,
        cameraLoading: false,
        obsrvationData: action.payload,
        saveNote: '',
        saveImage: '',
        saveImageLocal: '',
        localId: '',
        reRate: [100],
      };
    case 'SAVE_OBSERVATION':
      return {
        ...state,
        loading: false,
        cameraLoading: false,
        obsrvationData: action.payload,
        reRate: [100],
      };
    case 'SAVE_RERATE':
      return {
        ...state,
        loading: false,
        cameraLoading: false,
        reRate: action.payload,
      };
    case 'CLEAR_UPTO_ELEMENT':
      return {
        ...state,
        loading: false,
        cameraLoading: false,
        elementSelect: {},
        reRate: [100],
      };
    case 'CLEAR_UPTO_TASK':
      return {
        ...state,
        loading: false,
        cameraLoading: false,
        elementSelect: {},
        taskSelect: {},
        reRate: [100],
      };
    case 'CLEAR_UPTO_AREA':
      return {
        ...state,
        loading: false,
        cameraLoading: false,
        elementSelect: {},
        taskSelect: {},
        areaSelect: {},
        reRate: [100],
      };
    case 'CANCELING_STUDY':
      return {
        ...state,
        loading: false,
        cameraLoading: false,
        roleSelect: {},
        areaSelect: {},
        taskSelect: {},
        elementSelect: {},
        obsrvationData: [],
        saveNote: '',
        studyID: '',
        projectSelect: {},
        obsStart: false,
        saveImage: '',
        saveImageLocal: '',
        studyData: {},
        reRate: [100],
      };
    case 'CLEAR_STUDY':
      return {
        ...state,
        loading: false,
        cameraLoading: false,
        roleSelect: {},
        areaSelect: {},
        taskSelect: {},
        elementSelect: {},
        reRate: [100],
        obsrvationData: [],
        saveNote: '',
        obsStart: false,
        saveImage: '',
        saveImageLocal: '',
        reRate: [100],
      };
    case 'CLEAR_PROJECT':
      return {
        ...state,
      };
    case 'SAVE_NOTE':
      return {
        ...state,
        loading: false,
        cameraLoading: false,
        saveNote: action.payload,
      };
    case 'CLEAR_IMAGE':
      return {
        ...state,
        loading: false,
        cameraLoading: false,
        saveImage: '',
        saveImageLocal: '',
      };
    case 'SAVE_IMAGE':
      return {
        ...state,
        loading: false,
        saveImage: [...state.saveImage, action.payload.path],
        localId: action.payload.localNo,
        cameraLoading: false,
      };
    case 'SAVE_IMAGE_LOCAL':
      return {
        ...state,
        loading: false,
        cameraLoading: false,
        saveImageLocal: [...state.saveImageLocal, action.payload],
      };
    case 'USER_LOGOUT':
      return { ...initialState };
    default: {
      return state;
    }
  }
};
