import {
  STUDY_LIST,
  STUDY_LOADING,
  STUDY_LOADING_FALSE,
  STUDY_SUBMITTED,
  STUDY_SUBMITTED_FAILED,
} from '../actions/studyAction';

const initialState = {
  loading: false,
  studyList: [],
  studyData: [],
  studySumbitted: false,
  studySubmittedFailed: false,
  noOfStudySynced: [],
  noOfStudyNotSynced: [],
  syncedStudyIDs: [],
  syncingStudyDetail: [],
  dataSyncCompleted: false,
  studySumbitStarted: false,
  studyCount: 0,
};

export default (state = initialState, action) => {
  switch (action.type) {
    case STUDY_LOADING:
      return {
        ...state,
        loading: true,
      };
    case STUDY_LOADING_FALSE:
      return {
        ...state,
        loading: false,
      };
    case STUDY_LIST:
      return {
        ...state,
        loading: false,
        projectList: action.payload,
      };
    case STUDY_SUBMITTED:
      return {
        ...state,
        loading: false,
        studySumbitted: true,
        studySubmittedFailed: false,
        studySumbitStarted: true,
        noOfStudySynced: [...state.noOfStudySynced, action.payload.studyID],
        syncedStudyIDs: [...state.syncedStudyIDs, action.payload.syncedStudyID],
      };
    case STUDY_SUBMITTED_FAILED:
      return {
        ...state,
        loading: false,
        // studySumbitted: false,
        studySubmittedFailed: true,
        noOfStudyNotSynced: [...state.noOfStudyNotSynced, ...action.payload],
      };
    case 'STUDY_COUNT':
      return {
        ...state,
        studyCount: action.payload,
      };
    case 'RESET_SUBMITTED':
      return {
        ...state,
        loading: false,
        studyCount: 0,
        studySumbitStarted: false,
        studySumbitted: false,
        studySubmittedFailed: false,
        noOfStudyNotSynced: [],
        noOfStudySynced: [],
        dataSyncCompleted: false,
      };
    case 'SYNCING_STUDIES':
      return {
        ...state,
        loading: false,
        studySumbitStarted: false,
        syncingStudyDetail: action.payload,
      };
    case 'CLEAR_SYNCING_STUDIES':
      return {
        ...state,
        syncingStudyDetail: [],
        syncedStudyIDs: [],
        dataSyncCompleted: true,
      };
    case 'USER_LOGOUT':
      return { ...initialState };
    default: {
      return state;
    }
  }
};
