import { FontStyle } from '_utils';
import { AButton, AText } from '_theme_components';
import React from 'react';
import { StyleSheet, View } from 'react-native';
import { useTheme } from 'react-native-paper';

const HeaderSubmitPage = ({ label, order, setOrder, showButton }) => {
  const { colors, dark } = useTheme();

  return (
    <View
      style={[
        styles.headerstyle,
        {
          width: '97%',
          justifyContent: label.includes('Summary') ? 'center' : 'space-between',
          marginTop: label.includes('Frequency') ? 35 : 0,
        },
      ]}
    >
      <AText
        styleText={{ color: '#D0D0D0', textTransform: 'capitalize' }}
        fontSize={'title'}
        fontWeight={FontStyle.fontBold}
      >
        {label}
      </AText>
      {showButton && (
        <AButton
          mode="contained"
          bgColor={'#ffffff'}
          btnStyle={{ borderRadius: 30 }}
          styleText={{ color: 'black' }}
          title={`Show ${order === 'highest' ? 'Lowest' : 'Highest'} `}
          fontSize={'small'}
          onPress={() => {
            setOrder(order === 'highest' ? 'lowest' : 'highest');
          }}
        />
      )}
    </View>
  );
};

export default HeaderSubmitPage;

const styles = StyleSheet.create({
  headerstyle: {
    flexDirection: 'row',
    width: '85%',
    alignSelf: 'flex-start',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 5,
    marginBottom: 25,
  },
});
