import 'react-native-gesture-handler';
import React from 'react';
import { useSelector } from 'react-redux';
import { useTheme } from 'react-native-paper';
import AppNaivgation from './app-navigation';
import AuthNaivgation from './auth-navigation';

const Navigation = ({ navigation }) => {
    const { colors } = useTheme();
    const Login = useSelector(state => state.login.login);

    return <>{Login ? <AppNaivgation /> : <AuthNaivgation />}</>;
};

export default Navigation;
