const initialState = {
  loading: false,
  studyStart: false,
  questionList: [],
  end_of_study: [],
  show_on_days: [],
  show_on_task_elements: [],
  time_in_study: [],
  show_byTime: [],
  answer: [],
  showonelements_reminder: [],
  reminderList: [],
};

export default (state = initialState, action) => {
  switch (action.type) {
    case 'QUESTION_LOADING':
      return {
        ...state,
        loading: true,
      };
    case 'QUESTION_LOADING_FALSE':
      return {
        ...state,
        loading: false,
      };
    case 'SAVE_QUESTION_LIST':
      return {
        ...state,
        loading: false,
        studyStart: true,
        end_of_study: action.payload.end_of_study,
        show_on_task_elements: action.payload.show_on_task_elements,
        show_byTime: action.payload.show_byTime,
      };
    case 'SAVE_TIME_IN_STUDY_QUES':
      return {
        ...state,
        loading: false,
        show_byTime: action.payload,
      };
    case 'SAVE_SUBMITTED_QUESTION':
      return {
        ...state,
        loading: false,
        end_of_study: action.payload,
      };
    case 'STUDY_STOP':
      return {
        ...state,
        loading: false,
        studyStart: false,
        questionList: [],
      };
    case 'USER_LOGOUT':
      return { ...initialState };
    default: {
      return state;
    }
  }
};
