import { View, Keyboard } from 'react-native';
import React, { useState } from 'react';
import { useTheme } from 'react-native-paper';
import Icon from 'react-native-vector-icons/Ionicons';
import { styles } from './styles';
import FIcon from 'react-native-vector-icons/FontAwesome5';

import {
  AButton,
  AText,
  BottomModalUI,
  IconButton,
  LinearGradientButton,
  Textinputs,
} from '_theme_components';
import { FontStyle, windowHeight, windowWidth } from '_utils';
import { BottomModal, ModalSelect } from '_components';
import { CustomSwitch, isEmpty } from '_helpers';

const deftRatngOption = [
  { id: 1, label: 'Not Rated', value: 'notRated', rating: true },
  { id: 2, label: '100', value: '100', rating: true },
  { id: 3, label: 'Field User Input', value: 'fileUserInput', rating: false },
];

const createElementModal = ({
  modalShow,
  allCategory,
  elementList,
  createElement,
  closeModal,
  elementViewNext,
  setElementViewNext,
  closeCanCreateModal,
  setCloseCanCreateModal,
  elementValue,
  setElementValue,
  errorValue,
  setErrorValue,
}) => {
  const { colors, dark } = useTheme();

  const elementCreationCheck = async () => {
    Keyboard.dismiss();
    const { elementName, category, orderAfter, rating, contingencyAllowance, relaxationAllowance } =
      elementValue;
    const regex = /[^a-zA-Z0-9&\s]/;

    var errorValues = {};

    if (
      !elementViewNext &&
      !regex.test(elementName) &&
      !isEmpty(elementName) &&
      !isEmpty(contingencyAllowance) &&
      !isEmpty(relaxationAllowance) &&
      !isEmpty(category)
    ) {
      setElementViewNext(true);
    } else if (
      regex.test(elementName) ||
      isEmpty(elementName) ||
      isEmpty(category) ||
      isEmpty(contingencyAllowance) ||
      isEmpty(relaxationAllowance) ||
      (elementViewNext && isEmpty(rating))
    ) {
      errorValues.elementName = regex.test(elementName)
        ? 'Only Letters, Numbers, Ampersand, and spaces are allowed'
        : isEmpty(elementName)
          ? 'Please enter element name'
          : '';
      errorValues.category = isEmpty(category) ? 'Please select category' : '';
      errorValues.orderAfter = isEmpty(orderAfter) ? 'Please select order After' : '';
      errorValues.contingencyAllowance = isEmpty(contingencyAllowance)
        ? 'Please enter contingency allowance'
        : '';
      errorValues.relaxationAllowance = isEmpty(relaxationAllowance)
        ? 'Please enter relaxation allowance'
        : '';
      errorValues.rating = elementViewNext && isEmpty(rating) ? 'Please enter rating' : '';
      setErrorValue({ ...errorValues });
    } else {
      createElement(elementValue);
      clearModal();
    }
  };
  const clearModal = () => {
    setErrorValue({
      elementName: '',
      category: '',
      orderAfter: '',
      rating: '',
      contingencyAllowance: '',
      relaxationAllowance: '',
    });
    setElementViewNext(false);
    setElementValue({
      elementName: '',
      category: '',
      ordeAfterrName: 'Add last',
      orderAfter: 'addLast',
      rating: '',
      categoryModalShow: false,
      elementModalShow: false,
      count: true,
      contingencyAllowance: '',
      relaxationAllowance: '',
    });
    setCloseCanCreateModal(false);
  };
  return (
    <BottomModalUI
      closeShow
      showScroll
      ModalClose={false}
      width={'100%'}
      height={windowHeight > 1000 ? windowHeight * 0.45 : windowHeight * 0.65}
      closeModal={() => {
        Keyboard.dismiss(), setCloseCanCreateModal(true);
      }}
      modalShow={modalShow}
    >
      <View style={styles.modalContainer}>
        <AText fontWeight={FontStyle.fontBold} color={'#3C4555'} fontSize={'homeTitle'}>
          Create Element
        </AText>
        {!elementViewNext ? (
          <AButton
            mode="text"
            styleText={{ color: colors.primary }}
            fontSize={'small'}
            title={'Next Step'}
            onPress={() => elementCreationCheck()}
          />
        ) : (
          <View style={styles.btnGrp}>
            <IconButton
              bgColor={colors.secondary}
              icon={<Icon name="arrow-back-outline" color={colors.primary} size={18} />}
              btnStyle={styles.backBtnStyle}
              onPress={() => setElementViewNext(false)}
            />
            <LinearGradientButton
              disabled={false}
              btnStyle={{ width: windowWidth * 0.34 }}
              contentStyles={{ paddingVertical: 10 }}
              title={'Create element'}
              onPress={() => elementCreationCheck()}
            />
          </View>
        )}
      </View>

      {!elementViewNext ? (
        <View style={{ width: windowWidth * 0.87, alignSelf: 'center' }}>
          <Textinputs
            label={'Element name'}
            placeholder="e.g. Make hot Drink"
            value={elementValue.elementName}
            onerror={false}
            onchange={(val) => {
              setElementValue({
                ...elementValue,
                elementName: val,
              });
            }}
            error={errorValue.elementName ? errorValue.elementName : ''}
          />

          <ModalSelect
            heading={'select category'}
            modalOpen={elementValue.categoryModalShow}
            modalData={allCategory}
            selectedItem={elementValue.category.name}
            selectedDataIndex={allCategory.findIndex((n) => n.name === elementValue.category.name)}
            setModalOpen={() => {
              setElementValue({
                ...elementValue,
                categoryModalShow: !elementValue.categoryModalShow,
              });
            }}
            setDataSelected={(val) => {
              setElementValue({
                ...elementValue,
                category: val,
                categoryModalShow: false,
              });
            }}
            error={errorValue.category ? errorValue.category : ''}
          />

          <ModalSelect
            heading={'order after element'}
            modalOpen={elementValue.elementModalShow}
            modalData={elementList}
            selectedItem={elementValue.ordeAfterrName}
            setModalOpen={() => {
              setElementValue({
                ...elementValue,
                elementModalShow: !elementValue.elementModalShow,
              });
            }}
            selectedDataIndex={elementList.findIndex((n) => n.name === elementValue.ordeAfterrName)}
            setDataSelected={(val) => {
              setElementValue({
                ...elementValue,
                orderAfter: val._id,
                ordeAfterrName: val.name,
                elementModalShow: false,
              });
            }}
            error={errorValue.orderAfter ? errorValue.orderAfter : ''}
          />
          <View style={styles.allowancesView}>
            <View>
              <AText
                styleText={{ textAlign: 'left' }}
                fontWeight={FontStyle.fontMedium}
                fontSize={'medium'}
              >
                Relaxation Allowance
              </AText>
              <View style={styles.allowanceInput}>
                <Textinputs
                  // label={'Relaxation Allowance'}
                  placeholder="e.g. 1"
                  keyboardtype={'numeric'}
                  maxLength={3}
                  underlineColor={dark ? '#000' : '#fff'}
                  value={elementValue.relaxationAllowance}
                  onchange={(val) => {
                    setElementValue({
                      ...elementValue,
                      relaxationAllowance: val,
                    });
                  }}
                  stylesTextInput={{ width: 90 }}
                />
                <FIcon name="percent" style={styles.percentIcon} size={10} />
              </View>
              <AText error fontSize={'xtrasmall'}>
                {errorValue.relaxationAllowance ?? ''}
              </AText>
            </View>
            <View>
              <AText
                styleText={{ textAlign: 'left' }}
                fontWeight={FontStyle.fontMedium}
                fontSize={'medium'}
              >
                Contingency Allowance
              </AText>
              <View style={styles.allowanceInput}>
                <Textinputs
                  // label={'Contingency Allowance'}
                  placeholder="e.g. 1"
                  keyboardtype={'numeric'}
                  maxLength={3}
                  value={elementValue.contingencyAllowance}
                  onerror={false}
                  onchange={(val) => {
                    setElementValue({
                      ...elementValue,
                      contingencyAllowance: val,
                    });
                  }}
                  stylesTextInput={{ width: 90 }}
                />
                <FIcon name="percent" style={styles.percentIcon} size={10} />
              </View>
              <AText error fontSize={'xtrasmall'}>
                {errorValue.contingencyAllowance ?? ''}
              </AText>
            </View>
          </View>
        </View>
      ) : (
        <View style={{ width: windowWidth * 0.86, alignSelf: 'center' }}>
          <View style={[styles.sortmodalContainer]}>
            <AText fontWeight={FontStyle.fontMedium} color={'#3C4555'} fontSize={'large'}>
              Count
            </AText>
            <CustomSwitch
              selected={elementValue.count}
              option1={'ON'}
              option2={'OFF'}
              updatedSwitchData
              onSelectSwitch={() => {
                setElementValue({ ...elementValue, count: !elementValue.count });
              }}
              selectionColor={'blue'}
            />
          </View>

          <AText
            styleText={{ paddingTop: 5, paddingBottom: 10 }}
            fontSize={'medium'}
            fontWeight={FontStyle.fontBold}
          >
            Default Rating:
          </AText>

          {deftRatngOption.map((item) => (
            <AButton
              mode="text"
              onPress={() => {
                setElementValue({ ...elementValue, rating: item.id });
              }}
              btnStyle={styles.radiobtnView}
              color={elementValue.rating === item.id ? colors.primary : 'black'}
              title={item.label}
              styleText={{ color: dark ? '#fff' : '#000' }}
              icon={
                <Icon
                  name={elementValue.rating === item.id ? 'radio-button-on' : 'radio-button-off'}
                  color={elementValue.rating === item.id ? colors.primary : 'black'}
                  style={styles.iconStyle}
                  size={25}
                />
              }
            />
          ))}
        </View>
      )}

      {/* ----------------------------create element modal end------------------------------------------- */}
      <BottomModal
        height={windowHeight > 1000 ? windowHeight * 0.2 : windowHeight * 0.3}
        modalShow={closeCanCreateModal}
        confirmPress={() => {
          clearModal();
          closeModal();
        }}
        cancelShow={() => setCloseCanCreateModal(false)}
        title={'Do you want to cancel?'}
        body={''}
        confirm={'okay'}
        reject={' cancel '}
      />
    </BottomModalUI>
  );
};

export default createElementModal;
