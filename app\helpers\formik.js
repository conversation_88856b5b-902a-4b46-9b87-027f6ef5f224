import { useState } from 'react';
import { StyleSheet, View } from 'react-native';
import { IconButton, LinearGradientButton, Textinputs } from '_theme_components';
import { Formik } from 'formik';
import { isEmpty } from './isEmpty';
import FIcon from 'react-native-vector-icons/Entypo';
import { useTheme } from 'react-native-paper';

export const Formiks = ({
  validation,
  fieldArray,
  btntitle,
  btnContentStyle,
  btnStyle,
  viewStyle,
  submitChange,
  fontSize,
  loginPage,
}) => {
  const { dark } = useTheme();
  const [passwordVisibility, setPasswordVisibility] = useState({
    password: true,
    newpassword: true,
    confirmpassword: true,
  });

  return (
    <Formik
      initialValues={{
        [fieldArray[0].value]: '',
        [fieldArray[1].value]: '',
      }}
      initialTouched={{
        [fieldArray[0].value]: '',
        [fieldArray[1].value]: '',
      }}
      validationSchema={validation}
      validateOnChange={true}
      validateOnBlur={true}
      onSubmit={(values, { setSubmitting, resetForm }) => {
        setSubmitting(false);
        loginPage ? '' : resetForm();
        submitChange(values);
      }}
    >
      {({ submitForm, errors, values, handleChange, isValid, handleBlur, touched }) => (
        <>
          {fieldArray.map(({ name, value, isPassword, placeholder }) => (
            <View style={{ marginTop: 15 }}>
              <Textinputs
                label={name}
                secureTextEntry={isPassword ? passwordVisibility[value] : false}
                Icon={
                  isPassword ? (
                    <IconButton
                      btnStyle={styles.iconBtnStyle}
                      onPress={() =>
                        setPasswordVisibility({
                          ...passwordVisibility,
                          [value]: !passwordVisibility[value],
                        })
                      }
                      icon={
                        <FIcon
                          name={passwordVisibility[value] ? 'eye' : 'eye-with-line'}
                          color={dark ? 'rgba(255, 255, 255, 0.9)' : 'rgba(0, 0, 0, 0.9)'}
                          size={20}
                        />
                      }

                      // onPress={() => setShowPassword(!showPassword)}
                      // icon={<FIcon name={showPassword ? 'eye' : 'eye-with-line'} size={20} />}
                    />
                  ) : (
                    ''
                  )
                }
                placeholder={placeholder ?? name}
                value={values[value]}
                onchange={handleChange(value)}
                onblur={handleBlur(value)}
                onerror={!isEmpty(errors[value]) && touched[value] ? true : false}
                stylesTextInput={{ padding: 5 }}
                error={errors[value] && touched[value] ? errors[value] : ''}
              />
            </View>
          ))}
          <View style={viewStyle}>
            <LinearGradientButton
              btnStyle={btnStyle}
              contentStyles={btnContentStyle}
              disabled={
                !isValid ||
                (isEmpty(values[fieldArray[0].value]) && isEmpty(values[fieldArray[1].value]))
              }
              fontSize={fontSize ?? 'small'}
              title={btntitle}
              onPress={submitForm}
            />
          </View>
        </>
      )}
    </Formik>
  );
};
const styles = StyleSheet.create({
  iconBtnStyle: {
    position: 'absolute',
    alignItems: 'center',
    flexWrap: 'wrap',
    width: 50,
    right: 5,
    zIndex: 11,
    alignSelf: 'center',
    justifyContent: 'center',
  },
});
