import React, { Fragment, useState, useEffect } from 'react';
import { Snackbar, Colors, Portal } from 'react-native-paper';
import { useSelector, useDispatch } from 'react-redux';
import { ALERT_HIDE } from '../store/reducers/alert';

const Alert = () => {
  const dispatch = useDispatch();
  const alert = useSelector((state) => state.alert);
  const [isOpen, setisOpen] = useState(false);

  const hideAlert = () => {
    const timeoutId = setTimeout(() => {
      setisOpen(false);
      dispatch({ type: ALERT_HIDE });
    }, 4000);

    return () => clearTimeout(timeoutId);
  };

  useEffect(() => {
    if (alert.success || alert.error || alert.alerts || alert.closeapp || alert.alert_show) {
      setisOpen(true);
      hideAlert();
    } else {
      setisOpen(false);
    }
  }, [alert]);

  return (
    <Portal>
      <Fragment>
        {isOpen ? (
          <Snackbar
            visible={isOpen}
            onDismiss={() => setisOpen(false)}
            theme={{ colors: { surface: alert.alerts || alert.alert_show ? '#000000' : '#fff' } }}
            style={{
              backgroundColor: alert.closeapp
                ? 'black'
                : alert.error
                  ? Colors.red800
                  : alert.alerts || alert.alert_show
                    ? '#fff'
                    : Colors.green800,
              color: '#000000',
              alignSelf: 'center',
              width: '80%',
            }}
          >
            {alert.message}
          </Snackbar>
        ) : null}
      </Fragment>
    </Portal>
  );
};

export default Alert;
