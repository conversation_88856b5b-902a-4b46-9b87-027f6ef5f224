import { alphanumericSort, getDataByTableAndID, getElementsGroupByTask, isEmpty } from '_helpers';
import { tables } from '_utils';
import { SEVER_LOADING, SEVER_LOADING_FALSE } from '../store/actions/serverDataAction';
import { getUniqueName } from '_provider';

const LOCATION_LIST_STORE = 'LOCATION_LIST_STORE';
const AREA_LIST_STORE = 'AREA_LIST_STORE';
const TASK_LIST_STORE = 'TASK_LIST_STORE';
const STORE_ALL_ELEMENTS = 'STORE_ALL_ELEMENTS';
const STORE_ALL_CATEGORY = 'STORE_ALL_CATEGORY';

// Optimized main function fetching all study items
export const retrieveStudyItemsData = (projectId, dbDetail) => async (dispatch) => {
  try {
    dispatch({ type: SEVER_LOADING });

    const [dataLocation, dataRoles, dataAreas, dataTasks, dataElements] = await Promise.all([
      dispatch(getLocationData(dbDetail, projectId)),
      dispatch(getAreaDataFetch(dbDetail, projectId)),
      dispatch(getTaskDataFetch(projectId, dbDetail)),
      dispatch(getElementDataFetch(dbDetail, projectId)),
      dispatch(getElementCategoryDataFetch(dbDetail, projectId)),
    ]);

    dispatch({ type: SEVER_LOADING_FALSE });
  } catch (error) {
    console.error('Error retrieving study items:', error);
    dispatch({ type: SEVER_LOADING_FALSE });
  }
};
export const getLocationData = (dbDetail, projectId) => async (dispatch) => {
  try {
    const dataLocation = await getDataByTableAndID(tables.LOCATIONS_TABLE, projectId, dbDetail);
    if (!isEmpty(dataLocation)) {
      dispatch({
        type: LOCATION_LIST_STORE,
        payload: dataLocation,
      });
    }
  } catch (error) {
    console.error('Error fetching location data:', error);
  }
};

export const setNextElementSet = async (elementList, elementSelect) => {
  var nextElements = elementList.slice();
  let previousElementIndex = elementList.indexOf(elementSelect);
  nextElements = elementList.slice(previousElementIndex + 1, previousElementIndex + 4);
  return nextElements;
};

export const getAreaDataFetch = (dbDetail, projectId) => async (dispatch) => {
  try {
    const [data, createData] = await Promise.all([
      getDataByTableAndID(tables.AREAS_TABLE, projectId, dbDetail),
      getDataByTableAndID(tables.CREATE_AREA_TABLE, projectId, dbDetail),
    ]);

    var areas = [...data];
    createData.forEach((item) => {
      const isUnique =
        areas.findIndex((s) => getUniqueName(item.name) === getUniqueName(s.name)) === -1;
      if (isUnique) areas.push(item);
    });

    areas.forEach((area) => (area.visible = true));
    areas.sort(alphanumericSort());

    dispatch({
      type: AREA_LIST_STORE,
      payload: areas,
    });
  } catch (error) {
    console.error('Error fetching area data:', error);
  }
};

export const getTaskDataFetch = (projectID, dbDetail) => async (dispatch) => {
  try {
    const [tasks, grpdata] = await Promise.all([
      getDataByTableAndID(tables.TASKS_TABLE, projectID, dbDetail),
      getDataByTableAndID(tables.GROUPS_TABLE, projectID, dbDetail),
    ]);

    const groupedData = [];
    const ungroupedEntries = [];

    tasks.forEach((task) => {
      if (!task.group) {
        ungroupedEntries.push(task);
      } else {
        const group = grpdata.find((g) => g._id === task.group);
        if (group) {
          let groupEntry = groupedData.find((g) => g.name === group.name);
          if (!groupEntry) {
            groupEntry = { name: group.name, entries: [task], ungrouped: false };
            groupedData.push(groupEntry);
          } else {
            groupEntry.entries.push(task);
          }
        } else {
          ungroupedEntries.push(task);
        }
      }
    });

    groupedData.push({ name: 'ungrouped', ungrouped: true, entries: ungroupedEntries });

    dispatch({
      type: TASK_LIST_STORE,
      payload: { groupedData, tasks },
    });
  } catch (error) {
    console.error('Error fetching task data:', error);
    dispatch({ type: SEVER_LOADING_FALSE });
  }
};

export const getElementDataFetch = (dbDetail, projectID) => async (dispatch) => {
  try {
    const [data, createData] = await Promise.all([
      getElementsGroupByTask(projectID, dbDetail),
      getDataByTableAndID(tables.CREATE_ELEMENT_TABLE, projectID, dbDetail),
    ]);

    const datas = [...data];
    createData.forEach((item) => {
      const fIndex = datas.findIndex((grData) => grData.taskID === item.taskID);
      if (fIndex !== -1) {
        const isUnique = datas[fIndex].grouped_data.findIndex((s) => s.name === item.name) === -1;
        if (isUnique) {
          if (item.addPosition === 'addFirst') {
            datas[fIndex].grouped_data.unshift(item);
          } else if (item.addPosition === 'addLast') {
            datas[fIndex].grouped_data.push(item);
          } else {
            const elemntData = [];
            datas[fIndex].grouped_data.forEach((elements) => {
              elemntData.push(elements);
              if (elements._id === item.addPosition) {
                elemntData.push(item);
              }
            });
            datas[fIndex].grouped_data = elemntData;
          }
        }
      }
    });

    if (!isEmpty(datas)) {
      dispatch({
        type: STORE_ALL_ELEMENTS,
        payload: datas,
      });
    }
  } catch (error) {
    console.error('Error fetching element data:', error);
  }
};
export const getElementCategoryDataFetch = (dbDetail, projectID) => async (dispatch) => {
  try {
    const data = await getDataByTableAndID(tables.CATEGORIES_TABLE, projectID, dbDetail);
    if (!isEmpty(data)) {
      dispatch({
        type: STORE_ALL_CATEGORY,
        payload: data,
      });
    }
  } catch (error) {
    console.error('Error fetching element category data:', error);
  }
};
