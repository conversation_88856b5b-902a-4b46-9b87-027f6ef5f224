import { executeFetch, isEmpty, deleteRecord, insertIntoTable } from '_helpers';
import { NO_DATA_FOUND, SLOW_INTERNET_ERROR, tables } from '_utils';
import { ALERT_ERROR } from '../reducers/alert';

export const getCategories = (payload) => (dispatch) => {
  return executeFetch('categories/get', 'Getmultiple', payload)
    .then(async (response) => {
      if (response.status === 200 && !isEmpty(response.data)) {
        return response.data;
        // dispatch({
        //     type: CATEGORY_LIST,
        //     payload: response.data
        // });
      } else {
        return [];
      }
    })
    .catch((error) => {
      // if (error.code && [990, 991, 992, 993, , 994, 995, 996].indexOf(error.code) !== -1)
      //     dsipatch(handleError(error));
    });
};

export const pullServerData = (dataTable, projectID, dbDetail) => async (dispatch) => {
  dispatch({
    type: SEVER_LOADING,
  });

  let endPoint = dataTable.toLowerCase() + '/get';

  try {
    const response = await executeFetch(endPoint, 'Getmultiple', projectID);
    if (!isEmpty(response)) {
      let data1 = response;
      let queryData = { projectID: projectID };
      await Promise.all([
        deleteRecord(dataTable, queryData, dbDetail),
        insertIntoTable(dataTable, data1, dbDetail),
      ]);
    } else {
      dispatch({ type: SEVER_LOADING_FALSE });
      dispatch({ type: ALERT_ERROR, payload: NO_DATA_FOUND });
    }
  } catch (error) {
    dispatch({ type: SEVER_LOADING_FALSE });
    dispatch({ type: ALERT_ERROR, payload: SLOW_INTERNET_ERROR });
  }
};

export const fetchGroup = (projectID, dbDetail) => async (dispatch) => {
  try {
    const response = await executeFetch('groups/get', 'Getmultiple', projectID);
    let data = response.data;
    if (response.status === 200) {
      await deleteRecord(tables.GROUPS_TABLE, { projectID: projectID }, dbDetail);
      await insertIntoTable(tables.GROUPS_TABLE, data, dbDetail);
    } else {
      dispatch({
        type: ALERT_ERROR,
        payload: response.data.message || 'Something went wrong. Please try again later.',
      });
      return [];
    }
  } catch (error) {
    return;
  }
};

export const SEVER_LOADING = 'SEVER_LOADING';
export const SEVER_LOADING_FALSE = 'SEVER_LOADING_FALSE';
export const CATEGORY_LIST = 'CATEGORY_LIST';
