<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>Activity Study</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>4.0.2</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>4</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSExceptionDomains</key>
		<dict>
			<key>localhost</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
			</dict>
		</dict>
	</dict>
	<key>NSFaceIDUsageDescription</key>
	<string>Enabling Face ID allows you quick access to Retime</string>
	<key>NSCameraUsageDescription</key>
	<string>Retime would like to acces your camera to capture photo.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string/>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>Retime would like to save photos from the app to your gallery</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Retime would like to access your photo gallery for uploading images to the app</string>
	<key>UIAppFonts</key>
	<array>
		<string>Poppins-Bold.ttf</string>
		<string>Poppins-Medium.ttf</string>
		<string>Poppins-Regular.ttf</string>
		<string>AntDesign.ttf</string>
		<string>Entypo.ttf</string>
		<string>EvilIcons.ttf</string>
		<string>Feather.ttf</string>
		<string>FontAwesome.ttf</string>
		<string>FontAwesome5_Brands.ttf</string>
		<string>FontAwesome5_Regular.ttf</string>
		<string>FontAwesome5_Solid.ttf</string>
		<string>Foundation.ttf</string>
		<string>Ionicons.ttf</string>
		<string>MaterialIcons.ttf</string>
		<string>MaterialCommunityIcons.ttf</string>
		<string>Octicons.ttf</string>
		<string>SimpleLineIcons.ttf</string>
		<string>Zocial.ttf</string>
		<string>NeoSansStdRegular.otf</string>
		<string>NeoSansStdMedium.otf</string>
		<string>NeoSansStdBold.otf</string>
		<string>NeoSansStdBlack.otf</string>
	</array>
	<key>UIFileSharingEnabled</key>
	<string>YES</string>
	<key>LSSupportsOpeningDocumentsInPlace</key>
	<string>YES</string>
	<key>LSApplicationCategoryType</key>
	<string/>
	<key>UIRequiresFullScreen</key>
	<true/>
	<key>UIRequiresFullScreen~ipad</key>
	<string>NO</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UILaunchStoryboardName~ipad</key>
	<string>LaunchScreenIPad.storyboard</string>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen.storyboard</string>
</dict>
</plist>
