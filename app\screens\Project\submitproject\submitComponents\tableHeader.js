// TableHeaders.js
import { AButton } from '_theme_components';
import { FontStyle } from '_utils';
import { isEmpty } from '_helpers';
import React from 'react';
import { StyleSheet } from 'react-native';
import { View } from 'react-native';
import { useTheme } from 'react-native-paper';

const TableHeaders = ({ toggleOrdering }) => {
  const { colors, dark } = useTheme();

  const headers = [
    { id: 1, value: 'Time', per: '12%', name: 'time' },
    { id: 2, value: 'Element', per: '20%', name: 'element' },
    { id: 3, value: 'Rat.', per: '10%', name: 'rating' },
    { id: 4, value: 'FQ.', per: '8%', name: 'frequency' },
    { id: 5, value: 'Area', per: '20%', name: 'area' },
    { id: 6, value: 'task', per: '18%', name: 'task' },
    { id: 6, value: '', per: '12%', name: '' },
    { id: 6, value: '', per: '12%', name: '' },
  ];
  return (
    <View style={styles.tableheading}>
      {headers.map((item) => (
        <AButton
          mode={'text'}
          onPress={() => (!isEmpty(item.name) ? toggleOrdering(item.name) : '')}
          key={item.name}
          btnStyle={{
            width: item.per,
            paddingStart: ['Area', 'Time'].includes(item.value) ? 0 : 5,
            justifyContent: ['Time'].includes(item.value) ? 'center' : 'flex-start',
            alignItems: ['Time'].includes(item.value) ? 'center' : 'flex-start',
          }}
          styleText={{ color: dark ? '#fff' : '#000', textAlign: 'left' }}
          fontSize={'small'}
          fontWeight={FontStyle.fontBold}
          title={item.value}
        />
      ))}
    </View>
  );
};

export default TableHeaders;
const styles = StyleSheet.create({
  tableheading: {
    flexDirection: 'row',
    width: '100%',
    marginBottom: 5,
    alignSelf: 'center',
    // justifyContent: "space-between",
  },
});
