import React from 'react';
import { StyleSheet, Text, Pressable } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { useTheme } from 'react-native-paper';
import { isEmpty } from '_helpers';
import { FontStyle, getFontSize } from '_utils';
import PropTypes from 'prop-types';

const IconButton = ({ btnStyle, disabled, onPress, icon, bgColor }) => {
  IconButton.propTypes = {
    btnStyle: PropTypes.object,
    disabled: PropTypes.bool,
    onPress: PropTypes.func,
    icon: PropTypes.object,
    bgColor: PropTypes.string,
  };
  const getButtonStyle = () => {
    return StyleSheet.create({
      buttonStyle: {
        alignSelf: 'center',
        opacity: 0.9,
        flexDirection: 'row',
        justifyContent: 'center',
        width: '100%',
        padding: 5,
      },
    });
  };
  const buttonStyle = [
    getButtonStyle().buttonStyle,
    {
      backgroundColor: disabled ? '#c2c2c2' : bgColor || 'transparent',
      alignItems: 'center',
      paddingVertical: 8,
    },
    btnStyle,
  ];
  return (
    <>
      <Pressable
        onPress={onPress}
        disabled={isEmpty(disabled) ? false : disabled}
        style={buttonStyle}
      >
        {icon ? icon : null}
      </Pressable>
    </>
  );
};

export default IconButton;
