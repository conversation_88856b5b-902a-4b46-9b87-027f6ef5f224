import { executeFetch, isEmpty, updateTable } from '_helpers';
import { ALERT_ERROR, ALERT_SUCCESS } from '../store/reducers/alert';
import {
  APP_ID,
  FILE_SAVED_LOCALLY,
  FILE_UPLOADED_MESSAGE,
  IMAGE_UPLOADING_ERROR,
  TOKEN_EXPIRED_LOGIN_AGAIN,
  tables,
} from '_utils';
import {
  CREATESTUDY_CAMERA_LOADING,
  CREATESTUDY_LOADING_FALSE,
} from '../store/reducers/createStudy';

export const uploadPhoto =
  (photo, returnImage = false, payload, dbDetail) =>
  async (dispatch) => {
    console.log(payload, ' payllool');
    dispatch({
      type: CREATESTUDY_CAMERA_LOADING,
    });
    let endPoint = 'studies/upload/photo';
    try {
      var formdata = new FormData();
      formdata.append('fileKey', 'photo');
      formdata.append('ImageName', payload.ImageName);
      formdata.append('customerID', payload.customerID);
      formdata.append('projectID', payload.projectID);
      formdata.append('localNo', payload?.study?.length + 1);
      formdata.append('image', photo);
      formdata.append('fileName', 'filename');
      formdata.append('chunkedMode', false);
      formdata.append('mimeType', 'image/jpeg');
      formdata.append('_method', 'PATCH');
      console.log(formdata, ' form data');
      const response = await executeFetch(endPoint, 'uploadImageFormData', formdata);
      console.log(response, ' image updload');
      if (response.status === 200 && response.data.success) {
        if (returnImage) {
          return response.data.data.path;
        }
        setTimeout(async () => {
          dispatch({ type: 'SAVE_IMAGE_LOCAL', payload: photo.uri });
          dispatch({ type: 'SAVE_IMAGE', payload: response.data.data });
        }, 3000);
        await dispatch({ type: ALERT_SUCCESS, payload: FILE_UPLOADED_MESSAGE });
      } else {
        if (returnImage) {
          return;
        }
        if (response.code == 'TokenExpiredError') {
          dispatch({
            type: ALERT_ERROR,
            payload: TOKEN_EXPIRED_LOGIN_AGAIN,
          });
          dispatch({
            type: 'SAVE_IMAGE',
            payload: photo,
          });
          dispatch({ type: 'SAVE_IMAGE_LOCAL', payload: photo.uri });
        }
        dispatch({
          type: CREATESTUDY_LOADING_FALSE,
        });
        dispatch({
          type: ALERT_ERROR,
          payload: response.data.message || IMAGE_UPLOADING_ERROR,
        });
      }
    } catch (error) {
      console.error(error, 'error');
      if (returnImage) {
        return;
      }
      dispatch({
        type: CREATESTUDY_LOADING_FALSE,
      });
      dispatch({
        type: ALERT_ERROR,
        payload: 'Something went wrong. Please try again later.',
      });
      return;
    }
  };

export const Cameraopen = (netConnection, response, ImageName, dbDetail) => async (dispatch) => {
  let type = response.split('.');
  const image = {
    uri: response,
    type: `image/${type[type.length - 1]}`,
    name: response.substr(response.lastIndexOf('/') + 1),
  };
  if (netConnection) {
    dispatch(uploadPhoto(image, false, ImageName, dbDetail));
  } else {
    dispatch({
      type: 'SAVE_IMAGE',
      payload: { path: response },
    });
    dispatch({ type: 'SAVE_IMAGE_LOCAL', payload: response });
    dispatch({
      type: ALERT_SUCCESS,
      payload: FILE_SAVED_LOCALLY,
    });
    return;
  }
  // }
};

export const updateLastObsImage = async (photos, lastObsDataId, dbDetail, local = false) => {
  if (isEmpty(photos)) {
    return;
  }
  const data = {
    // photoUpdate: photos,
    id: lastObsDataId,
  };

  if (local) {
    data.photoUpdateLocal = photos;
  } else {
    data.photoUpdate = photos;
  }

  try {
    await Promise.all([
      updateTable(tables.STUDY_DATA_TABLE, null, data, dbDetail),
      updateTable(tables.RECOVERY_DATA_TABLE, null, data, dbDetail),
    ]);
    return;
  } catch (error) {
    console.log('errrrr', error);
    return;
    // Handle the error if needed.
  }
};
