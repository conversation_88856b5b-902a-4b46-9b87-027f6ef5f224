import { CATEGORY_LIST, SEVER_LOADING, SEVER_LOADING_FALSE } from '../actions/serverDataAction';

const initialState = {
  loading: false,
  roleList: [],
  categoryList: [],
  areaList: [],
  taskList: [],
  hiddenArea: [],
  allElementList: [],
  elementList: [],
  createEntry: {},
  taskAllList: {},
  allCategory: [],
  locationList: [],
  showYESElement: {},
  showNOElement: {},
};

export default (state = initialState, action) => {
  switch (action.type) {
    case SEVER_LOADING:
      return {
        ...state,
        loading: true,
      };
    case SEVER_LOADING_FALSE:
      return {
        ...state,
        loading: false,
      };
    case CATEGORY_LIST:
      return {
        ...state,
        loading: false,
        categoryList: action.payload,
      };
    case 'LOCATION_LIST_STORE':
      return {
        ...state,
        loading: false,
        locationList: action.payload,
      };
    case 'ROLE_LIST_STORE':
      return {
        ...state,
        loading: false,
        roleList: action.payload,
      };
    case 'AREA_LIST_STORE':
      return {
        ...state,
        loading: false,
        areaList: action.payload,
      };
    case 'TASK_LIST_STORE':
      return {
        ...state,
        loading: false,
        taskList: action.payload.groupedData,
        taskAllList: action.payload.tasks,
      };
    case 'HIDDEN_AREA':
      return {
        ...state,
        loading: false,
        hiddenArea: action.payload,
      };
    case 'STORE_ALL_ELEMENTS':
      return {
        ...state,
        loading: false,
        allElementList: action.payload,
      };
    case 'CLEAR_UPTO_TASK':
      return {
        ...state,
        showYESElement: {},
        showNOElement: {},
      };
    case 'STORE_ALL_CATEGORY':
      return {
        ...state,
        loading: false,
        allCategory: action.payload,
      };
    case 'STORE_ELEMENT_LIST':
      return {
        ...state,
        loading: false,
        elementList: action.payload.elementList,
        showYESElement: action.payload.showYESElement,
        showNOElement: action.payload.showNOElement,
      };
    case 'CREATE_ENTRY_DATA':
      return {
        ...state,
        loading: false,
        createEntry: action.payload,
      };
    case 'CANCELING_STUDY':
      return { ...initialState };
    case 'CLEAR_STUDY':
      return {
        ...state,
        loading: false,
        categoryList: [],
        hiddenArea: [],
        elementList: [],
        showYESElement: {},
        showNOElement: {},
        createEntry: {},
        roleList: [],
        areaList: [],
        taskList: [],
        allElementList: [],
        createEntry: {},
        taskAllList: {},
        allCategory: [],
      };
    case 'CLEAR_STUDY_DATA':
      return {
        ...state,
        loading: false,
        categoryList: [],
        hiddenArea: [],
        elementList: [],
        showYESElement: {},
        showNOElement: {},
        createEntry: {},
        roleList: [],
        createEntry: {},
        allCategory: [],
      };
    case 'CLEAR_SERVER':
      return {
        ...initialState,
      };
    case 'USER_LOGOUT':
      return { ...initialState };
    default: {
      return state;
    }
  }
};
