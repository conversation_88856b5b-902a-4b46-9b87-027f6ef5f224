import { Platform, Pressable, StyleSheet, Text, View } from 'react-native';
import React from 'react';
import { AText, BottomModalUI, RatingButton, StudyNextCard } from '_theme_components';
import { FontStyle, LINEAR_GRADIENT_RATING_BUTTON, windowHeight, windowWidth } from '_utils';
import LinearGradient from 'react-native-linear-gradient';
import { isEmpty } from '_helpers';
import { useTheme } from 'react-native-paper';

const EditStudyData = ({
  closeModal,
  editModalHeader,
  editComponentModalShow,
  taskAllList,
  updateData,
  ratingData,
  areaList,
  elementData,
}) => {
  const rate = 60;
  const n = 14;
  const { colors, dark } = useTheme();

  const renderStudyNextCard = (data, updateDataCallback) => (
    <View style={{ width: '100%' }} key={data.id}>
      <StudyNextCard
        data={data}
        showBorder={true}
        icon={'arrow-right'}
        navigateNext={() => updateDataCallback(data)}
      />
    </View>
  );

  const ratingPanel = (updateDataCallback) =>
    !isEmpty(rate) &&
    [...Array(n)].map((e, i) => {
      let currentRating = rate + i * 5;
      currentRating = currentRating == 125 ? 'Not Rated' : currentRating;

      const isNotRated = currentRating === 125 || currentRating === 'Not Rated';
      const isRatingSelected = ratingData === currentRating;

      return (
        <RatingButton
          onPress={() => {
            updateDataCallback(isNotRated ? 'Not Rated' : currentRating);
          }}
          selected={currentRating === 100 && isEmpty(ratingData) ? true : isRatingSelected}
          ratingContainerStyle={{
            height: windowWidth > windowHeight ? windowWidth * 0.05 : windowWidth * 0.12,
            width: isNotRated ? '63%' : '20%',
            opacity: ratingData === rate + i * 5 ? 0.8 : 1,
            backgroundColor:
              ratingData === rate + i * 5
                ? 'rgba(226, 216, 235 ,0.9)'
                : dark
                  ? colors.onSurface
                  : '#fff',
            borderColor:
              currentRating === 100 && isEmpty(ratingData)
                ? '#00C0F3'
                : isRatingSelected
                  ? '#00C0F3'
                  : '#fff',
          }}
          selectedRateContainerStyle={{
            borderColor:
              currentRating === 100 && isEmpty(ratingData)
                ? '#00C0F3'
                : isRatingSelected
                  ? '#00C0F3'
                  : '#fff',
            borderWidth:
              currentRating === 100 && isEmpty(ratingData) ? 3 : isRatingSelected ? 3 : 1,
          }}
          item={isNotRated ? 'Not Rated' : currentRating}
        />
      );
    });
  return (
    <BottomModalUI
      width={'100%'}
      closeModal={() => {
        closeModal();
      }}
      height={editModalHeader == 'Rating' ? windowHeight * 0.6 : windowHeight * 0.47}
      modalShow={editComponentModalShow}
      showScroll={true}
      closeShow
    >
      <View style={styles.containerStyles}>
        <AText fontWeight={FontStyle.fontMedium} fontSize={'title'}>
          Edit {editModalHeader}
        </AText>
      </View>
      <View style={[styles.container]}>
        {editModalHeader === 'Task' && !isEmpty(taskAllList) ? (
          taskAllList.map((item) => renderStudyNextCard(item, updateData.bind(null, 'task')))
        ) : editModalHeader === 'Task' ? (
          <Text style={{ ...styles.noDataStyle, color: dark ? '#fff' : '#000' }}>
            No Data Found
          </Text>
        ) : null}
        {editModalHeader === 'Area' && !isEmpty(areaList) ? (
          areaList.map((item) => renderStudyNextCard(item, updateData.bind(null, 'area')))
        ) : editModalHeader === 'Area' ? (
          <Text style={{ ...styles.noDataStyle, color: dark ? '#fff' : '#000' }}>
            No Data Found
          </Text>
        ) : null}
        {editModalHeader === 'Element' && !isEmpty(elementData) ? (
          elementData.map((item) => renderStudyNextCard(item, updateData.bind(null, 'element')))
        ) : editModalHeader === 'Element' ? (
          <Text style={{ ...styles.noDataStyle, color: dark ? '#fff' : '#000' }}>
            No Data Found
          </Text>
        ) : null}
      </View>
      {editModalHeader === 'Rating' && (
        <View style={[styles.rateContainStyle]}>
          {ratingPanel(updateData.bind(null, 'rating'))}
        </View>
      )}
    </BottomModalUI>
  );
};

export default EditStudyData;

const styles = StyleSheet.create({
  noDataStyle: {
    alignSelf: 'center',
    fontSize: 25,
    marginTop: 20,
  },
  containerStyles: {
    paddingVertical: 15,
    width: '85%',
    alignSelf: 'center',
  },
  container: {
    alignItems: 'center',
    width: '100%',
    paddingBottom: 30,
  },
  ratecontainer: {
    justifyContent: 'center',
    marginHorizontal: 7,
    marginVertical: 10,
    alignItems: 'center',
    borderRadius: 26,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
    elevation: 2,
  },
  slectedRatecontainer: {
    justifyContent: 'center',
    width: '100%',
    height: '100%',
    alignSelf: 'center',
    alignItems: 'center',
    borderRadius: 26,
  },
  rateContainStyle: {
    width: Platform.OS == 'ios' ? '100%' : '100%',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    flexWrap: 'wrap',
    paddingBottom: 55,
  },
});
