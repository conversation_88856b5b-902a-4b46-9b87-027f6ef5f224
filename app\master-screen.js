import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import NetInfo from '@react-native-community/netinfo';

import Navigation from './navigation';
import { SplashScreen } from './screens';
import { Alert } from '_theme_components';
import { ALREADY_HAS_LOGIN } from './store/reducers/login';
import { USER } from './store/reducers/user';
import { NET_OFF, NET_ON } from './store/reducers/netInfo';
import { getData, isEmpty } from '_helpers';
import { createDataCase, getSyncExclamation } from '_provider';
import { AppThemeProvider } from './hooks/themeContext';

const MasterScreen = () => {
  const dispatch = useDispatch();
  const loginState = useSelector((state) => state.login);
  const { dbDetail } = useSelector((state) => state.user);
  const [splashShow, setSplashShow] = useState(true);

  useEffect(() => {
    NetInfo.addEventListener((networkState) => {
      dispatch({
        type: networkState.isConnected && networkState.isInternetReachable ? NET_ON : NET_OFF,
      });
    });
  }, [NetInfo]);

  const checkUserLoggedIn = async () => {
    try {
      let token = await getData('token');
      if (!isEmpty(token)) {
        let userDetail = await getData('currentUser');

        if (!isEmpty(userDetail)) {
          dispatch({
            type: USER,
            payload: JSON.parse(userDetail),
          });
        }
        dispatch({ type: ALREADY_HAS_LOGIN, payload: { token } });
      }
    } catch (e) {
      // Error
    }
  };
  useEffect(() => {
    setTimeout(() => {
      setSplashShow(false);
    }, 1800);
  }, []);

  useEffect(() => {
    if (!loginState.login) {
      checkUserLoggedIn();
      // launchBugsee()
    }
    if (loginState.login && isEmpty(dbDetail)) {
      dispatch(createDataCase());
    }
  }, [loginState]);

  useEffect(() => {
    if (loginState.login && !isEmpty(dbDetail)) {
      checkExcalmation();
    }
  }, [loginState, dbDetail]);
  const checkExcalmation = async () => {
    let userDetail = await getData('currentUser');
    userDetail = JSON.parse(userDetail);
    dispatch(getSyncExclamation(dbDetail, userDetail._id));
  };

  return (
    <AppThemeProvider>
      {splashShow ? <SplashScreen /> : <Navigation />}
      <Alert />
    </AppThemeProvider>
  );
};

export default MasterScreen;
