import React, { useEffect, useState } from 'react';
import { View, ScrollView, Modal } from 'react-native';
import { Text, useTheme } from 'react-native-paper';
import { FontStyle, windowWidth } from '_utils';
import { AButton, AText } from '_theme_components';
import { isEmpty } from '_helpers';
import Icon from 'react-native-vector-icons/SimpleLineIcons';
import { useIsFocused } from '@react-navigation/native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { styles } from './styles';

const ModalSelect = ({
  modalOpen,
  modalData,
  setModalOpen,
  setDataSelected,
  selectedItem,
  heading,
  dismissable,
  placeholder,
  selectedDataIndex,
  error,
}) => {
  const { colors, dark } = useTheme();
  const isFocused = useIsFocused();
  const [checked, setChecked] = useState({
    name: '',
  });
  useEffect(() => {
    if (modalOpen) {
      if (!isEmpty(selectedDataIndex) && selectedDataIndex > -1) {
        setChecked(modalData[selectedDataIndex]);
      } else {
        setChecked({
          name: selectedItem,
        });
      }
    }
  }, [modalOpen]);
  return (
    <>
      {!isEmpty(heading) && (
        <AText
          styleText={{ textTransform: 'capitalize', paddingTop: 18 }}
          fontWeight={FontStyle.fontMedium}
          fontSize={'small'}
        >
          {heading}
        </AText>
      )}
      <AButton
        mode={'text'}
        activeOpacity={0.9}
        onPress={() => setModalOpen()}
        btnStyle={styles.selectElemetStyle}
        fontSize={'small'}
        styleText={{
          textTransform: selectedItem !== 'NVA' ? 'capitalize' : null,
          color: dark ? '#fff' : '#525252',
        }}
        title={selectedItem ?? placeholder}
        icon={<Icon name="arrow-down" size={12} />}
      />
      {error ? (
        <AText fontSize={'xtrasmall'} styleText={{ color: 'red' }}>
          {error}
        </AText>
      ) : null}
      {modalOpen && (
        <Modal
          animationType="slide"
          transparent={true}
          dismissable={dismissable}
          onDismiss={setModalOpen}
          style={{ flex: 1 }}
          onRequestClose={() => {
            try {
              setModalOpen();
            } catch (e) {
              console.log(e, ' error on request close');
            }
          }}
          visible={modalOpen}
        >
          <View style={styles.modalSelectContainerStyle}>
            <View
              style={[
                styles.modalSelectInsideContainerStyle,
                { backgroundColor: colors.onSurface, width: windowWidth * 0.37 },
              ]}
            >
              <AText
                styleText={styles.modalSelectHeadingTextStyle}
                fontSize={'medium'}
                fontWeight={FontStyle.fontBold}
              >
                {heading}
              </AText>
              <ScrollView
                showsVerticalScrollIndicator={false}
                style={styles.modalSelectScrollContainer}
              >
                {modalData.length > 0 ? (
                  modalData.map((item) => (
                    <AButton
                      onPress={() => {
                        setChecked(item);
                      }}
                      key={Math.random()}
                      mode={'text'}
                      btnStyle={styles.itemContainer}
                      icon={
                        <Ionicons
                          name={checked.name === item.name ? 'radio-button-on' : 'radio-button-off'}
                          color={
                            checked.name === item.name ? colors.primary : dark ? '#fff' : 'black'
                          }
                          style={styles.iconStyle}
                          size={25}
                        />
                      }
                      title={item.name}
                      fontSize={'small'}
                      fontWeight={FontStyle.fontRegular}
                      styleText={{
                        textAlign: 'left',
                        textTransform: 'none',
                        color:
                          checked.name === item.name ? colors.primary : dark ? '#fff' : 'black',
                        width: '87%',
                      }}
                    />
                  ))
                ) : (
                  <Text style={{ textAlign: 'center' }}>No records found</Text>
                )}
              </ScrollView>
              <View style={[styles.modalSelectBtncontainer, { backgroundColor: colors.onSurface }]}>
                <AButton
                  styleText={{ color: colors.primary }}
                  fontSize={'small'}
                  mode="text"
                  title={'cancel'}
                  onPress={() => {
                    setChecked(''), setModalOpen();
                  }}
                />
                <AButton
                  disabled={isEmpty(checked.name)}
                  btnStyle={{ alignSelf: 'flex-end' }}
                  mode="text"
                  styleText={{ color: colors.primary }}
                  fontSize={'small'}
                  title={'ok'}
                  onPress={() => {
                    setDataSelected(checked);
                  }}
                />
              </View>
            </View>
          </View>
        </Modal>
      )}
    </>
  );
};

export default ModalSelect;
