import { StyleSheet, View } from 'react-native';
import React, { useEffect, useState } from 'react';
import { useTheme } from 'react-native-paper';
import { useDispatch, useSelector } from 'react-redux';
import { useIsFocused } from '@react-navigation/native';

import { AText, NoDataFound, StudyHeader, StudyNextCard } from '_theme_components';
import { FontStyle, INTERNET_ERROR, localimage, tables, windowHeight, windowWidth } from '_utils';
import { isEmpty } from '_helpers';
import { creatingData, getAreaDataFetch } from '_provider';

import { pullServerData } from '_action';
import { ALERT_ERROR } from '../../../store/reducers/alert';
import { CreateAreaModal } from './components';

const AreaScreen = ({
  navigation,
  modalView,
  navigateTo,
  showCreateModal,
  setShowCreateModal,
  onRefreshPage,
  setRefreshFalse,
  areaFieldValue,
  setAreaFieldValue,
  areaErrorsField,
  setAreaErrorsField,
}) => {
  const { colors } = useTheme();
  const isFocused = useIsFocused();
  const dispatch = useDispatch();

  const { projectSelect } = useSelector((state) => state.createStudy);
  const { userDetails, dbDetail } = useSelector((state) => state.user);
  const { netConnection } = useSelector((state) => state.netInfo);
  const { areaList, hiddenArea } = useSelector((state) => state.serverReducer);

  const [modalShow, setModalShow] = useState(false);
  const [EyeShow, setEyeShow] = useState(false);
  const [showSearch, setShowSearch] = useState(true);

  const [areaData, setAreaData] = useState([]);
  const [areaSearchInput, setAreaSearchInput] = useState('');

  useEffect(() => {
    if (showCreateModal) {
      setModalShow(true);
    }
  }, [showCreateModal]);

  const onToggleSwitch = async (area) => {
    let areaID = area._id;
    area.visible = !area.visible;
    setAreaData([...areaData]);
    let hiddenAreasForStudy = area.visible
      ? hiddenArea.filter((rID) => rID !== areaID)
      : [...hiddenArea, areaID];

    dispatch({
      type: 'HIDDEN_AREA',
      payload: hiddenAreasForStudy,
    });
  };

  useEffect(() => {
    if (isFocused) {
    } else {
      setEyeShow(false);
      setShowSearch(true);
    }
  }, [isFocused]);

  const getAreaData = async (refresh) => {
    if (refresh) {
      await dispatch(pullServerData(tables.AREAS_TABLE, projectSelect._id, dbDetail));
    }
    dispatch(getAreaDataFetch(dbDetail, projectSelect._id));
  };
  const navigateNext = (item) => {
    dispatch({
      type: 'STORE_AREA',
      payload: item,
    });
    if (modalView) {
      navigateTo(item);
    } else {
      navigateTo('task');
    }
  };
  const onChangeSearch = (value) => {
    let data = areaList;
    if (value !== '') {
      let filteredData = data.filter((item) =>
        item.name.toLowerCase().includes(value.toLowerCase())
      );
      setAreaData(filteredData);
      setAreaSearchInput(value);
    } else {
      setAreaData(data);
      setAreaSearchInput('');
    }
  };

  const populateDataHiddenData = async (data) => {
    data.map((area) => {
      area.visible = hiddenArea.indexOf(area._id) == -1;
    });
    setAreaData(data);
  };

  useEffect(() => {
    if (!isEmpty(areaList)) {
      populateDataHiddenData(areaList);
    }
  }, [areaList]);

  const ItemShowView = ({ item, index }) => {
    return item.visible ? (
      <View key={item.id + index + item._id}>
        <StudyNextCard
          ShowSwitch={EyeShow}
          data={item}
          onToggleSwitch={() => {
            onToggleSwitch(item);
          }}
          navigateNext={() => {
            navigateNext(item);
          }}
        />
      </View>
    ) : null;
  };

  const areaFieldSetup = async (areaFieldValue) => {
    const payload = {
      name: areaFieldValue.trim(),
      projectID: projectSelect._id,
      customerID: projectSelect.customerID,
    };
    setShowCreateModal();
    setModalShow(false);
    await dispatch(creatingData(areaList, payload, netConnection, dbDetail, 'area'));
    getAreaData(netConnection);
  };

  useEffect(() => {
    if (onRefreshPage) {
      onRefresh();
    }
  }, [onRefreshPage]);

  const onRefresh = async () => {
    if (!netConnection) {
      dispatch({
        type: ALERT_ERROR,
        payload: INTERNET_ERROR,
      });
    } else {
      await getAreaData(true);
      setEyeShow(false);
      setTimeout(() => {
        setShowSearch(false);
      }, 2500);
    }
    setRefreshFalse();
  };

  return (
    <>
      <StudyHeader
        modalView={modalView}
        ShowMagnify={true}
        navigation={navigation}
        Title={'Area'}
        bachkHandler={() => {}}
        SubTitle={'Select area'}
        showBack={false}
        icon={!EyeShow ? 'eye' : 'eye-off'}
        OnPressIcon={() => setEyeShow(!EyeShow)}
        searchPress={() => {
          setShowSearch(!showSearch), onChangeSearch('');
        }}
        showSearch={showSearch}
        ShowIcon={true}
        OnChangeSearch={(val) => onChangeSearch(val)}
      />
      <View onStartShouldSetResponder={() => true} style={styles.container}>
        {!isEmpty(areaData) ? (
          areaData.map((item, index) => <ItemShowView item={item} index={index} />)
        ) : !isEmpty(areaList) && isEmpty(areaData) && !isEmpty(areaSearchInput) ? (
          <NoDataFound
            source={localimage.emptydata}
            text={'Sorry, there is no area with that name'}
            imageStyle={{
              height: windowHeight * 0.22,
              width: windowWidth * 0.6,
              marginTop: 55,
              tintColor: colors.primary,
            }}
          />
        ) : isEmpty(areaList) ? (
          <AText
            fontSize={'large'}
            styleText={{ color: '#878787', paddingTop: 70, textAlign: 'center' }}
            fontWeight={FontStyle.fontBold}
          >
            No Data Found
          </AText>
        ) : null}
      </View>
      {EyeShow && (
        <>
          <View style={styles.hiddenAreaContainer}>
            <AText
              fontWeight={FontStyle.fontBold}
              styleText={{ color: '#C1C1C1' }}
              fontSize={'large'}
            >
              Hidden Areas
            </AText>
          </View>
          <View style={styles.container}>
            {!isEmpty(areaData) &&
              areaData.map(
                (item, index) =>
                  !item.visible && (
                    <View key={index}>
                      <StudyNextCard
                        ShowSwitch={EyeShow}
                        data={item}
                        onToggleSwitch={(item) => {
                          onToggleSwitch(item);
                        }}
                        navigateNext={() => {
                          navigateNext(item);
                        }}
                      />
                    </View>
                  )
              )}
          </View>
        </>
      )}
      <CreateAreaModal
        modalShow={modalShow}
        createArea={(val) => areaFieldSetup(val)}
        areaFieldValue={areaFieldValue}
        setAreaFieldValue={(val) => setAreaFieldValue(val)}
        closeModal={() => {
          setShowCreateModal();
          setModalShow(false);
        }}
        errorsField={areaErrorsField}
        setErrorsField={(val) => setAreaErrorsField(val)}
      />
    </>
  );
};

export default AreaScreen;

const styles = StyleSheet.create({
  container: {
    width: '87%',
    marginTop: 15,
  },
  hiddenAreaContainer: {
    justifyContent: 'flex-start',
    alignSelf: 'center',
    marginStart: 25,
    marginTop: 15,
    width: '75%',
  },
});
