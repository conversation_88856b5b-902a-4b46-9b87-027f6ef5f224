import React from 'react';
import { StyleSheet, View } from 'react-native';
import { useTheme } from 'react-native-paper';
import { LINEAR_GRADIENT_RATING_BUTTON, windowHeight, windowWidth } from '_utils';
import { RatingButton } from './';

const RatingPanel = ({ disableBtn, onPress, ratingData }) => {
  const n = 14;
  const rate = 60;
  const { colors, dark } = useTheme();

  return (
    <View style={styles.container}>
      {[...Array(n)].map((e, i) => {
        let currentRating = rate + i * 5;
        currentRating = currentRating == 125 ? 'Not Rated' : currentRating;
        const isNotRated = currentRating === 125 || currentRating === 'Not Rated';
        const isRatingSelected = ratingData === currentRating;
        return (
          <RatingButton
            onPress={() => {
              onPress(isNotRated ? 'Not Rated' : currentRating);
            }}
            disableBtn={disableBtn ?? false}
            selected={isRatingSelected}
            ratingContainerStyle={{
              height: windowWidth > windowHeight ? windowWidth * 0.04 : windowWidth * 0.1,
              width:
                Platform.OS == 'ios' && isNotRated
                  ? '55%'
                  : Platform.OS == 'ios'
                    ? '26%'
                    : // : Platform.OS == 'android' && isNotRated && windowWidth > windowHeight
                      //   ? '32%'
                      Platform.OS == 'android' && isNotRated
                      ? '57%'
                      : // : Platform.OS == 'android' && windowWidth > windowHeight
                        //   ? '15%'
                        '27%',
              opacity: ratingData === currentRating ? 0.8 : 1,
              backgroundColor:
                ratingData === currentRating
                  ? LINEAR_GRADIENT_RATING_BUTTON
                  : dark
                    ? colors.onSurface
                    : '#fff',
            }}
            selectedRateContainerStyle={{
              borderColor: isRatingSelected ? '#00C0F3' : '#fff',
              borderWidth: isRatingSelected ? 3 : 1,
            }}
            item={isNotRated ? 'Not Rated' : currentRating}
          />
        );
      })}
    </View>
  );
};

export default RatingPanel;
const styles = StyleSheet.create({
  container: {
    width: '100%',
    alignSelf: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    flexWrap: 'wrap',
    // marginTop: 15,

    marginBottom: 5,
  },
  ratecontainer: {
    alignSelf: 'center',
    alignContent: 'center',
    justifyContent: 'center',
    marginHorizontal: 7,
    marginVertical: 8,
    alignItems: 'center',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
    elevation: Platform.OS == 'ios' ? 0 : 2.5,
  },

  slectedRatecontainer: {
    justifyContent: 'center',
    width: '100%',
    height: '100%',
    alignSelf: 'center',
    alignItems: 'center',
    borderRadius: 12,
  },
  rateContainStyle: {
    width: '99%',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    flexWrap: 'wrap',
    paddingBottom: 55,
  },
});
