import { FlatList, ScrollView, StyleSheet, View } from 'react-native';
import React, { useEffect, useRef, useState } from 'react';
import { useTheme } from 'react-native-paper';

import { AText, StudyNextCard } from '_theme_components';
import { FontStyle, windowHeight } from '_utils';
import { isEmpty } from '_helpers';
import { useSelector } from 'react-redux';
import { useScrollToTop } from '@react-navigation/native';

const NextElementScreen = ({
  nextElementData,
  navigateScreen,
  controllingElementID,
  selectedElementIndex,
  CanElementContinue,
}) => {
  const { dark } = useTheme();
  const goToScreen = async (screen, elementContinue = false, element) => {
    navigateScreen(screen, elementContinue, element);
    return;
  };
  const [elementData, setElementData] = useState([]);
  const { elementList } = useSelector((state) => state.serverReducer);
  const scrollViewRef = useRef(null);
  const itemRefs = useRef([]);

  useEffect(() => {
    if (elementList) {
      setElementData(elementList);
      CanElementContinue();
    }
    setTimeout(() => {
      scrollToElement(selectedElementIndex);
    }, 500);
  }, [selectedElementIndex]);

  const scrollToElement = (index) => {
    if (index === null || !itemRefs.current[index] || !scrollViewRef.current) {
      console.error(`Invalid reference or index: ${index}`);
      return;
    }

    try {
      itemRefs.current[index].measureLayout(
        scrollViewRef.current,
        (x, y) => {
          if (scrollViewRef.current) {
            scrollViewRef.current.scrollTo({ x: 0, y: y, animated: false });
          }
        },
        (error) => {
          console.error(`MeasureLayout Error at index ${index}:`, error);
        }
      );
    } catch (err) {
      console.error('Error while trying to scroll to element:', err);
    }
  };
  const ItemView = ({ item, index }) => {
    return (
      <View
        ref={(el) => (itemRefs.current[index] = el)}
        onStartShouldSetResponder={() => true}
        key={item.id + Math.random()}
      >
        <StudyNextCard
          ShowEC={true}
          selectedElement={selectedElementIndex == index}
          controllingElementID={
            !isEmpty(controllingElementID) && controllingElementID == item._id ? true : false
          }
          data={item}
          icon={
            !isEmpty(controllingElementID) && controllingElementID == item._id
              ? 'target'
              : 'arrow-right'
          }
          navigateNext={(val) => {
            goToScreen('nextElement', val, item);
          }}
        />
      </View>
    );
  };

  return (
    <ScrollView
      ref={scrollViewRef}
      onStartShouldSetResponder={() => true}
      keyboardShouldPersistTaps={'always'}
      showsVerticalScrollIndicator={false}
      showsHorizontalScrollIndicator={false}
      style={styles.container}
      contentContainerStyle={{
        flexGrow: 1,
        alignItems: 'center',
        paddingBottom: 10,
      }}
      nestedScrollEnabled={true}
    >
      <View style={styles.textStyleView}>
        <AText
          fontWeight={FontStyle.fontBold}
          styleText={{ color: dark ? '#fff' : '#3C4555' }}
          fontSize={'title'}
        >
          Elements
        </AText>
      </View>

      <View onStartShouldSetResponder={() => true} style={styles.nextElemtcontainer}>
        {!isEmpty(elementData) ? (
          elementData.map((item, index) => (
            <ItemView key={index.toString()} item={item} index={index} />
          ))
        ) : (
          <AText
            fontSize={'large'}
            lightGray
            styleText={{ textAlign: 'center', paddingTop: 70 }}
            fontWeight={FontStyle.fontBold}
          >
            No Element Found
          </AText>
        )}
      </View>
    </ScrollView>
  );
};

export default NextElementScreen;

const styles = StyleSheet.create({
  container: {
    marginTop: 30,
    alignSelf: 'center',
    width: '100%',
    height: 150,
    flex: 1,
  },
  nextElemtcontainer: {
    // flex: 1,
    marginTop: 7,
    paddingBottom: 15,
  },
  textStyleView: { width: '72%', alignSelf: 'center' },
});
