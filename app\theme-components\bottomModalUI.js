import {
  KeyboardAvoidingView,
  Platform,
  Modal,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  View,
  TouchableOpacity,
} from 'react-native';
import React from 'react';
import { AButton, AText, IconButton } from '_theme_components';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useTheme, Portal } from 'react-native-paper';
import { FontStyle, LINEAR_GRADIENT_RATING_BUTTON, windowHeight } from '_utils';
import LinearGradient from 'react-native-linear-gradient';

const BottomModalUI = ({
  modalShow,
  width,
  height,
  children,
  closeModal,
  showScroll,
  closeShow,
  ModalStyle,
  ModalClose,
  backgroundColor,
  title,
  fontSize,
  styleTitle,
  showLinearGradient,
  scrollStyle,
  borderRadius,
}) => {
  const { colors, dark } = useTheme();
  return (
    <>
      {modalShow && Platform.OS === 'android' ? (
        <Modal
          animationType="none"
          transparent={true}
          dismissable={ModalClose}
          onDismiss={ModalClose ? closeModal : ''}
          onRequestClose={() => {
            try {
              closeModal();
            } catch (e) {
              console.log(e, ' error on request close bottommodalUI');
            }
          }}
          hideModalContentWhileAnimating={true}
          style={[{ zIndex: 1 }, ModalStyle]}
          contentContainerStyle={[{ justifyContent: 'center', flex: 1 }, ModalStyle]}
          visible={modalShow}
        >
          <SafeAreaView style={styles.safecontainer}>
            <IconButton
              onPress={() => {
                ModalClose ? closeModal() : '';
              }}
              btnStyle={{ flex: 1 }}
            />
            <KeyboardAvoidingView
              behavior={Platform.OS == 'ios' ? 'position' : null}
              enabled={Platform.OS == 'ios' ? true : false}
              contentContainerStyle={[
                styles.container,
                {
                  width: width,
                  height: height,
                  backgroundColor: backgroundColor ? backgroundColor : colors.onSurface,
                },
              ]}
              style={[
                styles.modalConatiner,
                Platform.OS == 'android'
                  ? [
                      styles.container,
                      {
                        width: width,
                        height: height,
                        backgroundColor: backgroundColor ? backgroundColor : colors.onSurface,
                      },
                    ]
                  : {},
              ]}
            >
              {showLinearGradient ? (
                <LinearGradient
                  colors={LINEAR_GRADIENT_RATING_BUTTON}
                  start={{ x: 0.7, y: 0.55 }}
                  end={{ x: 0.5, y: 1.0 }}
                  locations={[0.5, 0.6]}
                  style={[styles.modalContentConatiner, {}]}
                >
                  {/* <View style={[styles.modalContentConatiner, { height: height }]}> */}
                  {closeShow && (
                    <IconButton
                      mode="text"
                      btnStyle={styles.cancelBtnStyle}
                      bgColor={'#fff'}
                      icon={
                        <Ionicons
                          name="close"
                          style={{ alignSelf: 'center' }}
                          color={'#fff'}
                          size={27}
                        />
                      }
                      onPress={() => {
                        closeModal();
                      }}
                    />
                  )}
                  {title && (
                    <View style={{ marginHorizontal: 15, marginTop: 15 }}>
                      <AText
                        fonts={FontStyle.fontBold}
                        styleText={{ color: '#fff' }}
                        fontSize={'smallTitle'}
                      >
                        {title}
                      </AText>
                    </View>
                  )}
                  {showScroll ? (
                    <ScrollView
                      showsHorizontalScrollIndicator={false}
                      showsVerticalScrollIndicator={false}
                      // keyboardShouldPersistTaps={'handled'}
                      contentContainerStyle={styles.scrollContainerStyle}
                      style={[styles.viewStyle, { paddingVertical: 15, paddingBottom: 25 }]}
                    >
                      {children}
                    </ScrollView>
                  ) : (
                    <View style={[styles.viewStyle, { paddingVertical: 15 }]}>{children}</View>
                  )}
                  {/* </View> */}
                </LinearGradient>
              ) : (
                <View style={[styles.modalContentConatiner, { height: height }]}>
                  {closeShow && (
                    <IconButton
                      mode="text"
                      btnStyle={styles.cancelBtnStyle}
                      bgColor={'#fff'}
                      icon={
                        <Ionicons
                          name="close"
                          style={{ alignSelf: 'center' }}
                          color={colors.primary}
                          size={27}
                        />
                      }
                      onPress={() => {
                        closeModal();
                      }}
                    />
                  )}
                  {title && (
                    <View style={{ marginHorizontal: 35, marginVertical: 15, ...styleTitle }}>
                      <AText fontWeight={FontStyle.fontBold} fontSize={fontSize ?? 'medium'}>
                        {title}
                      </AText>
                    </View>
                  )}
                  {showScroll ? (
                    <ScrollView
                      showsHorizontalScrollIndicator={false}
                      showsVerticalScrollIndicator={false}
                      // keyboardShouldPersistTaps={'handled'}
                      contentContainerStyle={styles.scrollContainerStyle}
                      style={[styles.viewStyle, { paddingBottom: 25 }, scrollStyle]}
                    >
                      {children}
                    </ScrollView>
                  ) : (
                    <View style={[styles.viewStyle, {}]}>{children}</View>
                  )}
                </View>
              )}
            </KeyboardAvoidingView>
          </SafeAreaView>
        </Modal>
      ) : (
        modalShow && (
          <Portal>
            <SafeAreaView style={styles.safecontainer}>
              <View style={[{ zIndex: 1, flex: 1, justifyContent: 'center' }, ModalStyle]}>
                <AButton
                  mode={'text'}
                  onPress={() => {
                    ModalClose ? closeModal() : '';
                  }}
                  btnStyle={{ width: '100%', height: '100%' }}
                />
                <KeyboardAvoidingView
                  behavior={Platform.OS == 'ios' ? 'padding' : null}
                  enabled={Platform.OS == 'ios' ? true : false}
                  contentContainerStyle={[
                    styles.container,
                    {
                      width: width,
                      backgroundColor: backgroundColor ? backgroundColor : colors.onSurface,
                    },
                  ]}
                  style={[
                    styles.modalConatiner,
                    {
                      borderTopLeftRadius: borderRadius ?? 28,
                      borderTopRightRadius: borderRadius ?? 28,
                      alignSelf: 'center',
                      position: 'absolute',
                      width: width,
                      bottom: 0,
                      backgroundColor: backgroundColor ? backgroundColor : colors.onSurface,
                    },
                  ]}
                >
                  {showLinearGradient ? (
                    <LinearGradient
                      colors={LINEAR_GRADIENT_RATING_BUTTON}
                      start={{ x: 0.7, y: 0.55 }}
                      end={{ x: 0.5, y: 1.0 }}
                      locations={[0.5, 0.6]}
                      style={[styles.modalContentConatiner, {}]}
                    >
                      {/* <View style={[styles.modalContentConatiner, { height: height }]}> */}
                      {closeShow && (
                        <IconButton
                          mode="text"
                          btnStyle={styles.cancelBtnStyle}
                          bgColor={'#fff'}
                          icon={
                            <Ionicons
                              name="close"
                              style={{ alignSelf: 'center' }}
                              color={'#fff'}
                              size={27}
                            />
                          }
                          onPress={() => {
                            closeModal();
                          }}
                        />
                      )}
                      {title && (
                        <View style={{ marginHorizontal: 15, marginTop: 15 }}>
                          <AText
                            fonts={FontStyle.fontBold}
                            styleText={{ color: '#fff' }}
                            fontSize={'smallTitle'}
                          >
                            {title}
                          </AText>
                        </View>
                      )}
                      {showScroll ? (
                        <ScrollView
                          showsHorizontalScrollIndicator={false}
                          showsVerticalScrollIndicator={false}
                          // keyboardShouldPersistTaps={'handled'}
                          contentContainerStyle={styles.scrollContainerStyle}
                          style={[styles.viewStyle, { paddingVertical: 15, paddingBottom: 25 }]}
                        >
                          {children}
                        </ScrollView>
                      ) : (
                        <View style={[styles.viewStyle, { paddingVertical: 15 }]}>{children}</View>
                      )}
                    </LinearGradient>
                  ) : (
                    <View style={[styles.modalContentConatiner, { height: height }]}>
                      {closeShow && (
                        <IconButton
                          mode="text"
                          btnStyle={styles.cancelBtnStyle}
                          bgColor={'#fff'}
                          icon={
                            <Ionicons
                              name="close"
                              style={{ alignSelf: 'center' }}
                              color={colors.primary}
                              size={27}
                            />
                          }
                          onPress={() => {
                            closeModal();
                          }}
                        />
                      )}
                      {title && (
                        <View style={{ marginHorizontal: 35, marginVertical: 15 }}>
                          <AText
                            styleText={{ fontWeight: '500' }}
                            fonts={FontStyle.fontBlack}
                            fontSize={'title'}
                          >
                            {title}
                          </AText>
                        </View>
                      )}
                      {showScroll ? (
                        <ScrollView
                          showsHorizontalScrollIndicator={false}
                          showsVerticalScrollIndicator={false}
                          // keyboardShouldPersistTaps={'handled'}
                          contentContainerStyle={styles.scrollContainerStyle}
                          style={[styles.viewStyle, { paddingBottom: 25 }, scrollStyle]}
                        >
                          {children}
                        </ScrollView>
                      ) : (
                        <View style={[styles.viewStyle, {}]}>{children}</View>
                      )}
                    </View>
                  )}
                </KeyboardAvoidingView>
              </View>
            </SafeAreaView>
          </Portal>
        )
      )}
    </>
  );
};

export default BottomModalUI;

const styles = StyleSheet.create({
  safecontainer: {
    flex: 1,
    backgroundColor: Platform.OS == 'ios' ? 'rgba(0, 0, 0, 0.5)' : 'rgba(0, 0, 0, 0.5)',
  },
  modalWrapper: {
    // backgroundColor: Platform.OS == 'ios' ? "rgba(0, 0, 0, 0.5)" : "rgba(0, 0, 0, 0.5)",
    flex: 1,
  },
  scrollContainerStyle: {
    flexGrow: 1,
    paddingBottom: 45,
    // backgroundColor: 'red'
    // justifyContent: "center",
  },
  cancelBtnStyle: {
    position: 'absolute',
    zIndex: 1,
    alignItems: 'flex-end',
    width: 70,
    backgroundColor: 'transparent',
    alignSelf: 'flex-end',
  },
  viewStyle: {
    width: '100%',
    alignSelf: 'center',
    flex: 1,
    // backgroundColor: 'rgba(0, 0, 0, 1)',
    paddingVertical: 35,
    paddingBottom: 35,
  },
  modalConatiner: {
    alignSelf: 'center',
  },
  modalContentConatiner: {
    width: '100%',
    height: '100%',
    paddingTop: 15,
    paddingHorizontal: 5,
    paddingBottom: 34,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    alignSelf: 'center',
    // flex: 1,
  },
  container: {
    backgroundColor: 'rgba(255, 255, 255, 1)',
    height: windowHeight * 0.3,
    alignSelf: 'center',
    width: '100%',
    position: 'absolute',
    alignSelf: 'center',
    bottom: 0,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 1.84,
    elevation: Platform.OS == 'ios' ? 2 : 10,
  },
  androidModalContentConatiner: {
    backgroundColor: 'rgba(255, 255, 255, 1)',
    height: windowHeight * 0.3,
    alignSelf: 'center',
    width: '100%',
    // flex: 1,
    position: 'absolute',
    paddingBottom: 14,

    alignSelf: 'center',
    bottom: 0,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 1.84,
    elevation: Platform.OS == 'ios' ? 2 : 10,
    padding: 5,
    elevation: Platform.OS == 'ios' ? 2 : 10,
    alignSelf: 'center',
    paddingTop: 15,
    paddingHorizontal: 5,
    paddingBottom: 4,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
});
