import React, { useEffect, useRef, useState } from 'react';
import {
  ScrollView,
  SafeAreaView,
  Platform,
  View,
  StyleSheet,
  StatusBar,
  RefreshControl,
  Keyboard,
  BackHandler,
  Pressable,
} from 'react-native';
import { CommonActions, useScrollToTop } from '@react-navigation/native';
import {
  FontStyle,
  windowHeight,
  windowWidth,
  ALERT_TITLE,
  STUDY_END_MESSAGE,
  tables,
  STUDY_CANCELING_MESSAGE2,
} from '_utils';
import {
  AText,
  BottomModalUI,
  Textinputs,
  StudyPoPUP,
  LinearGradientButton,
  IconButton,
  AuthLoading,
  AButton,
} from '_theme_components';
import { useTheme } from 'react-native-paper';
import Ionicons from 'react-native-vector-icons/Ionicons';
import CommunityIcon from 'react-native-vector-icons/MaterialCommunityIcons';
import { BottomModal, BreadCrumb } from '_components';
import {
  OpenCameraView,
  dynamicSort,
  formatTime,
  insertIntoTable,
  isEmpty,
  launchCamera,
  markStudyAsCancelled,
  updateTable,
} from '_helpers';
import { useDispatch, useSelector } from 'react-redux';
import { removeStudyTitle } from '_provider';
import { AppContext } from '../../hooks/TimeContext';
import { useContext } from 'react';

const StudyLayout = ({
  children,
  navigation,
  rating,
  scrollToBottom,
  resetRatingTimer,
  elementChangedResetTimer,
  modalView,
  onRefresh,
  refreshing,
  checkReRate,
  reRatingPromptTime,
  stopObservation,
  studyPage,
  setElementNotes,
  ElementNote,
  navigateToStudyPage,
  resetFrequency,
  selectYESorNO,
}) => {
  const scrollViewRef = useRef(null);
  const { colors, dark } = useTheme();
  const dispatch = useDispatch();

  const {
    timerValue,
    startTimer,
    stopTimer,
    restartTimer,
    timerLoader,
    reRateReLoad,
    reRateLoadStop,
  } = useContext(AppContext);
  const { dbDetail } = useSelector((state) => state.user);
  const { netConnection } = useSelector((state) => state.netInfo);
  const { obsrvationData, studyData, obsStart, ECclick, studyID, cameraLoading } = useSelector(
    (state) => state.createStudy
  );
  const { showYESElement, showNOElement } = useSelector((state) => state.serverReducer);
  const { show_byTime } = useSelector((state) => state.questions);
  const [noteModal, setNoteModal] = useState(false);
  const [cancelStudyModal, setCancelStudyModal] = useState(false);
  const [noteText, setNoteText] = useState(ElementNote);
  const [keyBoardFocus, setKeyBoardFocus] = useState(false);
  const [showQuestionPopUP, setShowQuestionPopUP] = useState(false);
  const [cancelModalTwo, setCancelModalTwo] = useState(false);
  const [currentTimeout, setCurrentTimeout] = useState(0);
  const [showQuestion, setShowQuestion] = useState({});
  const [reRateTrigerTime, setReRateTrigerTime] = useState(-1);
  const [showRerateModal, setShowRerateModal] = useState(false);
  const [viewCamera, setViewCamera] = useState(false);
  const [cameraDisable, setCameraDisable] = useState(false);
  const [freetext, setFreetext] = useState('');
  const [selectedVal, setSelectedVal] = useState('');
  const [selectedCheckboxVal, setselectedCheckboxVal] = useState([]);

  useEffect(() => {
    if (!isEmpty(show_byTime) && !showQuestionPopUP) {
      checkQuestionReminder(show_byTime);
    }
  }, [show_byTime, showQuestionPopUP]);

  useEffect(() => {
    const backHandler = BackHandler.addEventListener('hardwareBackPress', backAction);
    return () => backHandler.remove();
  }, []);

  const backAction = () => {
    endStudy();
    return true;
  };

  const checkQuestionReminder = async (questions) => {
    let item = questions[0];
    let milliSec = item.time;
    if (milliSec <= 0) {
      milliSec = 50;
    }
    let updatedQuestions = JSON.parse(JSON.stringify(questions));
    // Clear the previous timeout
    if (currentTimeout) {
      clearTimeout(currentTimeout);
    }
    const newTimeout = displayTimer(item, milliSec, updatedQuestions);
    setCurrentTimeout(newTimeout);
  };
  const displayTimer = (item, milliSec, updatedQuestions) => {
    return setTimeout(() => {
      Keyboard.dismiss();
      setShowQuestion(item);
      setShowQuestionPopUP(true);
      updatedQuestions = updatedQuestions.slice(1);
      if (!isEmpty(updatedQuestions)) {
        updatedQuestions.map((key, ind) => {
          key.time = key.time - item.time;
          if (key.time <= 0) {
            key.time = 0;
          }
        });
      }
      if (item.repeat) {
        item.time = 60000 * (item.triggerTime.hours * 60 + item.triggerTime.minutes);
        updatedQuestions.push(item);
      }
      if (updatedQuestions.length > 0) {
        updatedQuestions.sort(dynamicSort('time', true));
      }
      dispatch({
        type: 'SAVE_TIME_IN_STUDY_QUES',
        payload: updatedQuestions,
      });
      if (currentTimeout) {
        clearTimeout(currentTimeout);
      }
    }, milliSec);
  };
  useEffect(() => {
    if (elementChangedResetTimer) {
      startTimer();
    }
  }, [elementChangedResetTimer]);

  useEffect(() => {
    if (checkReRate) {
      getReRateElementTriggerTime();
    }
  }, [checkReRate]);

  useEffect(() => {
    if (cameraLoading) {
      setCameraDisable(true);
    } else {
      setTimeout(() => {
        setCameraDisable(false);
      }, 5000);
    }
  }, [cameraLoading]);

  const getReRateElementTriggerTime = (showratePopUp) => {
    if (!checkReRate) {
      return;
    }
    let time = timerValue + reRatingPromptTime;
    setReRateTrigerTime(time);
    if (showratePopUp && studyPage == 'RateNFrequencyScreen') {
      setShowRerateModal(true);
    }
  };

  const displayElementTimer = () => {
    if (checkReRate) {
      if (reRateTrigerTime == timerValue || (reRateReLoad && reRateTrigerTime <= timerValue)) {
        Keyboard.dismiss();
        if (reRateReLoad) {
          reRateLoadStop();
        }
        getReRateElementTriggerTime(true);
      }
    }
    return formatTime(timerValue, 'studyTime');
  };

  const endStudy = () => {
    setCancelStudyModal(true);
  };
  const resetRateTimer = () => {
    restartTimer();
  };

  useEffect(() => {
    if (resetRatingTimer) {
      if (studyPage == 'RateNFrequencyScreen') {
        setReRateTrigerTime(-1);
      }
      resetRateTimer();
    }
  }, [resetRatingTimer]);

  const stopStudy = async () => {
    Keyboard.dismiss();
    setCancelStudyModal(false);
    clearTimeout(currentTimeout);
    global.STUDYPAGESELECT = 'area';
    if (isEmpty(obsrvationData)) {
      await handleEmptyObservationData();
    } else {
      handleNonEmptyObservationData();
    }
  };
  const handleNonEmptyObservationData = async () => {
    if (studyPage == 'RateNFrequencyScreen') {
      await stopObservation();
    }
    updateTable(
      tables.STUDY_TABLE,
      null,
      { studyEndTime: new Date().getTime(), studyID: studyID },
      dbDetail
    );
    dispatch({ type: 'CLEAR_STUDY_DATA' });
    dispatch({ type: 'ALERT', payload: 'Study has now ended' });
    stopTimer();
    navigation.dispatch(CommonActions.reset({ index: 1, routes: [{ name: 'SubmitData' }] }));
  };
  const handleEmptyObservationData = async () => {
    stopTimer();
    markStudyAsCancelled(studyID, dbDetail);
    setCancelModalTwo(false);
    dispatch({ type: 'CLEAR_STUDY' });
    dispatch({ type: 'CANCELING_STUDY' });
    await removeStudyTitle(studyData.name);
    navigation.replace('HomeNav', { screen: 'Home' });
  };

  const updateAnswer = async (submit, val) => {
    if (submit) {
      const payload = {
        question: showQuestion.text,
        answer: val,
        studyID: studyID,
        time: new Date().getTime(),
        questionID: showQuestion._id,
        triggerType: showQuestion.triggerType,
        // observationID: 1675774350670
      };
      insertIntoTable(tables.ANSWERS_TABLE, [payload], dbDetail);
    }
    setShowQuestionPopUP(false);
  };

  useEffect(() => {
    if (scrollViewRef.current) {
      try {
        scrollViewRef.current.scrollTo({
          y: 0,
          animated: true,
        });
      } catch (error) {
        console.error('Scroll to top error:', error);
      }
    } else {
      console.warn('scrollViewRef is not initialized');
    }
  }, [studyPage]);

  useScrollToTop(scrollViewRef);

  const openCamera = async () => {
    let camerpermision = await launchCamera();
    if (camerpermision) {
      setViewCamera(true);
    }
  };
  const setNoteAction = () => {
    Keyboard.dismiss();
    setKeyBoardFocus(false);
    setElementNotes(noteText);
    setNoteModal(false);
  };

  return (
    <>
      {timerLoader ? (
        <AuthLoading />
      ) : (
        <>
          <Pressable
            onPress={() => Keyboard.dismiss()}
            style={[
              styles.conatinerHeader,
              {
                backgroundColor: dark ? 'rgba(0, 0, 0, 0.7)' : 'rgba(255, 255, 255 ,1)',
              },
            ]}
          >
            <SafeAreaView style={{ backgroundColor: colors.primary }}>
              <StatusBar
                hidden={false}
                animated
                backgroundColor={colors.primary}
                barStyle="light-content"
              />
              {viewCamera ? (
                <OpenCameraView
                  study={obsrvationData}
                  projectID={studyData.projectID}
                  customerID={studyData.customerID}
                  cancel={() => {
                    setViewCamera(false);
                  }}
                  ImageName={studyData.customerName + `_` + studyData.name}
                />
              ) : null}
            </SafeAreaView>
            <SafeAreaView style={{ flex: 1, paddingBottom: Platform.OS === 'ios' ? 30 : 2 }}>
              {!modalView && (
                <>
                  <View style={[styles.headerStyle]}>
                    <View
                      style={[
                        styles.headerUpperContainerStyle,
                        {
                          width:
                            isEmpty(obsrvationData) && windowHeight < windowWidth
                              ? '90%'
                              : isEmpty(obsrvationData) && windowHeight > windowWidth
                                ? '95%'
                                : '100%',
                        },
                      ]}
                    >
                      <View style={{ flexDirection: 'row', marginStart: 15 }}>
                        {!isEmpty(obsrvationData) && (
                          <LinearGradientButton
                            mode="contained"
                            btnStyle={{ width: windowWidth * 0.14 }}
                            contentStyles={{ paddingVertical: 12 }}
                            fontSize={'xxtrasmall'}
                            title={'end study'}
                            onPress={() => {
                              endStudy();
                            }}
                          />
                        )}
                      </View>

                      {obsStart ? (
                        <View
                          style={[
                            styles.timerView,
                            {
                              marginRight:
                                !isEmpty(obsrvationData) && windowWidth > windowHeight ? 15 : 0,
                              marginLeft:
                                !isEmpty(obsrvationData) && windowWidth > windowHeight
                                  ? 0
                                  : !isEmpty(obsrvationData)
                                    ? 0
                                    : 105,
                            },
                          ]}
                        >
                          <AText
                            fontWeight={FontStyle.fontBold}
                            styleText={{ color: '#26356C' }}
                            fontSize={'jumbo'}
                          >
                            {displayElementTimer()}
                          </AText>
                        </View>
                      ) : null}
                      <View style={styles.timerContainerStyle}>
                        <IconButton
                          bgColor={colors.secondary}
                          btnStyle={styles.photoBtnStyle}
                          disabled={
                            cameraDisable || (studyPage == 'RateNFrequencyScreen' && ECclick)
                          }
                          mode="contained"
                          icon={
                            <Ionicons
                              name="camera-outline"
                              style={{ alignSelf: 'center' }}
                              color={colors.primary}
                              size={25}
                            />
                          }
                          onPress={() => {
                            Keyboard.dismiss();
                            openCamera();
                          }}
                        />
                        <IconButton
                          bgColor={colors.secondary}
                          btnStyle={styles.noteBtnStyle}
                          mode="contained"
                          icon={
                            <CommunityIcon
                              name="clipboard-text-outline"
                              style={{ alignSelf: 'center' }}
                              color={colors.primary}
                              size={24}
                            />
                          }
                          onPress={() => {
                            setNoteText(ElementNote);
                            setNoteModal(true);
                            setTimeout(() => {
                              setKeyBoardFocus(true);
                            }, 400);
                          }}
                        />
                      </View>
                    </View>
                    {studyPage === 'element' && (
                      <View
                        style={{
                          flexDirection: 'row',
                          alignSelf: 'center',
                        }}
                      >
                        {showYESElement && (
                          <AButton
                            mode="contained"
                            bgColor={'#A2C73B'}
                            btnStyle={{ ...styles.countBtnStyle, width: windowWidth * 0.24 }}
                            fontSize={'small'}
                            title={'YES'}
                            onPress={() => {
                              selectYESorNO(showYESElement);
                            }}
                          />
                        )}
                        {showNOElement && (
                          <AButton
                            mode="contained"
                            bgColor={'#00BFF3'}
                            btnStyle={{ ...styles.countBtnStyle, width: windowWidth * 0.24 }}
                            fontSize={'small'}
                            title={'NO'}
                            onPress={() => {
                              selectYESorNO(showNOElement);
                            }}
                          />
                        )}
                      </View>
                    )}
                  </View>
                  {studyPage !== 'role' || studyPage !== 'area' ? (
                    <View style={{ zIndex: 1, marginBottom: 25 }}>
                      <BreadCrumb
                        navigation={navigation}
                        studyPage={studyPage}
                        navigateToStudyPage={(val) => navigateToStudyPage(val, true)}
                        ratings={rating}
                      />
                    </View>
                  ) : null}
                </>
              )}

              <ScrollView
                nestedScrollEnabled={true}
                keyboardShouldPersistTaps={'always'}
                refreshControl={
                  studyPage == 'role' ||
                  studyPage == 'area' ||
                  studyPage == 'task' ||
                  studyPage == 'element' ? (
                    <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
                  ) : null
                }
                ref={scrollViewRef}
                onContentSizeChange={() =>
                  scrollToBottom ? scrollViewRef.current.scrollToEnd({ animated: true }) : ''
                }
                showsVerticalScrollIndicator={false}
                showsHorizontalScrollIndicator={false}
                style={{ flexGrow: 1, flex: 1, backgroundColor: colors.onSurface }}
                contentContainerStyle={{
                  paddingTop: 25,
                  paddingVertical: 5,
                  paddingBottom: 100,
                  paddingHorizontal: 7,
                  flexGrow: 1,
                  alignItems: 'center',
                }}
              >
                {children}
              </ScrollView>
            </SafeAreaView>
          </Pressable>
          <BottomModal
            height={windowHeight > 1000 ? windowHeight * 0.25 : windowHeight * 0.32}
            modalShow={cancelStudyModal ? true : cancelModalTwo ? true : false}
            confirmPress={() => {
              isEmpty(obsrvationData) && cancelStudyModal
                ? (setCancelStudyModal(false), setCancelModalTwo(true))
                : stopStudy();
            }}
            cancelShow={() =>
              cancelStudyModal ? setCancelStudyModal(false) : setCancelModalTwo(false)
            }
            title={
              !isEmpty(obsrvationData) && cancelStudyModal
                ? ALERT_TITLE
                : 'Do you want to cancel this study?'
            }
            body={
              !isEmpty(obsrvationData) && cancelStudyModal
                ? STUDY_END_MESSAGE
                : STUDY_CANCELING_MESSAGE2
            }
            confirm={!isEmpty(obsrvationData) && cancelStudyModal ? 'Yes' : 'Cancel Study'}
            clrBody={
              !isEmpty(obsrvationData) && cancelStudyModal && dark
                ? '#fff'
                : !isEmpty(obsrvationData) && cancelStudyModal
                  ? '#777778'
                  : 'red'
            }
            reject={!isEmpty(obsrvationData) && cancelStudyModal ? 'No' : 'Continue Study'}
            ButtonContainer={cancelModalTwo ? { flexDirection: 'row-reverse' } : {}}
          />

          <BottomModalUI
            width={'100%'}
            closeShow
            // showScroll
            closeModal={() => {
              setKeyBoardFocus(false), setNoteModal(false), isEmpty(ElementNote) && setNoteText('');
            }}
            height={windowHeight > 1000 ? windowHeight * 0.25 : windowHeight * 0.4}
            modalShow={noteModal}
          >
            <View style={[styles.noteModalContainer, { width: windowWidth * 0.84 }]}>
              <AText fontWeight={FontStyle.fontBold} fontSize={'title'} color={'#3C4555'}>
                Add note
              </AText>
              <View style={{ width: windowWidth * 0.21 }}>
                <LinearGradientButton
                  btnStyle={{ width: windowWidth * 0.21 }}
                  contentStyles={{ paddingVertical: 10 }}
                  disabled={isEmpty(noteText)}
                  title={'Add note'}
                  onPress={() => setNoteAction()}
                />
              </View>
            </View>
            <Textinputs
              onerror={false}
              autofocus={keyBoardFocus}
              heading={'Add your notes'}
              placeholder=" "
              maxLength={520}
              textViewInputStyle={{ width: windowWidth * 0.84, alignSelf: 'center' }}
              value={noteText}
              onchange={(val) => {
                setNoteText(val);
              }}
            />
          </BottomModalUI>
          {!isEmpty(showQuestion) && (
            <StudyPoPUP
              question={showQuestion}
              freetext={freetext}
              setFreetext={(val) => setFreetext(val)}
              selectedVal={selectedVal}
              setSelectedVal={(val) => setSelectedVal(val)}
              selectedCheckboxVal={selectedCheckboxVal}
              setselectedCheckboxVal={(val) => setselectedCheckboxVal(val)}
              modalShow={showQuestionPopUP}
              onPhotoSubmit={() => {
                openCamera();
                !showQuestion.answerRequired ? updateAnswer(false) : '';
              }}
              onDismiss={() => {
                showQuestion.answerRequired ? '' : updateAnswer(false);
              }}
              onConfirm={(val) => {
                updateAnswer(true, val);
              }}
            />
          )}
          <StudyPoPUP
            question={{
              title: 'Re Rate',
              text: 'Please re-rate the element',
              answerRequired: false,
            }}
            freetext={freetext}
            setFreetext={(val) => setFreetext(val)}
            selectedVal={selectedVal}
            setSelectedVal={(val) => setSelectedVal(val)}
            selectedCheckboxVal={selectedCheckboxVal}
            setselectedCheckboxVal={(val) => setselectedCheckboxVal(val)}
            modalShow={showRerateModal}
            onDismiss={() => {
              resetFrequency(), setShowRerateModal(false);
            }}
          />
        </>
      )}
    </>
  );
};

export default StudyLayout;

const styles = StyleSheet.create({
  conatinerHeader: {
    flex: 1,
  },
  headerStyle: {
    paddingVertical: Platform.OS == 'ios' ? 15 : 20,
    alignItems: 'center',
    alignSelf: 'flex-end',
    width: '100%',
  },
  headerUpperContainerStyle: {
    flexDirection: 'row',
    // alignSelf: 'flex-end',

    justifyContent: 'space-between',
    paddingBottom: Platform.OS == 'ios' ? 15 : 40,
    alignItems: 'center',
    alignSelf: 'center',
  },
  noteModalContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
    alignSelf: 'center',
    marginTop: windowHeight * 0.015,
  },
  timerView: {
    paddingHorizontal: 30,
    paddingVertical: 19,
    alignSelf: 'center',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
    alignItems: 'center',
    borderRadius: 55,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 1.65,

    elevation: Platform.OS == 'ios' ? 1 : 8,
  },
  timerContainerStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginRight: 15,
    alignContent: 'center',
    alignItems: 'center',
  },
  modalConatiner: {
    backgroundColor: '#fff',
    height: windowHeight * 0.35,
    justifyContent: 'center',
    paddingHorizontal: 5,
  },
  cameraModalStyle: {
    backgroundColor: '#E8E9EA',
    justifyContent: 'center',
    alignSelf: 'center',
    padding: 45,
    marginTop: 20,
  },
  photoBtnStyle: {
    marginEnd: 15,
    borderRadius: 70,
    width: 60,
    height: 60,
    alignSelf: 'center',
  },
  noteBtnStyle: {
    marginEnd: 15,
    borderRadius: 70,
    width: 60,
    height: 60,
    alignSelf: 'center',
  },
  countBtnStyle: {
    borderRadius: 120,
    padding: 20,
    paddingVertical: 20,
    marginHorizontal: 22,
  },
});
