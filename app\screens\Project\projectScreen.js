import { FlatList, StyleSheet, Text, View } from 'react-native';
import React, { useEffect, useState } from 'react';
import { getURL, isEmpty } from '_helpers';
import { ProjectCard } from '_components';
import { useDispatch, useSelector } from 'react-redux';
import { useIsFocused } from '@react-navigation/native';
import { clearOldStudyTitles } from '_provider';
import { FontStyle, PRODUCTION_URL } from '_utils';
import { AText } from '_theme_components';

const ProjectScreen = ({ navigation }) => {
  const { projectList } = useSelector((state) => state.projectReducer);
  const dispatch = useDispatch();
  const isFocused = useIsFocused();

  const navigateToNext = (item) => {
    dispatch({
      type: 'STORE_PROJECT',
      payload: item,
    });
    navigation.navigate('ProjectWrapper', {
      screen: 'Locations',
    });
  };
  useEffect(() => {
    if (isFocused) {
      dispatch({
        type: 'RESET_SUBMITTED',
      });
      clearOldStudyTitles();
    }
  }, [isFocused]);

  const ItemView = React.useCallback(({ item, index }) => {
    return (
      <View key={item.id}>
        <ProjectCard
          url={PRODUCTION_URL}
          navigateTo={() => {
            navigateToNext(item);
          }}
          navigation={navigation}
          data={item}
        />
      </View>
    );
  }, []);
  return (
    <View style={styles.contentContainer}>
      {!isEmpty(projectList) ? (
        <FlatList
          data={projectList}
          showsVerticalScrollIndicator={false}
          showsHorizontalScrollIndicator={false}
          keyExtractor={(key, index) => index.toString()}
          renderItem={ItemView}
        />
      ) : (
        <AText
          styleText={{ textAlign: 'center' }}
          fontSize={'medium'}
          fontWeight={FontStyle.fontBold}
        >
          No project Found{' '}
        </AText>
      )}
    </View>
  );
};

export default ProjectScreen;

const styles = StyleSheet.create({
  contentContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    marginTop: 25,
    marginBottom: 25,
  },
});
