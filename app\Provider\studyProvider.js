import { APP_ID, tables, TOO_MANY_UNSYNCED_STUDIES, UNSYNCED_DATA_ALERT } from '_utils';
import {
  isEmpty,
  getData,
  storeData,
  getAllData,
  getDataByTableAndID,
  getStudies,
  insertIntoTable,
  markStudyAsCancelled,
  dynamicSort,
  getReoverDataByTableAndID,
} from '_helpers';
import { reminderinitForProject } from './reminderProvider';
import { questioninitForProject } from './questionProvider';
import moment from 'moment';

export const fetchRecoverData = async (dbDetail) => {
  let [study, studyData, creatArea, creatRole, creatElement, answerData] = await Promise.all([
    await getReoverDataByTableAndID(tables.STUDY_TABLE, '', dbDetail),
    await getReoverDataByTableAndID(tables.STUDY_DATA_TABLE, '', dbDetail),
    // await getReoverDataByTableAndID(tables.RECOVERY_DATA_TABLE, '', dbDetail),
    await getReoverDataByTableAndID(tables.CREATE_AREA_TABLE, '', dbDetail),
    await getReoverDataByTableAndID(tables.CREATE_ROLE_TABLE, '', dbDetail),
    await getReoverDataByTableAndID(tables.CREATE_ELEMENT_TABLE, '', dbDetail),
    await getReoverDataByTableAndID(tables.ANSWERS_TABLE, '', dbDetail),
  ]);
  let payload = { study, studyData, creatElement, creatRole, creatArea, answerData };
  await storeData('payload', JSON.stringify(payload));
};

export const canStartStudy = async (db, netInfo, userID) => {
  var studies = [];
  studies = await getStudies(db, userID);
  // for (const study of studies) {
  !isEmpty(studies) &&
    studies.map(async (study) => {
      await markStudyAsCancelledIfEmpty(study.id, db);
    });

  //get a fresh copy of studies after checking for cancelled ones
  var observations = [];

  return new Promise(async (resolve) => {
    observations = await getAllData(tables.STUDY_DATA_TABLE, db);
    resolve(true);
  }).then(() => {
    // check if there are any studies that have no observations
    // usually happens when a study's observations
    if (isEmpty(studies)) {
      if (isEmpty(observations)) {
        return { canStart: true };
        // no reason to have observations if no studies present but better to go on
      }
    }

    var hasOfflineEntries = [];
    // if (!isEmpty(observations)) {
    //     hasOfflineEntries = observations.filter(observation => {
    //         if (!isEmpty(observation.element) && !isEmpty(observation.area)) {
    //             return observation.element.indexOf("element") > -1 ||
    //                 observation.area.indexOf("area") > -1
    //         }
    //     });
    // }

    // let studiesWithOfflineRoles = [];
    // if (APP_ID == 2 && !isEmpty(studies)) {
    //     studiesWithOfflineRoles = studies.filter(study => {
    //         if (!study.role) {
    //             return false;
    //         }
    //         return study.role.indexOf("-role") > -1;
    //     });
    // }

    if (!isEmpty(studies) && studies.length >= 15) {
      return { canStart: false, message: TOO_MANY_UNSYNCED_STUDIES };
    }
    // if (netInfo && (!isEmpty(hasOfflineEntries) || !isEmpty(studiesWithOfflineRoles))) {
    //     return { canStart: false, message: UNSYNCED_DATA_ALERT };
    // }
    return { canStart: true };
  });
};
export const markStudyAsCancelledIfEmpty = async (studyID, db) => {
  const observations = await getDataByTableAndID(tables.STUDY_DATA_TABLE, studyID, db);
  if (observations.length === 0) {
    await markStudyAsCancelled(studyID, db);
    return true;
  } else {
    return false;
  }
};
export const saveStudyTitle = async (studyTitle) => {
  let studyTitles = await getData('studyTitles');

  studyTitles = !isEmpty(studyTitles) ? JSON.parse(studyTitles) : [];
  studyTitles.push(studyTitle);
  storeData('studyTitles', JSON.stringify(studyTitles));
};

export const studyStarted = (projectID, dbDetail) => async (dispatch) => {
  let reminders = await reminderinitForProject(projectID, dbDetail);
  let questions = await questioninitForProject(projectID, dbDetail);
  let sortedQuestion = {
    show_on_task_elements: (reminders?.show_on_task_elements ?? []).concat(
      questions?.show_on_task_elements ?? []
    ),
    show_byTime: (reminders?.show_byTime_reminder ?? []).concat(questions?.show_byTime ?? []),
    end_of_study: questions?.end_of_study ?? [],
  };
  let sortby = sortedQuestion.show_byTime;
  sortby.sort(dynamicSort('time', true));
  sortedQuestion.show_byTime = sortby;
  setTimeout(() => {
    dispatch({
      type: 'SAVE_QUESTION_LIST',
      payload: sortedQuestion,
    });
  }, 500);
};
export const saveStudyData = async (dbDetail) => {
  let addData = await insertIntoTable(tables.STUDY_TABLE, [1], dbDetail);
  return addData;
};
export const getStudyData = (dbDetail) => {
  let getData = getAllData(tables.STUDY_TABLE, dbDetail);
  return getData;
};

export const removeStudyTitle = async (studyTitle) => {
  let studyTitles = (await getData('studyTitles')) || [];
  studyTitles = !isEmpty(studyTitles) ? JSON.parse(studyTitles) : [];
  let studyTitleIndex = studyTitles.indexOf(studyTitle);

  if (studyTitleIndex == -1) {
    return;
  }
  studyTitles.splice(studyTitleIndex, 1);

  storeData('studyTitles', JSON.stringify(studyTitles));
  return;
};

export const clearOldStudyTitles = async () => {
  let studyTitles = (await getData('studyTitles')) || [];
  studyTitles = !isEmpty(studyTitles) ? JSON.parse(studyTitles) : [];

  if (!studyTitles) {
    return;
  }

  const date = moment().format('DDMM');
  studyTitles = studyTitles.filter((title) => title.indexOf(date) != -1);

  storeData('studyTitles', JSON.stringify(studyTitles));
};

/* GETTING OFFLINE DATA AND SYNCING IT TO THE SERVER */
export const getSyncExclamation = (dbDetail, userDetailsId) => async (dispatch) => {
  try {
    const result = await getStudies(dbDetail, userDetailsId, true);
    const data = JSON.parse(JSON.stringify(result));

    // Use Promise.all to process all elements in data array concurrently
    const filteredData = await Promise.all(
      data
        .map(async (element) => {
          const res = await markStudyAsCancelledIfEmpty(element.id, dbDetail);
          return res ? null : element;
        })
        .filter(Boolean)
    ); // Use filter to remove null entries

    // Dispatch action based on the length of filteredData
    const actionType = filteredData.length > 0 ? 'SHOW_EXCLAMATION' : 'HIDE_EXCLAMATION';
    dispatch({ type: actionType });
  } catch (error) {
    dispatch({
      type: 'SYNCDATA_LOADING_FALSE',
    });
  }
};
