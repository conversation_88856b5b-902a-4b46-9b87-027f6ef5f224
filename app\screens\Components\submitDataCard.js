import React from 'react';
import { View } from 'react-native';
import { Card, useTheme } from 'react-native-paper';
import { AButton, AText } from '_theme_components';
import LinearGradient from 'react-native-linear-gradient';
import Ionicons from 'react-native-vector-icons/Ionicons';
import CommunityIcon from 'react-native-vector-icons/MaterialCommunityIcons';
import { FontStyle, LINEAR_GRADIENT_RATING_BUTTON } from '_utils';
import { styles } from './styles';
import { isEmpty } from '_helpers';

const SubmitDataCard = ({ item, selected, handleselectstudy, navigateNextTo }) => {
  const { colors, dark } = useTheme();
  const {
    startTime,
    continuesObservation,
    elementName,
    frequency,
    areaName,
    taskName,
    notes,
    photo,
    rating,
  } = item;

  const itemArray = [
    {
      id: 1,
      type: 'element',
      name: elementName,
      style: [
        styles.textContainer,
        {
          width: '20%',
          paddingEnd: 5,
        },
      ],
    },
    {
      id: 2,
      type: 'rating',
      name: rating,
      style: [
        styles.textContainer,
        {
          width: '10%',
          paddingStart: 5,
        },
      ],
    },
    {
      id: 2,
      type: 'frequency',
      name: frequency,
      style: [
        styles.textContainer,
        {
          width: '8%',
          // alignItems: 'center',
          paddingStart: 5,
        },
      ],
    },
    {
      id: 2,
      type: 'area',
      name: areaName,
      style: [
        styles.textContainer,
        {
          width: '18%',
        },
      ],
    },
    {
      id: 2,
      type: 'task',
      name: taskName,
      style: [
        styles.textContainer,
        {
          width: '20%',
          paddingStart: 5,
        },
      ],
    },
    {
      id: 2,
      type: 'notes',
      name: notes,
      icon: (
        <CommunityIcon
          name="clipboard-text-outline"
          style={{ alignSelf: 'center' }}
          color={colors.primary}
          size={28}
        />
      ),
      style: [
        styles.textContainer,
        {
          width: '5%',
        },
      ],
    },
    {
      id: 2,
      type: 'photo',
      icon: (
        <Ionicons
          name="camera-outline"
          style={{ alignSelf: 'center' }}
          color={colors.primary}
          size={28}
        />
      ),
      name: photo,
      style: [
        styles.textContainer,
        {
          width: '5%',
        },
      ],
    },
  ];
  return (
    <>
      <Card
        activeOpacity={1}
        style={[styles.submitCardstyle, { borderColor: selected ? '#00C0F3' : '#fff' }]}
        onPress={navigateNextTo}
      >
        <LinearGradient
          colors={
            selected
              ? LINEAR_GRADIENT_RATING_BUTTON
              : dark
                ? [colors.onSurface, colors.onSurface]
                : ['#fff', '#fff']
          }
          start={{ x: 0.1, y: 0.3 }}
          end={{ x: 0.5, y: 0.7 }}
          style={[styles.slectedContainer, { borderRadius: 10 }]}
        >
          <AButton
            onPress={handleselectstudy}
            mode="text"
            styleText={{ color: dark ? '#fff' : '#000' }}
            btnStyle={styles.timeContainer}
            fontSize={'small'}
            fontWeight={FontStyle.fontRegular}
            title={new Date(startTime).toLocaleTimeString().replace(/:[^:]*$/, '')}
          />
          {itemArray.map(({ type, name, style, icon }) => (
            <View style={[style, { justifyContent: type === 'element' ? 'flex-start' : 'center' }]}>
              {(type == 'photo' || type == 'notes') && name ? (
                icon
              ) : (
                <>
                  <AText fontSize={'small'}>{name ?? ''}</AText>
                  {type === 'element' && !isEmpty(continuesObservation) && (
                    <View style={[styles.ecViewContainer, { backgroundColor: colors.primary }]}>
                      <AText styleText={{ color: '#fff' }} fontSize={'xtrasmall'}>
                        EC
                      </AText>
                    </View>
                  )}
                </>
              )}
            </View>
          ))}
        </LinearGradient>
      </Card>
    </>
  );
};

export default SubmitDataCard;
