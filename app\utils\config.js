import { Alert, Dimensions, PixelRatio, Platform } from 'react-native';
//export const SERVER_URL = 'http://retime-dev.herokuapp.com/';
import DeviceInfo from 'react-native-device-info';
import { ALERT_ERROR } from '../store/reducers/alert';
import { DefaultTheme, DarkTheme } from 'react-native-paper';

//export const SERVER_URL = 'http://retime-webapp.herokuapp.com/';
export let windowHeight = Dimensions.get('window').height;
export let windowWidth = Dimensions.get('window').width;

export const changeWindowHeightWidth = (height, width) => {
  windowHeight = height ?? Dimensions.get('window').height;
  windowWidth = width ?? Dimensions.get('window').width;
};

export const changeDevlopmentServer = (IPAddresss) => {
  DEVELOPMENT_URL = IPAddresss;
};

export const APP_PRIMARY_COLOR = '#CFE8C8';
export const APP_SECONDARY_COLOR = '#266F42';
export let DEV_URL = 'https://retime-webapp.herokuapp.com/';
export let DEVELOPMENT_URL = 'http://************:8000/';
export const STAGING_URL = 'https://dashboard.retime.co.uk/';
export const PRODUCTION_URL = 'https://dashboard.retime.co.uk/';
export const DEVLOPMENT_SERVER = true;
export const VERSION = DeviceInfo.getVersion();
export const reTimeTheme = {
  ...DefaultTheme,
  dark: false,
  colors: {
    ...DefaultTheme.colors,
    primary: '#0C9FEF',
    accent: '#0C9FEF',
    secondary: '#CCF2FD', //'rgba(228, 220, 251)',
    onSurface: '#EBEFF0',
  },
};
export const reTimeDarkTheme = {
  ...DarkTheme,
  dark: true,
  colors: {
    ...DarkTheme.colors,
    primary: '#0C9FEF',
    accent: '#0C9FEF',
    secondary: '#CCF2FD',
    //'#E4DCFB'
    onSurface: '#3C4556',
  },
};

export const DATABASE_CONFIG = {
  name: 'retime_dots_v3.db',
  location: 'default',
};
export const APP_ID = 3;
export const DEBUG_BUILD = false;
export const FORCE_OFFLINE = false;
export const STUDY_END_WINDOW = 60000;
export let SESSION_KEY = '';
export const ENABLE_REPLATFORM_API = false;

export let LINEAR_GRADIENT_RATING_BUTTON = ['rgba(117, 211, 241, 1)', 'rgba(147, 212, 231,0.9)'];
export const EMAIL_REGEXP =
  /^(([^<>()\[\]\\.,;:\s@']+(\.[^<>()\[\]\\.,;:\s@']+)*)|('.+'))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
export const PLEASE_WAIT = 'Please wait...';
export const PLEASE_WAIT_MESSAGE = 'Please wait...';
export const ERROR = 'SOMETHING WENT WRONG.';
export const NO_DATA_FOUND = 'Sorry! No Data  Found.';
export const NO_CATEGORY_FOUND = 'Sorry! No Category found to create new element.';
export const STUDY_START_TOAST = 'New Study has started';
export const NOTES_ALERT_TITLE = 'STUDY NOTES';
export const NOTES_ALERT_MESSAGE = 'Please enter some notes for this Study.';
export const FILE_UPLOADED_MESSAGE = 'Study Photo uploaded successfully.';
export const FILE_SAVED_LOCALLY =
  'Study Photo saved successfully in local data base and will be synced when Internet is available.';
export const OFFLINE_STUDY_DATA_MSG = 'Study Data will be saved when internet is available.';
export const INTERNET_ERROR = 'Sorry! No Internet connection available right now.';
export const SLOW_INTERNET_ERROR =
  'We are unable to pull the fresh data. It might be due to Slow Internet Connection.';
export const IMAGE_UPLOADING_ERROR =
  "Sorry! Image couldn't be uploaded. It might be due to poor internet connection.";
export const IMAGE_CAPTURE_ERROR = "Sorry! Image couldn't be capture. Please try again.";
export const TRY_AGAIN_MSG =
  'Please try to connect to a good internet connection and try again by pulling down the page.';
export const SYNC_DONE = 'All the data synced successfully.';
export const SYNC_DATA_MSG = 'Syncing Data...';
export const STUDY_END_MESSAGE = 'Are you sure that you want to end this Study ?';
export const STUDY_CANCELING_MESSAGE =
  'Are you sure that you want to cancel this Study? (By cancelling, all the study data will be erased).';
export const STUDY_CANCELING_MESSAGE2 =
  'All data for this study will be deleted if you press cancel.';
export const ALERT_TITLE = 'Activity Study';
export const BACK_BTN_MESSAGE = 'Press back again to exit the App.';
export const REMOVING_STUDY_ITEMS_MSG = 'Are you sure that you want to remove all Study Itmes ?';
export const ROUND_ENDED = 'Round has now ended';
export const STUDY_ENDED = 'Study has now ended';
export const DELETE_TITLE = 'Delete Item';
export const DELETE_PHOTO_MSG = 'Are you sure that you want to delete all photo ?';
export const DELETE_MSG = 'Are you sure that you want to delete this Item ?';
export const FREQUENCY_INPUT_ERROR = 'Only positive numbers are allowed';
export const ENTRY_ALREADY_EXIST = 'An entry with the same name already exists.';
export const SESSION_EXPIRED = 'Please login again.';
export const UNSYNCED_DATA_ALERT = 'Please sync study data before starting a new Study.';
export const TOO_MANY_UNSYNCED_STUDIES =
  'You have many unsynced studies. Please sync them before starting a new study.';
export const NO_ACCESS_MSG = "Sorry! you don't have access to this app.";
export const NOT_RATED_ELEMENTS_MESSAGE =
  'User can not add frequency for Disable Elements or Disable Projects.';
export const sync_studies = 'Sync Studies';
export const resync_confirmation = `These studies are already synced.Syncing them again might create duplicates.Proceed?`;
export const TOKEN_EXPIRED = 'Your token has expired. Please log in again.';
export const TOKEN_EXPIRED_LOGIN_AGAIN =
  'Your session has expired. Please log in again after completing your study. For now, the image is saved in local data.';

/* TABLE NAMES  */
export let PROJECTS_TABLE = 'Projects';
export let LOCATIONS_TABLE = 'Locations';
export let ASSIGNED_LOCATIONS_TABLE = 'assignedLocations';
export let ROLES_TABLE = 'Roles';
export let CREATE_ROLE_TABLE = 'CreateRole';
export let AREAS_TABLE = 'Areas';
export let TASKS_TABLE = 'Tasks';
export let CREATE_AREA_TABLE = 'CreateArea';
export let ELEMENTS_TABLE = 'Elements';
export let CREATE_ELEMENT_TABLE = 'CreateElement';
export let CATEGORIES_TABLE = 'Categories';
export let STUDY_TABLE = 'Study';
export let STUDY_DATA_TABLE = 'StudyData';
export let RECOVERY_DATA_TABLE = 'RecoverStudyData';
export let PROFILE_UPDATED = 'Profile updated successfully.';
export let PASSWORD_CHANGED = 'Password changed successfully.';

export const tables = {
  PROJECTS_TABLE: 'Projects',
  LOCATIONS_TABLE: 'Locations',
  ASSIGNED_LOCATIONS_TABLE: 'assignedLocations',
  ROLES_TABLE: 'Roles',
  CREATE_ROLE_TABLE: 'CreateRole',
  AREAS_TABLE: 'Areas',
  TASKS_TABLE: 'Tasks',
  CREATE_AREA_TABLE: 'CreateArea',
  ELEMENTS_TABLE: 'Elements',
  CREATE_ELEMENT_TABLE: 'CreateElement',
  CATEGORIES_TABLE: 'Categories',
  STUDY_TABLE: 'Study',
  // TODO: rename to Observations
  STUDY_DATA_TABLE: 'StudyData',
  GROUPS_TABLE: 'Groups',
  REMINDERS_TABLE: 'Reminders',
  QUESTIONS_TABLE: 'Questions',
  ANSWERS_TABLE: 'Answers',
  RECOVERY_DATA_TABLE: 'RecoverStudyData',
  ERROR_TABLE: 'ErrorData',
};

// firebase details
export const firebaseConfig = {
  apiKey: 'AIzaSyCzQ0qJWDV12rs1jbumNogPCq9StGXv6MQ',
  authDomain: 'activity-study.firebaseapp.com',
  projectId: 'activity-study',
  storageBucket: 'activity-study.appspot.com',
  messagingSenderId: '858777983955',
  appId: '1:858777983955:web:9ff2656551fa6c19d8ed26',
  measurementId: 'G-SGXRXH8XV2',
};

export const FontStyle = {
  fontBlack: 'NeoSansStdBlack',
  fontBold: 'NeoSansStdBold',
  fontMedium: 'NeoSansStdMedium',
  fontRegular: 'NeoSansStdRegular',
};

export const normalizeSize = (size, multiplier = 2) => {
  // const scale = (windowWidth / windowHeight) * multiplier;
  // const newSize = size * scale;
  const newSize = (windowWidth * size) / 100;
  return Math.round(PixelRatio.roundToNearestPixel(newSize));
  // return size
};

export const getFontSize = (fSize) => {
  const isTablet = DeviceInfo.isTablet(); //check if device is Tablet
  const genericFontSizes = {
    Heading: 30,
    xtralarge: 25,
    jumbo: 25,
    homeTitle: 25,
    title: 20,
    large: 18,
    medium: 15,
    small: 12,
    xtrasmall: 10,
    xxtrasmall: 8,
  };
  const specificPlatformFontSizes = {
    Heading: { android: 48, ios: 45 },
    xtralarge: { android: 42, ios: 40 },
    jumbo: { android: 37, ios: 35 },
    homeTitle: { android: 35, ios: 32 },
    title: { android: 32, ios: 30 },
    large: { android: 24, ios: 22 },
    medium: { android: 20, ios: 18 },
    small: { android: 15, ios: 14, default: 12 },
    xtrasmall: { android: 12, ios: 10, default: 10 },
    xxtrasmall: { android: 10, ios: 9, default: 8 },
  };
  const calculateDefaultFontSize = () => {
    const newSize = windowWidth * 0.03;
    return Math.round(PixelRatio.roundToNearestPixel(newSize));
  };
  const fontSize = isTablet
    ? specificPlatformFontSizes[fSize]?.[Platform.OS]
    : genericFontSizes[fSize];
  return fontSize ? fontSize : calculateDefaultFontSize();
};

export const handleError = (error) => (dispatch) => {
  if (error.code == 11000) {
    dispatch({
      type: ALERT_ERROR,
      payload: ENTRY_ALREADY_EXIST,
    });
  } else if ([990, 991, 992, 993, 994, 995, 996].indexOf(error.code) !== -1) {
    if (error.message) {
      dispatch({
        type: ALERT_ERROR,
        payload: error.message || 'Something went wrong. Please try again later.',
      });
    } else {
      if (error.code === 994) {
        dispatch({
          type: ALERT_ERROR,
          payload: SESSION_EXPIRED || 'Something went wrong. Please try again later.',
        });
      } else if (error.code === 996) {
        dispatch({
          type: ALERT_ERROR,
          payload: NO_ACCESS_MSG || 'Something went wrong. Please try again later.',
        });
      } else {
        dispatch({
          type: ALERT_ERROR,
          payload: ERROR || 'Something went wrong. Please try again later.',
        });
      }
    }
    // this.auth.logOut();
  } else {
    dispatch({
      type: ALERT_ERROR,
      payload: ERROR || 'Something went wrong. Please try again later.',
    });
  }
};

export const localimage = {
  logo: require('../assets/images/logo.png'),
  trending: require('../assets/images/trending-up.png'),
  emptydata: require('../assets/images/no-search-data.png'),
  keys: require('../assets/images/keys.png'),
  splash: require('../assets/splash.png'),
  splashLandscape: require('../assets/splash-landscape.jpg'),
  nounsync: require('../assets/images/no-unsync.png'),
  menu: require('../assets/images/menu.png'),
  dots: require('../assets/images/dot.png'),
  submitteddata: require('../assets/images/submitted-data.png'),
  submittingdata: require('../assets/images/submitting-data.png'),
  submittingdataFailed: require('../assets/images/submitting-data-failed.png'),
  sort: require('../assets/images/sort.png'),
  stopwatch: require('../assets/images/stopwatch.png'),
  clockfinal: require('../assets/images/clock-final.png'),
  question: require('../assets/images/question.png'),
  reminder: require('../assets/images/reminder.png'),
  blackbg: require('../assets/images/black-bg.png'),
  whitebg: require('../assets/images/white-bg.png'),
  flash: require('../assets/images/flash.png'),
  flashoff: require('../assets/images/flash-off.png'),
  flashauto: require('../assets/images/auto-flash.png'),
  cameraflip: require('../assets/images/camera-flip.png'),
};
