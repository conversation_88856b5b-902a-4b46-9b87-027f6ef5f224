import { StyleSheet, Pressable, View } from 'react-native';
import React from 'react';
import { AText } from '_theme_components';
import { FontStyle } from '_utils';
import Icon from 'react-native-vector-icons/Feather';
import { useTheme } from 'react-native-paper';
import { CustomSwitch, isEmpty } from '_helpers';

const StudyNextCard = ({
  data,
  navigateNext,
  icon,
  onToggleSwitch,
  ShowSwitch,
  taskGroup,
  taskGroupElement,
  lasttask,
  ShowTasks,
  controllingElementID,
  ShowEC,
  width,
  selectedElement,
}) => {
  const { dark } = useTheme();

  return (
    <>
      {taskGroup ? (
        <View style={[styles.containerStyle, { marginVertical: 0, marginTop: 20 }]}>
          <Pressable
            activeOpacity={0.9}
            onPress={() => navigateNext()}
            style={[
              styles.cardStyle,
              {
                backgroundColor: dark ? 'rgba(0,0,0,0.2)' : 'rgba(255,255,255,0.9)',
                borderWidth: 1,
                borderColor: dark ? '#fff' : '#d1cfcf',
                width: '100%',
                borderRadius: ShowTasks ? 0 : 10,
                borderTopLeftRadius: 10,
                borderTopRightRadius: 10,
              },
            ]}
          >
            <View style={styles.taskgroupView}>
              <Icon
                name={'folder'}
                color={dark ? 'rgba(255,255,255,0.7)' : 'rgba(0,0,0,0.5)'}
                size={30}
              />
              <AText
                styleText={{ paddingHorizontal: 5 }}
                fontWeight={FontStyle.fontRegular}
                fontSize={'medium'}
              >
                {data.name}
              </AText>
            </View>

            <Icon
              name={ShowTasks ? 'chevron-up' : 'chevron-down'}
              color={dark ? 'rgba(255,255,255,0.7)' : 'rgba(0,0,0,0.5)'}
              size={22}
            />
          </Pressable>
        </View>
      ) : taskGroupElement ? (
        <View
          key={data.id + Math.random() + data._id}
          style={[styles.containerStyle, { marginVertical: 0 }]}
        >
          <Pressable
            activeOpacity={0.9}
            onPress={() => navigateNext()}
            style={[
              styles.groupCardStyle,
              {
                borderBottomEndRadius: lasttask ? 10 : 0,
                borderBottomStartRadius: lasttask ? 10 : 0,
                backgroundColor: dark ? 'rgba(0,0,0,0.2)' : 'rgba(255,255,255,0.9)',
                borderColor: dark ? '#fff' : '#d1cfcf',
                width: '100%',
              },
            ]}
          >
            <AText fontWeight={FontStyle.fontRegular} fontSize={'medium'}>
              {data.name}
            </AText>
            <Icon
              name={icon ?? 'arrow-right'}
              color={dark ? 'rgba(255,255,255,0.7)' : 'rgba(0,0,0,0.5)'}
              size={22}
            />
          </Pressable>
        </View>
      ) : (
        <View
          style={[
            styles.containerStyle,
            { width: width ?? '85%' },
            selectedElement && { opacity: 0.4 },
          ]}
        >
          <Pressable
            activeOpacity={0.9}
            onPress={() => (selectedElement ? '' : navigateNext(false))}
            style={[
              styles.cardStyle,
              {
                backgroundColor: controllingElementID
                  ? '#8F96B3'
                  : dark
                    ? 'rgba(0,0,0,0.2)'
                    : 'rgba(255,255,255,0.9)',
                borderWidth: dark ? 1 : 0.8,
                borderColor: dark ? '#fff' : '#d1cfcf',
                width: data.canContinueElement && ShowEC ? '82%' : '100%',
              },
            ]}
          >
            <AText
              fontWeight={FontStyle.fontRegular}
              styleText={{ width: ShowSwitch ? '70%' : '90%' }}
              fontSize={'medium'}
            >
              {data.name}
            </AText>
            {ShowSwitch ? (
              <CustomSwitch
                option1={'ON'}
                option2={'OFF'}
                selected={data.visible}
                onSelectSwitch={() => {
                  onToggleSwitch(data);
                }}
              />
            ) : !(data.canContinueElement && ShowEC) || (!isEmpty(icon) && icon == 'target') ? (
              <Icon
                name={icon ?? 'arrow-right'}
                color={dark ? 'rgba(255,255,255,0.7)' : 'rgba(0,0,0,0.5)'}
                size={22}
              />
            ) : null}
          </Pressable>
          {data.canContinueElement && ShowEC && (
            <Pressable
              key={data.id}
              onPress={() => (selectedElement ? '' : navigateNext(true))}
              style={[
                styles.ECcardStyle,
                {
                  backgroundColor: controllingElementID
                    ? '#8F96B3'
                    : dark
                      ? 'rgba(0,0,0,0.2)'
                      : 'rgba(255,255,255,0.9)',
                  borderWidth: dark ? 1 : 0.8,
                  borderColor: dark ? '#fff' : '#d1cfcf',
                },
              ]}
            >
              <AText fontWeight={FontStyle.fontRegular} fontSize={'medium'}>
                EC
              </AText>
            </Pressable>
          )}
        </View>
      )}
    </>
  );
};

export default StudyNextCard;

const styles = StyleSheet.create({
  containerStyle: {
    flexDirection: 'row',
    width: '85%',
    alignSelf: 'center',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginVertical: 7,
    borderRadius: 10,
  },
  taskgroupView: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
  },
  cardStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 15,
    paddingVertical: 23,
    alignItems: 'center',
    borderRadius: 10,
    height: '100%',
  },
  groupCardStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 15,
    paddingVertical: 23,
    alignItems: 'center',
    borderRadius: 0,
    borderWidth: 0.9,
  },
  ECcardStyle: {
    flexDirection: 'row',
    justifyContent: 'center',
    paddingHorizontal: 18,
    paddingVertical: 24,
    height: '100%',
    width: '16%',
    alignItems: 'center',
    borderRadius: 10,
  },
});
