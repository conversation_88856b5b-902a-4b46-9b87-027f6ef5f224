import { FlatList, Platform, ScrollView, StyleSheet, View } from 'react-native';
import React, { useContext, useEffect, useRef, useState } from 'react';
import { useTheme } from 'react-native-paper';
import { useDispatch, useSelector } from 'react-redux';
import Icon from 'react-native-vector-icons/Entypo';

import { AText, AuthLoading, FrequencyPanel, RatingPanel, StudyNextCard } from '_theme_components';
import { FontStyle, windowHeight, windowWidth } from '_utils';
import { isEmpty } from '_helpers';
import { isYesOrNo } from '_provider';
import { AppContext } from '../../../hooks/TimeContext';

const RateNFrequencyScreen = ({
  navigation,
  navigateScreen,
  ratingSlect,
  setRatingsAndFreq,
  setFreqInput,
  freqInput,
  selectedElementIndex,
  CanElementContinue,
  reRatingPromptTime,
}) => {
  const { colors, dark } = useTheme();
  const dispatch = useDispatch();
  const { timerValue } = useContext(AppContext);
  const { reRate, projectSelect, taskSelect, elementSelect } = useSelector(
    (state) => state.createStudy
  );
  const { controllingElementID } = taskSelect;
  const [reRatings, setReRatings] = useState([100]);
  const [elementData, setElementData] = useState([]);
  const [loader, setLoader] = useState(false);
  // const isCount = !elementSelect.count;
  const isCount = elementSelect.count === 1 ? true : false;
  const ratingShow = projectSelect.rating && elementSelect.rating === 3;
  const { elementList } = useSelector((state) => state.serverReducer);
  const scrollViewRef = useRef(null);
  const itemRefs = useRef([]);
  const [reRateTrigerTime, setReRateTrigerTime] = useState(reRatingPromptTime + timerValue);

  useEffect(() => {
    if (elementList) {
      setElementData(elementList);
    }
  }, [selectedElementIndex]);

  useEffect(() => {
    if (!isCount && timerValue === reRateTrigerTime && projectSelect.rerateElement) {
      setReRatings((reRatings) => [...reRatings, 100]);

      var reRatearr = [];
      if (projectSelect.rerateElement && reRatings.length > 0) {
        reRatearr = [...reRatings, 100];
        const totalRatings = reRatearr.reduce((sum, reRate) => {
          return sum + reRate;
        }, 0);
        const averageRating = Math.round(totalRatings / reRatearr.length);

        ratingVal = averageRating > 0 ? averageRating : ratingVal;
      }
      setRatingsAndFreq(100, ratingVal, isCount);

      let time = timerValue + reRatingPromptTime;
      setReRateTrigerTime(time);
    } else if (isCount && timerValue === reRateTrigerTime && projectSelect.rerateElement) {
      var ratings = 100;
      let reRateElement = reRate;
      reRateElement.push(ratings);
      dispatch({
        type: 'SAVE_RERATE',
        payload: reRateElement,
      });

      let totalRatings = reRateElement.reduce((total, element) => total + element, 0);
      var averageRating =
        reRateElement.length === 0 ? 0 : Math.round(totalRatings / reRateElement.length);
      averageRating = reRateElement.length == 1 && averageRating == 0 ? 'Not Rated' : averageRating;
      ratings = ratings == 0 ? 'Not Rated' : ratings;
      const ratingVal = averageRating > 0 && projectSelect.rerateElement ? averageRating : ratings;

      setRatingsAndFreq(100, ratingVal, true);
      let time = timerValue + reRatingPromptTime;
      setReRateTrigerTime(time);
    }
  }, [timerValue]);

  useEffect(() => {
    if (selectedElementIndex) {
      setLoader(true);
      setTimeout(() => {
        scrollToElement(selectedElementIndex);
        setTimeout(() => {
          setLoader(false);
        }, 200);
      }, 800);
    }
  }, []);

  const scrollToElement = (index) => {
    if (index === null || !itemRefs.current[index] || !scrollViewRef.current) {
      console.error(`Invalid reference or index: ${index}`);
      return;
    }

    try {
      itemRefs.current[index].measureLayout(
        scrollViewRef.current,
        (x, y) => {
          if (scrollViewRef.current) {
            scrollViewRef.current.scrollTo({ x: 0, y: y, animated: false });
          }
        },
        (error) => {
          console.error(`MeasureLayout Error at index ${index}:`, error);
        }
      );
    } catch (err) {
      console.error('Error while trying to scroll to element:', err);
    }
  };

  const goToScreen = async (screen, elementContinue = false, element) => {
    CanElementContinue();
    setReRatings([100]);
    setReRateTrigerTime(reRatingPromptTime);
    if (!isYesOrNo(element.name)) {
      elementIndex = elementList.indexOf(element);
      scrollToElement(elementIndex);
    }
    navigateScreen(screen, elementContinue, element);
  };

  const selectRating = (rating) => {
    if (isCount && projectSelect.rerateElement) {
      saveReRate(rating);
    } else {
      saveRating(rating);
    }
  };

  const saveReRate = (rating) => {
    var ratings = rating == 'Not Rated' ? 0 : rating;
    let reRateElement = reRate.slice(0, -1);
    reRateElement.push(ratings);
    dispatch({
      type: 'SAVE_RERATE',
      payload: reRateElement,
    });

    let totalRatings = reRateElement.reduce((total, element) => total + element, 0);
    var averageRating =
      reRateElement.length === 0 ? 0 : Math.round(totalRatings / reRateElement.length);
    averageRating = reRateElement.length == 1 && averageRating == 0 ? 'Not Rated' : averageRating;
    ratings = ratings == 0 ? 'Not Rated' : ratings;
    const ratingVal = averageRating > 0 && projectSelect.rerateElement ? averageRating : ratings;
    setRatingsAndFreq(rating, ratingVal, true);
  };

  /* PARSING ROUND DATA TO NEXT PAGE */
  const saveRating = (rating) => {
    let ratings = rating === 'Not Rated' ? 0 : rating;
    let ratingVal = ratings === 0 ? 'Not Rated' : ratings;
    var reRatearr = [];
    if (projectSelect.rerateElement && reRatings.length > 0) {
      reRatearr = [...reRatings.slice(0, -1), ratings];
      const totalRatings = reRatearr.reduce((sum, reRate) => {
        return sum + reRate;
      }, 0);
      const averageRating = Math.round(totalRatings / reRatearr.length);

      ratingVal = averageRating > 0 ? averageRating : ratingVal;
    }
    setRatingsAndFreq(rating, ratingVal, isCount);
    if (projectSelect.rerateElement) {
      setReRatings((prevState) => [...prevState.slice(0, -1), ratings]);
    }
  };

  const ItemView = ({ item, index }) => {
    return (
      <View
        onStartShouldSetResponder={() => true}
        style={{
          // flex: windowWidth > windowHeight ? 0 : 1,
          width: windowWidth > windowHeight ? '100%' : '98%',
          alignSelf: 'center',
        }}
        ref={(el) => (itemRefs.current[index] = el)}
        key={item.id + index}
      >
        <StudyNextCard
          width={Platform.OS == 'ios' ? '97%' : '85%'}
          selectedElement={selectedElementIndex == index}
          controllingElementID={
            !isEmpty(controllingElementID) && controllingElementID == item._id ? true : false
          }
          data={item}
          ShowEC={true}
          icon={
            !isEmpty(controllingElementID) && controllingElementID == item._id
              ? 'target'
              : 'arrow-right'
          }
          navigateNext={(val) => {
            goToScreen('nextElement', val, item);
          }}
        />
      </View>
    );
  };

  return (
    <>
      {loader ? <AuthLoading /> : null}
      <View
        style={[
          styles.container,
          {
            flexDirection: windowWidth > windowHeight ? 'row' : 'column',
            width: '100%',
          },
        ]}
      >
        <View
          style={[
            styles.ratngNfreqContainer,
            {
              width:
                Platform.OS == 'ios' && windowWidth < windowHeight
                  ? '97%'
                  : windowWidth > windowHeight && !isEmpty(elementData)
                    ? '60%'
                    : windowWidth > windowHeight && isEmpty(elementData)
                      ? '100%'
                      : '92%',
            },
          ]}
        >
          <View
            onStartShouldSetResponder={() => true}
            style={[styles.rateViewcontainer, { opacity: ratingShow ? 1 : 0.2 }]}
          >
            <View
              onStartShouldSetResponder={() => true}
              style={
                ratingShow ? styles.inactiveOverlayStyle : [styles.overlayStyle, { width: '100%' }]
              }
            >
              <View style={[styles.freqDisp, { borderBottomWidth: 0 }]}>
                <AText lightGray fontWeight={FontStyle.fontMedium} fontSize={'medium'}>
                  Select rating
                </AText>
              </View>
              <View style={[styles.rateContainer, {}]}>
                <RatingPanel
                  disableBtn={ratingShow ? false : true}
                  onPress={(val) => selectRating(val)}
                  ratingData={ratingSlect}
                />
              </View>
            </View>
          </View>

          {ratingShow ? null : (
            <View
              onStartShouldSetResponder={() => true}
              style={[styles.overlayTextView, { left: 30 }]}
            >
              <AText error styleText={{ textAlign: 'center' }} fontSize={'small'}>
                <Icon
                  name="info-with-circle"
                  style={{ alignSelf: 'center' }}
                  color={colors.primary}
                  size={15}
                />
                {' Rating is not required for this element.'}
                {isCount ? ' Please enter count.' : ''}
              </AText>
            </View>
          )}
          <View style={styles.verticalLine} />
          <View
            onStartShouldSetResponder={() => true}
            style={[styles.freqViewcontainer, { opacity: !isCount ? 0.2 : 1 }]}
          >
            <View
              onStartShouldSetResponder={() => true}
              style={
                isCount ? styles.inactiveOverlayStyle : [styles.overlayStyle, { width: '100%' }]
              }
            >
              <View style={[styles.freqDisp]}>
                <AText lightGray fontWeight={FontStyle.fontMedium} fontSize={'medium'}>
                  Count: {isEmpty(freqInput) ? '' : freqInput}
                </AText>
                <AText fontWeight={FontStyle.fontMedium} error fontSize={'xtrasmall'}>
                  {elementSelect.unitOfMeasure}
                </AText>
              </View>
              <FrequencyPanel
                disableBtn={!isCount}
                setFreqKeyPressed={() => {}}
                setFreqInput={(val) => {
                  setFreqInput(val);
                }}
                freqInput={freqInput}
                freqKeyPressed
              />
            </View>
          </View>
          {isCount ? null : (
            <View
              onStartShouldSetResponder={() => true}
              style={[styles.overlayTextView, { right: 40 }]}
            >
              <AText error styleText={{ textAlign: 'center' }} fontSize={'small'}>
                <Icon
                  name="info-with-circle"
                  style={{ alignSelf: 'center' }}
                  color={colors.primary}
                  size={15}
                />
                {' Count is not required for this element.'}
                {ratingShow ? ' Please enter rating.' : ''}
              </AText>
            </View>
          )}
        </View>
        {/* <View style={styles.horizontalLine} /> */}

        <View
          onStartShouldSetResponder={() => true}
          style={[
            styles.nextElemtcontainer,
            {
              width: windowWidth > windowHeight ? '40%' : '100%',
            },
          ]}
        >
          {!isEmpty(elementData) && (
            <>
              <View style={styles.nextElemtHeadercontainer}>
                <AText
                  fontWeight={FontStyle.fontBold}
                  styleText={{ color: '#c7c7c7' }}
                  fontSize={'large'}
                >
                  Elements
                </AText>
              </View>

              <ScrollView
                onStartShouldSetResponder={() => true}
                ref={scrollViewRef}
                nestedScrollEnabled={true}
                keyboardShouldPersistTaps={'always'}
                showsVerticalScrollIndicator={false}
                showsHorizontalScrollIndicator={false}
                style={{ height: 350 }}
                contentContainerStyle={{
                  flexGrow: 1,
                  alignItems: 'center',
                }}
              >
                {elementData.map((item, index) => (
                  <ItemView key={index.toString()} item={item} index={index} />
                ))}
              </ScrollView>
            </>
          )}
        </View>
      </View>
    </>
  );
};

export default RateNFrequencyScreen;

const styles = StyleSheet.create({
  container: {
    width: '92%',
    alignSelf: 'center',
    paddingBottom: 45,
  },
  ratngNfreqContainer: {
    width: '92%',
    alignSelf: 'center',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  overlayStyle: {
    position: 'relative',
    backgroundColor: 'rgba(155, 162, 165, 0.4)', //rgb(155 162 165 / 58%)
    // height: '100%',
    zIndex: 1,
    // width: '50%',
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
  },
  inactiveOverlayStyle: {
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
  },
  overlayTextView: {
    alignItems: 'center',
    position: 'absolute',
    alignSelf: 'center',
    width: '40%',
    paddingHorizontal: 10,
    backgroundColor: '#fff',
    padding: 9,
    borderRadius: 9,
  },
  verticalLine: {
    height: '92%',
    alignSelf: 'center',
    backgroundColor: '#e2e4e5',
    width: 2,
  },
  horizontalLine: {
    height: 2,
    backgroundColor: '#e2e4e5',
    width: '85%',
    alignSelf: 'center',
  },
  rateViewcontainer: {
    width: '49%',
    paddingHorizontal: 6,
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  rateContainer: {
    width: '100%',
    alignSelf: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    flexWrap: 'wrap',
  },

  freqViewcontainer: {
    width: '49%',
    paddingHorizontal: 6,
    alignItems: 'center',
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  freqDisp: {
    justifyContent: 'flex-end',
    // marginHorizontal: '8%',
    padding: 5,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '80%',
    borderBottomWidth: 1.7,
    borderColor: '#D3D3D3',
  },
  nextElemtcontainer: {
    width: '100%',
    paddingBottom: 18,
    paddingHorizontal: Platform.OS == 'ios' ? 25 : 5,
  },
  nextElemtHeadercontainer: {
    justifyContent: 'flex-start',
    alignSelf: 'center',
    width: Platform.OS == 'ios' ? '97%' : '82%',
    marginStart: 0,
    // marginTop: 20,
  },
});
