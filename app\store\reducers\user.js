import { USER_LOADING, USER_FAIL, UPDATE_USER } from '../actions/userAction';

export const USER = 'USER';
export const DB_DATABASE = 'DB_DATABASE';
const initialState = {
  loading: false,
  userDetails: {},
  dbDetail: {},
  reload: false,
};

export default (state = initialState, action) => {
  switch (action.type) {
    case USER_LOADING:
      return {
        ...state,
        loading: true,
      };
    case USER:
      return {
        ...state,
        userDetails: action.payload,
        loading: false,
      };
    case USER_FAIL:
      return {
        ...state,
        loading: false,
      };
    case UPDATE_USER:
      return {
        ...state,
        loading: false,
      };
    case DB_DATABASE:
      return {
        ...state,
        dbDetail: action.payload,
      };
    case 'USER_LOGOUT':
      return {
        ...state,
        loading: false,
        userDetails: {},
      };
    case 'RELOAD':
      return {
        ...state,
        reload: true,
      };
    case 'RELOAD_FALSE':
      return {
        ...state,
        reload: false,
      };
    default: {
      return state;
    }
  }
};
