import { StyleSheet, Text, View } from 'react-native';
import React from 'react';
import { AText, BottomModalUI, IconButton, LinearGradientButton } from '_theme_components';
import { FontStyle, windowHeight, windowWidth } from '_utils';
import { isEmpty } from '_helpers';
import { useTheme } from 'react-native-paper';
import FeatherIcon from 'react-native-vector-icons/Feather';

const EditStudyModal = ({
  closeModal,
  editModalShow,
  selectedObs,
  editElementData,
  updateEditElement,
  onIconPress,
}) => {
  const { colors, dark } = useTheme();

  const FieldTODisplay = [
    { id: 4, name: 'Rating', value: 'rating' },
    { id: 2, name: 'Task', value: 'taskName' },
    { id: 1, name: 'Area', value: 'areaName' },
    { id: 3, name: 'Element', value: 'elementName' },
  ];
  return (
    <BottomModalUI
      width={'100%'}
      closeModal={() => closeModal()}
      height={windowWidth > windowHeight ? windowHeight * 0.65 : windowHeight * 0.6}
      modalShow={editModalShow}
      showScroll={true}
      closeShow
    >
      <View style={styles.continer}>
        <AText fontWeight={FontStyle.fontMedium} fontSize={'title'}>
          Edit {selectedObs.length} elements
        </AText>
        <LinearGradientButton
          disabled={
            (isEmpty(editElementData.area) &&
              isEmpty(editElementData.rating) &&
              isEmpty(editElementData.task) &&
              isEmpty(editElementData.element)) ||
            (!isEmpty(editElementData.task) && isEmpty(editElementData.element))
          }
          btnStyle={{ width: windowWidth * 0.25 }}
          contentStyles={{ paddingVertical: 13 }}
          onPress={() => {
            updateEditElement();
          }}
          title={'Save Changes'}
          fontSize={'small'}
        />
      </View>
      <View style={styles.syncCardstyle}>
        {!isEmpty(FieldTODisplay) &&
          FieldTODisplay.map((key) => (
            <View key={Math.random()} style={styles.divstyle}>
              <View style={{ width: '80%' }}>
                <AText
                  fontWeight={FontStyle.fontMedium}
                  styleText={{ color: '#828181' }}
                  fontSize={'medium'}
                >
                  {key.name}
                </AText>
                <AText
                  styleText={{ paddingTop: 5 }}
                  fontWeight={FontStyle.fontBold}
                  fontSize={'medium'}
                >
                  {!isEmpty(editElementData[key.value]) ||
                  (key.value == 'elementName' && !isEmpty(editElementData.task)) ? (
                    <AText
                      styleText={{ paddingTop: 5 }}
                      fontWeight={FontStyle.fontBold}
                      fontSize={'medium'}
                    >
                      {editElementData[key.value]}
                    </AText>
                  ) : (
                    selectedObs.map((item, index) => (
                      <AText
                        styleText={{ paddingTop: 5 }}
                        fontWeight={FontStyle.fontBold}
                        fontSize={'medium'}
                      >
                        {item[key.value]}
                        {selectedObs.length - 1 !== index && ','}
                      </AText>
                    ))
                  )}
                </AText>
              </View>
              {key.value == 'elementName' && isEmpty(editElementData.task) ? null : (
                <IconButton
                  onPress={() => {
                    onIconPress(key.name);
                  }}
                  icon={
                    <FeatherIcon
                      name="edit-3"
                      color={colors.primary}
                      style={styles.iconStyle}
                      size={20}
                    />
                  }
                  btnStyle={{ ...styles.iconBtntyle, borderColor: colors.primary }}
                  bgColor={colors.secondary}
                />
              )}
            </View>
          ))}
      </View>
    </BottomModalUI>
  );
};

export default EditStudyModal;

const styles = StyleSheet.create({
  continer: {
    paddingVertical: 15,
    width: '90%',
    alignSelf: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  syncCardstyle: {
    width: '90%',
    justifyContent: 'center',
    flexDirection: 'column',
    alignItems: 'center',
    alignSelf: 'center',
  },

  divstyle: {
    width: '100%',
    justifyContent: 'space-between',
    flexDirection: 'row',
    paddingVertical: 30,
  },
  iconStyle: {
    alignSelf: 'center',
    marginRight: 0,
  },
  iconBtntyle: {
    marginRight: 10,
    justifyContent: 'center',
    borderRadius: 40,
    height: 60,
    width: 60,
  },
});
