import { executeFetch } from '_helpers';
import { ALERT_ERROR, ALERT_SUCCESS } from '../reducers/alert';
import { LogoutAction } from './loginAction';

export const changePasswordAction = (payload, dbDetail, userID) => (dispatch) => {
  dispatch({
    type: USER_LOADING,
  });
  executeFetch(`user/update/password/${userID}`, 'POST', payload)
    .then(async (response) => {
      if (response.status === 200) {
        dispatch(LogoutAction(dbDetail));
        dispatch({
          type: ALERT_SUCCESS,
          payload: response.data.message ?? response.data.data.message,
        });
        dispatch({
          type: UPDATE_USER,
        });
      } else {
        dispatch({
          type: USER_FAIL,
        });
        dispatch({
          type: ALERT_ERROR,
          payload: response.data.data.message,
        });
      }
    })
    .catch((error) => {
      dispatch({
        type: USER_FAIL,
      });
      dispatch({
        type: ALERT_ERROR,
        payload: 'Something went wrong. Please try again later.',
      });
    });
};
export const updateProfileAction = (payload, userID) => (dispatch) => {
  dispatch({
    type: USER_LOADING,
  });
  // return
  executeFetch(`user/update/${userID}`, 'POST', payload)
    .then(async (response) => {
      if (response.status === 200) {
        dispatch({
          type: ALERT_SUCCESS,
          payload: 'Profile Updated Successfully',
        });
        dispatch({
          type: UPDATE_USER,
        });
      } else {
        dispatch({
          type: USER_FAIL,
        });
        dispatch({
          type: ALERT_ERROR,
          payload: response.data.message,
        });
      }
    })
    .catch((error) => {
      dispatch({
        type: USER_FAIL,
      });
      dispatch({
        type: ALERT_ERROR,
        payload: 'Something went wrong. Please try again later.',
      });
    });
};
export const USER_LOADING = 'USER_LOADING';
export const CHANGE_PASSWORD = 'CHANGE_PASSWORD';
export const CHANGE_PASSWORD_LOGOUT = 'CHANGE_PASSWORD_LOGOUT';
export const UPDATE_USER = 'UPDATE_USER';
export const USER_FAIL = 'USER_FAIL';
