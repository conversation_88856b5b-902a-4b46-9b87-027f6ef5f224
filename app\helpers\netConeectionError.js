import React, { Fragment, useState, useEffect } from 'react';
import { Colors, Caption } from 'react-native-paper';
import { useSelector, useDispatch } from 'react-redux';
import NetInfo from '@react-native-community/netinfo';
import * as Animatable from 'react-native-animatable';
import { NET_OFF, NET_ON } from '../store/reducers/netInfo';

const NetinfoAlert = () => {
  const netinfo = useSelector((state) => state.alert.netinfo);
  const [isOpen, setisOpen] = useState(false);
  const dispatch = useDispatch();
  useEffect(() => {
    NetInfo.addEventListener((networkState) => {
      {
        !networkState.isConnected || !networkState.isInternetReachable
          ? (setisOpen(true), dispatch({ type: NET_OFF }))
          : (dispatch({ type: NET_ON }), setisOpen(false));
      }
    });
  }, []);

  return (
    <Fragment>
      {isOpen ? (
        <Animatable.View
          animation="fadeIn"
          style={{
            // position: 'absolute',
            // top: 75,
            // right: 0,
            // left: 0,
            zIndex: 9999,
            width: '100%',
            padding: 5,
            backgroundColor: 'red',
          }}
        >
          <Caption style={{ color: '#fff', paddingBottom: 0, textAlign: 'center' }}>
            Once you connect to the internet, it is important to allow sufficient time for the data
            to synchronise.{' '}
          </Caption>
        </Animatable.View>
      ) : null}
    </Fragment>
  );
};

export default NetinfoAlert;
