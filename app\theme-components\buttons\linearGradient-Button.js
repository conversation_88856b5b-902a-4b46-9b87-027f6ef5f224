import React from 'react';
import { StyleSheet, Text, Pressable } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { isEmpty } from '_helpers';
import { FontStyle, getFontSize } from '_utils';
import PropTypes from 'prop-types';

const LinearGradientButton = ({
  disabled,
  btnStyle,
  onPress,
  icon,
  contentStyles,
  styleText,
  title,
  compact,
  fontWeight,
  alignself,
  fontSize,
}) => {
  LinearGradientButton.propTypes = {
    disabled: PropTypes.bool,
    btnStyle: PropTypes.object,
    onPress: PropTypes.func,
    icon: PropTypes.object,
    contentStyles: PropTypes.object,
    styleText: PropTypes.object,
    title: PropTypes.string,
    compact: PropTypes.bool,
    fontWeight: PropTypes.string,
    alignself: PropTypes.string,
    fontSize: PropTypes.string,
  };
  const gradientColors = disabled
    ? ['#adaaaa', '#C1C1C1', '#C1C1C1']
    : ['rgba(12, 159, 239,1)', 'rgba(55, 172, 235,0.9)', 'rgba(12, 159, 239,1)'];

  const getButtonStyle = () => {
    return StyleSheet.create({
      buttonStyle: {
        alignSelf: alignself ?? 'center',
        opacity: 0.9,
        flexDirection: 'row',
        justifyContent: 'center',
        borderRadius: 40,
        width: '100%',
      },
      textStyle: {
        textAlign: 'center',
        fontFamily: fontWeight ? fontWeight : FontStyle.fontBold,
        textAlignVertical: 'center',
        color: '#fff',
        fontSize: fontSize ? FontSizes[fontSize] : FontSizes['medium'],
        textTransform: 'uppercase',
      },
      pressableStyle: {
        justifyContent: 'center',
        width: '100%',
        paddingVertical: 8,
        borderRadius: 40,
      },
    });
  };
  const FontSizes = {
    medium: getFontSize('medium'),
    small: getFontSize('small'),
    xxtrasmall: getFontSize('xxtrasmall'),
  };

  return (
    <>
      <LinearGradient
        colors={gradientColors}
        start={{ x: 0.8, y: 0.0 }}
        end={{ x: 2, y: 2.9 }}
        useAngle={true}
        angle={135}
        angleCenter={{ x: 0.5, y: 0.6 }}
        locations={[0, 0.6, 1]}
        style={[getButtonStyle().buttonStyle, btnStyle]}
      >
        <Pressable
          activeOpacity={0.6}
          dark
          compact={compact ?? false}
          onPress={onPress}
          disabled={isEmpty(disabled) ? false : disabled}
          icon={!isEmpty(icon) ? () => icon : null}
          style={[getButtonStyle().pressableStyle, contentStyles]}
        >
          <Text style={[getButtonStyle().textStyle, styleText]}>{title}</Text>
        </Pressable>
      </LinearGradient>
    </>
  );
};

export default LinearGradientButton;
