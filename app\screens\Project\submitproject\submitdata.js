import React, { useEffect, useRef, useState } from 'react';
import { BackHandler, Platform, StyleSheet, View, Dimensions, Text } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { CommonActions, useIsFocused } from '@react-navigation/native';
import Octicons from 'react-native-vector-icons/Octicons';
import NetInfo from '@react-native-community/netinfo';

import {
  AText,
  AuthLoading,
  FabButton,
  LinearGradientButton,
  StudyPoPUP,
  SubmitHeader,
} from '_theme_components';
import { SubmitDataCard } from '_components';
import {
  APP_ID,
  FontStyle,
  VERSION,
  changeWindowHeightWidth,
  tables,
  windowHeight,
  windowWidth,
} from '_utils';
import {
  NetinfoAlert,
  OpenCameraView,
  deleteRecord,
  getDataByTableAndID,
  insertIntoTable,
  isEmpty,
  launchCamera,
  markStudyAsSynced,
  runSqlQuery,
  updateTable,
} from '_helpers';
import { submitStudyAction, syncingStatus<PERSON>heck } from '_action';

import { ALERT_ERROR } from '../../../store/reducers/alert';
import { syncOfflinePhotos, updateLastObsImage } from '_provider';
import {
  BottomBadge,
  EditNameModal,
  EditStudyData,
  EditStudyModal,
  HeaderSubmitPage,
  ProjectStatsModal,
  SubmittedModal,
  SubmittingModal,
  TableHeaders,
} from './submitComponents';
import moment from 'moment';
import { useTheme } from 'react-native-paper';

const SubmitDataScreen = ({ navigation }) => {
  const isFocused = useIsFocused();
  const dispatch = useDispatch();
  const { dark } = useTheme();

  const { studyID, projectSelect, saveImage, saveImageLocal } = useSelector(
    (state) => state.createStudy
  );
  const { dbDetail } = useSelector((state) => state.user);
  const { netConnection } = useSelector((state) => state.netInfo);
  const { userDetails } = useSelector((state) => state.user);
  const {
    studySumbitted,
    studySumbitStarted,
    studySubmittedFailed,
    syncedStudyIDs,
    syncingStudyDetail,
  } = useSelector((state) => state.study);
  const { end_of_study } = useSelector((state) => state.questions);
  const { areaList, allElementList, taskAllList } = useSelector((state) => state.serverReducer);

  const [obsData, setObsData] = useState([]);
  const [selectedObs, setSelected] = useState([]);
  const [study, setStudy] = useState({});
  const [studyArrCopy, setStudyArrCopy] = useState([]);
  const [highestRatedObservations, sethighestRatedObservations] = useState([]);
  const [lowestRatedObservations, setlowestRatedObservations] = useState([]);
  const [highestFrequencyObservations, sethighestFrequencyObservations] = useState([]);
  const [lowestFrequencyObservations, setlowestFrequencyObservations] = useState([]);
  const [elementData, setElementData] = useState([]);
  const [answerData, setAnswerData] = useState([]);
  const [showQuestion, setShowQuestion] = useState({});
  const [questionCheckList, setQuestionCheckList] = useState([]);
  const [editElementData, setEditElementData] = useState({
    rating: '',
    task: '',
    area: '',
    element: '',
  });

  const [editModalHeader, setEditModalHeader] = useState('');

  const [submitHeader, setSumitHeader] = useState('');
  const [submitContent, setSumbitContent] = useState('');
  const [editStudyName, setEditStudyName] = useState('');
  const [freetext, setFreetext] = useState('');
  const [selectedVal, setSelectedVal] = useState('');
  const [selectedCheckboxVal, setselectedCheckboxVal] = useState([]);
  const [frequencyOrder, setFrequencyOrder] = useState('highest');
  const [rateOrder, setRateOrder] = useState('highest');
  const [size, setSize] = useState([]);

  const [loader, setLoader] = useState(false);
  const [submitingModalShow, setSubmitingModalShow] = useState(false);
  const [submitedModalShow, setSubmitedModalShow] = useState(false);
  const [showSumary, setshowSumary] = useState(true);
  const [editModalShow, setEditModalShow] = useState(false);
  const [editComponentModalShow, setEditComponentModalShow] = useState(false);
  const [editNameModal, setEditNameModal] = useState(false);
  const [showQuestionPopUP, setShowQuestionPopUP] = useState(false);
  const [showStudyPermissionModal, setShowStudyPermissionModal] = useState(false);
  const [viewCamera, setViewCamera] = useState(false);

  const [emailNprojectStatchkBox, setEmailNprojectStatchkBox] = useState({
    emailSubmit: false,
    projectStat: false,
  });
  var intervalIdRef = useRef(null);

  const fieldArray = [
    {
      id: 1,
      name: 'rating',
      high: highestRatedObservations,
      low: lowestRatedObservations,
      order: rateOrder,
      set: setRateOrder,
    },
    {
      id: 1,
      name: 'Frequency',
      high: highestFrequencyObservations,
      low: lowestFrequencyObservations,
      order: frequencyOrder,
      set: setFrequencyOrder,
    },
  ];
  const [orderToggles, setOrderToggles] = useState({
    time: {
      toggled: true,
      desc: false,
    },
    element: {
      toggled: false,
      desc: false,
    },
    rating: {
      toggled: false,
      desc: false,
    },
    frequency: {
      toggled: false,
      desc: false,
    },
    area: {
      toggled: false,
      desc: false,
    },
    task: {
      toggled: false,
      desc: false,
    },
    role: {
      toggled: false,
      desc: false,
    },
  });
  useEffect(() => {
    const backHandler = BackHandler.addEventListener('hardwareBackPress', () => true);
    return () => backHandler.remove();
  }, []);

  useEffect(() => {
    if (isFocused) {
      dispatch({
        type: 'RESET_SUBMITTED',
      });
      getAnswerData();
      checkSubmitScreenQuestions();
      setSelected([]);
    } else {
      clearInterval(intervalIdRef.current);
      intervalIdRef.current = null;
      setTimeout(() => {
        setSubmitingModalShow(false);
        setSubmitedModalShow(false);
      }, 1200);
      setLoader(false);
      // navigation.addListener('blur', payload =>
      //   BackHandler.removeEventListener('hardwareBackPress', backAction)
      // );
    }
  }, [isFocused]);

  useEffect(() => {
    if (isFocused) getStudyData(true);
  }, [netConnection, isFocused]);

  const checkSubmitScreenQuestions = () => {
    var questionCheck = [];
    if (!isEmpty(end_of_study)) {
      questionCheck = end_of_study;
    }
    if (!isEmpty(questionCheck)) {
      setShowQuestionPopUP(true);
      setShowQuestion(questionCheck[0]);
      setQuestionCheckList(questionCheck);
    }
  };

  const navigateNext = (page) => {
    clearInterval(intervalIdRef.current);
    intervalIdRef.current = null;
    setLoader(false);
    dispatch({ type: 'CLEAR_SYNCING_STUDIES' });
    dispatch({
      type: 'CLEAR_STUDY',
    });
    dispatch({
      type: 'SYNCDATA_CLEAR',
    });
    dispatch({
      type: 'RESET_SUBMITTED',
    });
    if (page == 'project') {
      navigation.replace('HomeNav', { screen: 'Home' });
    } else {
      navigation.dispatch(
        CommonActions.reset({
          index: 1,
          routes: [{ name: 'Locations' }],
        })
      );
    }
  };
  useEffect(() => {
    if (studySumbitStarted) {
      fetchDataSyncing();
      intervalIdRef.current = setInterval(fetchDataSyncing, 5000);
    }
    if (studySubmittedFailed) {
      dispatch({
        type: 'CLEAR_SERVER',
      });
      setSumitHeader('Data Submission Error');
      setSumbitContent(
        `Study data couldn't be submitted.Please check your internet connection and try again later from the "Sync Data" page. Choose from one of the options below to continue.`
      );
      setSubmitedModalShow(true);
      if (Platform.OS == 'ios') {
        setSubmitingModalShow(false);
      }
    }
  }, [studySumbitStarted, studySubmittedFailed]);

  const fetchDataSyncing = async () => {
    let idCheck = syncedStudyIDs;
    // dispatch(syncingStudiesCheck(idCheck));
    if (idCheck) {
      const arrayOfIds = idCheck.map((item) => item.syncedStudyID);
      let payload = {
        syncedStudyIDs: arrayOfIds,
      };
      try {
        await dispatch(syncingStatusCheck(payload));
      } catch (error) {
        console.error('Error checking sync status:', error);
      }
    }
  };

  useEffect(() => {
    if (!isEmpty(syncingStudyDetail)) {
      checkStatus();
    }
  }, [syncingStudyDetail]);

  const checkStatus = async () => {
    if (!isEmpty(syncingStudyDetail)) {
      if (syncingStudyDetail[0].status == 'COMPLETED') {
        markStudyAsSynced(syncingStudyDetail[0]._id, dbDetail);
        clearInterval(intervalIdRef.current);
        deleteRecord(tables.RECOVERY_DATA_TABLE, { studyID: studyID }, dbDetail);
        dispatch({
          type: 'CLEAR_SERVER',
        });
        setSumitHeader('Data Sent.');
        setSumbitContent(
          'The study data was transferred. Choose from one of the options below to continue.'
        );
        setTimeout(() => {
          setSubmitedModalShow(true);
          if (Platform.OS == 'ios') {
            setSubmitingModalShow(false);
          }
        }, 2500);
      } else if (syncingStudyDetail[0].status == 'ERROR') {
        clearInterval(intervalIdRef.current);
        dispatch({
          type: 'CLEAR_SERVER',
        });
        setSumitHeader('Data Submission Error');
        setSumbitContent(
          `Study data couldn't be submitted.Please check your internet connection and try again later from the "Sync Data" page. Choose from one of the options below to continue.`
        );
        setSubmitedModalShow(true);
        if (Platform.OS == 'ios') {
          setSubmitingModalShow(false);
        }
      }
    }
  };

  const handleSelectstudy = (id) => {
    if (id == 'all') {
      setSelected(obsData);
    } else if (id == 'cancel') {
      setSelected([]);
    } else {
      let selectedItem = obsData.find((item) => item.id === id);
      if (selectedItem) {
        setSelected((prevSelected) => {
          const isAlreadySelected = prevSelected.some((item) => item.id === id);
          if (isAlreadySelected) {
            return prevSelected.filter((item) => item.id !== id);
          } else {
            return [...prevSelected, selectedItem];
          }
        });
      }
    }
  };

  useEffect(() => {
    if (!isEmpty(saveImage) && !isEmpty(obsData)) {
      saveLastObsImage();
      saveLastObsImageLocal();
    }
  }, [saveImage, obsData]);

  const getStudyData = async () => {
    setLoader(true);
    await dispatch(syncOfflinePhotos(dbDetail, netConnection));
    let [study, studyDatas] = await Promise.all([
      getDataByTableAndID(tables.STUDY_TABLE, studyID, dbDetail),
      getDataByTableAndID(tables.STUDY_DATA_TABLE, studyID, dbDetail),
    ]);

    if (isEmpty(study) || isEmpty(studyDatas)) {
      setLoader(false);
      setStudy({});
      setObsData([]);
      setStudyArrCopy([]);
      return;
    }
    const currentStudy = study[0];
    setStudy(currentStudy);
    await getformattedData(studyDatas);
    setEditStudyName(currentStudy.name);

    setTimeout(() => {
      setLoader(false);
    }, 500);
  };
  const getAnswerData = async () => {
    let answers = await getDataByTableAndID(tables.ANSWERS_TABLE, studyID, dbDetail);
    if (!isEmpty(answers)) {
      setAnswerData(answers);
    }
  };

  const getformattedData = async (studydata) => {
    let updatedStudyData = await Promise.all(
      studydata.map(async (observation) => {
        let { areaID, areaName, taskID, elementID, startTime, endTime } = observation;
        startTime = moment(startTime).toISOString();
        endTime = moment(endTime).toISOString();
        if (areaID.indexOf('area') > -1) {
          observation.areaData = {
            name: areaName,
            studyTypes: [1, 2, 3],
            customerID: projectSelect.customerID,
            projectID: projectSelect._id,
          };
        }
        if (!isEmpty(taskID) && !isEmpty(elementID)) {
          let elementData;
          if (isEmpty(elementData)) {
            elementData = !isEmpty(elementID)
              ? await getDataByTableAndID('OfflineElement', elementID, dbDetail)
              : [{}];
          }
          elementData[0].categoryID = elementData[0].category;
          elementData[0].count = elementData[0].count === 1 ? true : false;
          observation.elementData = elementData[0];
        }
        return observation;
      })
    );
    // const resolvedObservations = updatedStudyData
    //   .filter(result => result.status === 'fulfilled')
    //   .map(result => result.value);

    setObsData(updatedStudyData);
    setStudyArrCopy(updatedStudyData);
    dispatch({
      type: 'SYNCDATA_OBSERVATION',
      payload: updatedStudyData,
    });

    // prepareSummaryScreen(updatedStudyData);
    setLoader(false);
  };

  const submitStudy = async () => {
    await updateStudyData();
    setShowStudyPermissionModal(false);
    var netConnectivity = false;
    let isConnected, isInternetReachable, details;
    NetInfo.addEventListener((networkState) => {
      isConnected = networkState.isConnected;
      isInternetReachable = networkState.isInternetReachable;
      details = networkState.details;
      {
        !networkState.isConnected ||
        !networkState.isInternetReachable ||
        networkState.details?.linkSpeed < 12
          ? (netConnectivity = false)
          : (netConnectivity = true);
      }
    });
    setTimeout(() => {
      if (netConnectivity) setSubmitingModalShow(true);
    }, 500);

    const os = Platform.OS == 'ios' ? 'IOS' : 'Android';
    const addedBy = {
      _id: userDetails._id,
      name: userDetails.name + ' ' + userDetails.lastname,
      date: new Date(),
    };

    if (netConnectivity) {
      let dataStudy = obsData;
      var studyToEmailAndStats = {
        sendStudyToEmail: emailNprojectStatchkBox.emailSubmit,
        sendProjectStatsReport: emailNprojectStatchkBox.projectStat,
      };

      var payload = {
        name: study.name,
        studyStartTime: study.studyStartTime,
        studyEndTime: study.studyEndTime,
        customerID: study.customerID,
        projectID: study.projectID,
        locationID: study.locationID,
        userID: userDetails._id,
        data: dataStudy,
        addedBy: addedBy,
        roundDuration: study.studyEndTime - study.studyStartTime,
        version: VERSION,
        studyType: APP_ID,
        osInfo: os,
        timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        answers: answerData,
        sendStudyToEmail: emailNprojectStatchkBox.emailSubmit,
        sendProjectStatsReport: emailNprojectStatchkBox.projectStat,
        showLastNote: true,
      };
      // console.log(JSON.stringify(payload), ' stutydapayload');
      // return;
      dispatch(submitStudyAction(payload, dbDetail, study.id));
    } else {
      setSumitHeader('Your data has been saved.');
      let message =
        isConnected && isInternetReachable && details?.linkSpeed < 7
          ? 'Your study data has been successfully saved locally. Please sync the study when you have a strong internet connection. Choose from one of the options below to continue.'
          : 'Your Study data has been successfully saved locally and can be synced when Internet is available. Choose from one of the options below to continue.';
      setSumbitContent(message);
      setSubmitedModalShow(true);
      if (Platform.OS == 'ios') {
        setSubmitingModalShow(false);
      }
    }
  };
  const prepareSummaryScreen = (observationsArray = obsData) => {
    let observations = observationsArray.slice();

    const amountOfObservationsPerCategory = 5;
    const observationExcludingNonRated = observations.filter((o) => o.rating != 'Not Rated');

    const sortedByRatingObservations = observationExcludingNonRated.sort((a, b) => {
      return a.rating - b.rating;
    });

    let highestRatedObservations = sortedByRatingObservations
      .slice(-amountOfObservationsPerCategory)
      .reverse();
    sethighestRatedObservations(highestRatedObservations);
    let lowestRatedObservations = sortedByRatingObservations.slice(
      0,
      amountOfObservationsPerCategory
    );
    setlowestRatedObservations(lowestRatedObservations);

    const sortedByFrequencyObservations = observations.sort((a, b) => {
      return a.frequency - b.frequency;
    });

    let highestFrequencyObservations = sortedByFrequencyObservations
      .slice(-amountOfObservationsPerCategory)
      .reverse();

    let lowestFrequencyObservations = sortedByFrequencyObservations.slice(
      0,
      amountOfObservationsPerCategory
    );
    sethighestFrequencyObservations(highestFrequencyObservations);
    setlowestFrequencyObservations(lowestFrequencyObservations);
  };
  const toggleOrdering = (columnName) => {
    var sortedObservations = [];
    let studydatas = JSON.parse(JSON.stringify(studyArrCopy));
    const currentToggleState = orderToggles[columnName].toggled;
    const currentToggleOrderState = orderToggles[columnName].desc;
    let ordertoggle = orderToggles;
    Object.keys(ordertoggle).forEach((key) => {
      ordertoggle[key] = {
        toggled: false,
        desc: false,
      };
    });
    setOrderToggles({ ...ordertoggle });
    let toggle = orderToggles[columnName];
    toggle.toggled = currentToggleState;
    toggle.desc = currentToggleOrderState;

    if (!toggle.desc) {
      // set to desc if not already
      toggle.desc = true;
      sortedObservations = studydatas.sort((a, b) => {
        return a[columnName] - b[columnName];
      });
      sortedObservations.reverse();
    } else {
      // set to off if already desc
      toggle.toggled = false;
      toggle.desc = false;
      sortedObservations = studydatas.sort((a, b) => {
        return a[columnName] - b[columnName];
      });
    }
    if (!isEmpty(sortedObservations)) {
      setObsData(sortedObservations);
    }
  };
  const deleteObservationFromSQLite = async () => {
    // setDeleteObsModal(false)
    let selectedObservation = [];
    selectedObs.map((item, index) => {
      selectedObservation.push(item.id);
    });
    selectedObservation = selectedObservation.toString();
    await deleteRecord(tables.STUDY_DATA_TABLE, { id: selectedObservation }, dbDetail)
      .then((result) => {
        setSelected([]);
        const updatedFieldArray = studyArrCopy.filter(
          (field) => !selectedObservation.includes(field.id)
        );
        setObsData(updatedFieldArray);
        setStudyArrCopy(updatedFieldArray);
        return;
      })
      .catch((error) => {});
  };

  const getElementData = async (taskId) => {
    let elements = allElementList.find((item) => item.taskID == taskId);
    let elementdata = elements?.grouped_data;
    setElementData(elementdata);
  };
  const updateEditElement = async () => {
    if (!isEmpty(editElementData.task) && isEmpty(editElementData.element)) {
      dispatch({
        type: ALERT_ERROR,
        payload: 'Please select element',
      });
      return;
    }

    let selectedObservation = selectedObs.map((item) => item.id).join(',');

    selectedObservation = selectedObservation.toString();
    let data = {};
    /* CHEDKING AREA*/
    if (!isEmpty(editElementData.area)) {
      data.area = editElementData.area;
      data.areaName = editElementData.areaName;
    }
    /* CHECKING TASk*/
    if (!isEmpty(editElementData.task)) {
      data.task = editElementData.task;
      data.taskName = editElementData.taskName;
    }
    /* CHECKING ELEMENT */
    if (!isEmpty(editElementData.element)) {
      data.element = editElementData.element;
      data.elementName = editElementData.elementName;
      if (editElementData.element.count === 2) {
        editElementData.frequency = 1;
      }
      if (editElementData.element.rating === 1) {
        editElementData.rating = 'Not Rated';
      } else if (editElementData.element.rating === 2) {
        editElementData.rating = 100;
      }
    }
    if (!isEmpty(editElementData.rating)) data.rating = editElementData.rating;
    /* SETTING RATING*/
    let editVar = Object.keys(data);
    let query = `UPDATE ${tables.STUDY_DATA_TABLE} SET `;
    let query_data = [];
    editVar.map((item, index) => {
      query += `${item} =? ${editVar.length - 1 !== index ? ',' : ''} `;
      query_data.push(data[item]);
    });
    query += ` WHERE id IN (${selectedObservation})`;
    runSqlQuery(dbDetail, query, query_data)
      .then((result) => {
        getStudyData();
        setEditModalHeader('');
        setEditModalShow(false);
        setEditComponentModalShow(false);
        setEditElementData({
          rating: '',
          task: '',
          area: '',
          element: '',
        });
        setSelected([]);
      })
      .catch((error) => {});
  };

  const updateStudyData = async () => {
    let data = {
      sendStudyToEmail: emailNprojectStatchkBox.emailSubmit,
      sendProjectStatsReport: emailNprojectStatchkBox.projectStat,
      studyID: study.id,
    };
    await updateTable(tables.STUDY_TABLE, null, data, dbDetail);
  };
  const updateStudyName = async () => {
    setEditNameModal(false);
    updateTable(tables.STUDY_TABLE, null, { studyName: editStudyName, studyID: study.id }, dbDetail)
      .then((result) => {
        setStudy({
          ...study,
          name: editStudyName,
        });
        // getStudyData();
      })
      .catch((error) => {});
  };

  const updateAnswer = async (val, submit = false) => {
    setShowQuestionPopUP(false);
    if (submit) {
      const payload = {
        question: showQuestion.text,
        answer: val,
        studyID: studyID,
        time: new Date().getTime(),
        questionID: showQuestion._id,
        triggerType: showQuestion.triggerType,
        // observationID: 1675774350670
      };
      await insertIntoTable(tables.ANSWERS_TABLE, [payload], dbDetail);
      getAnswerData();
    }
    setShowQuestion({});
    let ind = questionCheckList.findIndex((n) => n._id === showQuestion._id);

    let nextQuestion = questionCheckList[ind + 1];
    if (!isEmpty(ind) && !isEmpty(nextQuestion)) {
      setShowQuestion(questionCheckList[ind + 1]);
      questionCheckList.shift();
      setShowQuestionPopUP(true);
      dispatch({
        type: 'SAVE_SUBMITTED_QUESTION',
        payload: questionCheckList,
      });
    } else {
      setShowQuestion({});
      setShowQuestionPopUP(false);
      dispatch({
        type: 'SAVE_SUBMITTED_QUESTION',
        payload: [],
      });
    }
  };

  useEffect(() => {
    const updateWindowHeight = () => {
      const newWindowHeight = Dimensions.get('window').height;
      const newWindowWidth = Dimensions.get('window').width;
      changeWindowHeightWidth(newWindowHeight, newWindowWidth);
      setSize(newWindowWidth);
    };

    Dimensions.addEventListener('change', updateWindowHeight);

    return () => {
      // Dimension.remove('change', updateWindowHeight);
    };
  }, []);

  const saveLastObsImage = async () => {
    setLoader(true);
    let obseData = obsData[obsData.length - 1];
    let photos = '';
    if (!isEmpty(obseData.photo)) {
      photos = obseData.photo + ',' + saveImage;
    } else {
      photos = saveImage;
    }
    let newPaths = photos.toString();
    try {
      await updateLastObsImage(newPaths, obseData.id, dbDetail);

      dispatch({
        type: 'CLEAR_IMAGE',
      });
      const updatedArray = [...obsData];
      updatedArray[updatedArray.length - 1].photo = newPaths;
      setStudyArrCopy(updatedArray);
      setObsData(updatedArray);
      setLoader(false);
    } catch (error) {
      setLoader(false);
      console.error('Error updating last observation image:', error);
      // Handle the error appropriately (e.g., show a message to the user)
    }
  };

  const saveLastObsImageLocal = async () => {
    // setLoader(true);
    let obseData = obsData[obsData.length - 1];
    let photos = '';
    if (!isEmpty(obseData.photoLocal)) {
      photos = obseData.photoLocal + ',' + saveImageLocal;
    } else {
      photos = saveImageLocal;
    }
    let newPaths = photos.toString();
    try {
      await updateLastObsImage(newPaths, obseData.id, dbDetail, true);

      dispatch({
        type: 'CLEAR_IMAGE',
      });
      const updatedArray = [...obsData];
      updatedArray[updatedArray.length - 1].photoLocal = newPaths;
      setObsData(updatedArray);
      setStudyArrCopy(updatedArray);
      setLoader(false);
    } catch (error) {
      setLoader(false);
      console.error('Error updating last observation image:', error);
      // Handle the error appropriately (e.g., show a message to the user)
    }
  };

  const renderObservations = (observations) => {
    return observations.map((item) => (
      <View style={styles.container} key={item.id}>
        <SubmitDataCard
          showEditButton
          selected={selectedObs.findIndex((n) => n.id === item.id) !== -1}
          navigation={navigation}
          item={item}
          handleselectstudy={() => handleSelectstudy(item.id)}
          navigateNextTo={() =>
            navigation.navigate('ObservationDetail', { obsData: item, fromSyncPage: false })
          }
        />
      </View>
    ));
  };

  const openCamera = async () => {
    let camerpermision = await launchCamera();
    if (camerpermision) {
      setViewCamera(true);
    }
  };
  const renderNoEntries = () => (
    <View style={styles.noDataview}>
      <AText
        fontSize={'large'}
        styleText={{ textTransform: 'capitalize' }}
        fontWeight={FontStyle.fontBold}
      >
        No Entries
      </AText>
      <LinearGradientButton
        btnStyle={{ width: windowWidth * 0.3 }}
        contentStyles={{ paddingVertical: 13 }}
        compact={true}
        onPress={() => navigateNext('project')}
        title={'Cancel Study'}
      />
    </View>
  );

  return (
    <>
      {loader ? (
        <AuthLoading />
      ) : (
        <>
          <SubmitHeader
            navigation={navigation}
            Title={'Data Collected'}
            alignself={'flex-start'}
            allowStudyNameEdit={
              projectSelect.allowStudyNameEdit || !projectSelect.autoGenerateStudyNames
            }
            SubTitle={isEmpty(study) ? '' : study.name}
            showBack={false}
            showSearch={false}
            ShowMagnify={false}
            searchPress={''}
            submitDisabled={isEmpty(obsData)}
            showEditNameModal={() => setEditNameModal(true)}
            ShowDraft={true}
            SubmitDataCard={() => {
              setShowStudyPermissionModal(true);
            }}
            ShowSave={true}
            submiteModal={true}
            showSmallTitle={true}
          >
            {viewCamera ? (
              <OpenCameraView
                projectID={projectSelect._id}
                customerID={projectSelect.customerID}
                cancel={() => setViewCamera(false)}
                ImageName={study.customerName + `_` + study.name}
              />
            ) : null}
            {showSumary ? (
              <>
                <HeaderSubmitPage label={`Summary`} showButton={false} />
                <View style={styles.contentContainer}>
                  <TableHeaders
                    toggleOrdering={(val) => {
                      toggleOrdering(val);
                    }}
                  />
                  {!isEmpty(obsData)
                    ? renderObservations(obsData)
                    : isEmpty(obsData) && !loader
                      ? renderNoEntries()
                      : null}
                </View>
              </>
            ) : (
              fieldArray.map(({ name, high, low, order, set }) => (
                <>
                  <HeaderSubmitPage
                    label={`${order} ${name}`}
                    order={order}
                    setOrder={set}
                    showButton
                  />
                  <View style={styles.contentContainer}>
                    <TableHeaders toggleOrdering={(val) => {}} />
                    {order == 'highest'
                      ? renderObservations(eval(high))
                      : renderObservations(eval(low))}
                  </View>
                </>
              ))
            )}

            {/* --------------------submitting Modal ----------------------------- */}
            <SubmittingModal
              submitingModalShow={submitingModalShow}
              progress={!isEmpty(syncingStudyDetail) ? syncingStudyDetail[0].progress : ''}
            />

            {/* --------------------submitting Modal  end----------------------------- */}
            {/* --------------------submitted Modal ---------------------------------- */}
            <SubmittedModal
              submitedModalShow={submitedModalShow}
              netConnection={netConnection}
              submitHeader={submitHeader}
              submitContent={submitContent}
              navigateNext={(val) => {
                navigateNext(val);
              }}
            />
            {/* --------------------submitted Modal  end----------------------------- */}
            {/* --------------------edit Modal ------------------------------------ */}
            <EditStudyModal
              closeModal={() => {
                setEditModalShow(false),
                  setEditElementData({
                    rating: '',
                    task: '',
                    area: '',
                    element: '',
                  });
              }}
              editModalShow={editModalShow}
              selectedObs={selectedObs}
              editElementData={editElementData}
              updateEditElement={() => updateEditElement()}
              onIconPress={(val) => {
                setEditModalHeader(val), setEditModalShow(false), setEditComponentModalShow(true);
              }}
            />
            {/* --------------------edit Modal end----------------------------- */}
            {/* --------------------edit area/rate/task/element/rate Modal ----------------------------- */}
            <EditStudyData
              closeModal={() => {
                setEditComponentModalShow(false), setEditModalShow(true);
              }}
              editModalHeader={editModalHeader}
              editComponentModalShow={editComponentModalShow}
              ratingData={editElementData.rating}
              updateData={(val, item) => {
                setEditComponentModalShow(false),
                  setEditModalShow(true),
                  val == 'rating'
                    ? setEditElementData({
                        ...editElementData,
                        rating: item,
                      })
                    : setEditElementData({
                        ...editElementData,

                        [val]: item._id,
                        [`${val}Name`]: item.name,
                      });
                val === 'task'
                  ? (!isEmpty(editElementData.element) &&
                      setEditElementData({
                        ...editElementData,

                        element: '',
                        elementName: '',
                      }),
                    getElementData(item._id))
                  : '';
              }}
              taskAllList={taskAllList}
              areaList={areaList}
              elementData={elementData}
            />
            {/* --------------------edit area/rate/task/element/rate Modal end----------------------------- */}
            {/* --------------------edit name Modal ------------------------------------ */}
            <EditNameModal
              editNameModal={editNameModal}
              updateStudyName={updateStudyName}
              editStudyName={editStudyName}
              closeModal={() => {
                setEditNameModal(false), setEditStudyName(study.name);
              }}
              settStudyName={(val) => {
                setEditStudyName(val);
              }}
            />
            {/* --------------------edit name Modal end----------------------------- */}
            {/* --------------------project stat and study email Modal ------------------------------------ */}
            <ProjectStatsModal
              setShowStudyPermissionModal={() => setShowStudyPermissionModal(false)}
              projectStatsShow={projectSelect.projectStats}
              studyExport={projectSelect.studyExport}
              cancelPress={() => {
                setEmailNprojectStatchkBox({
                  emailSubmit: false,
                  projectStat: false,
                });
                setShowStudyPermissionModal(false);
              }}
              showStudyPermissionModal={showStudyPermissionModal}
              setEmailNproject={(val) => {
                setEmailNprojectStatchkBox({
                  ...emailNprojectStatchkBox,
                  [val]: !emailNprojectStatchkBox[val],
                });
              }}
              emailNprojectStatchkBox={emailNprojectStatchkBox}
              emailSubmit={emailNprojectStatchkBox.emailSubmit}
              projectStat={emailNprojectStatchkBox.projectStat}
              submitStudy={submitStudy}
            />
            {/* --------------------project stat and study email Modal end----------------------------- */}
          </SubmitHeader>
          <Text
            style={{ ...styles.noteStyle, color: dark ? '#fff' : '#fff' }}
            fontWeight={FontStyle.fontBold}
          >
            Please Make sure you have strong internet connection before submitting study.
          </Text>
          <NetinfoAlert />
          {!isEmpty(showQuestion) && (
            <StudyPoPUP
              question={showQuestion}
              modalShow={showQuestionPopUP}
              freetext={freetext}
              selectedVal={selectedVal}
              setSelectedVal={(val) => setSelectedVal(val)}
              selectedCheckboxVal={selectedCheckboxVal}
              setselectedCheckboxVal={(val) => setselectedCheckboxVal(val)}
              setFreetext={(val) => setFreetext(val)}
              onPhotoSubmit={() => {
                openCamera();
                !showQuestion.answerRequired && updateAnswer();
              }}
              onDismiss={() => {
                updateAnswer();
              }}
              onConfirm={(val) => {
                updateAnswer(val, true);
              }}
            />
          )}
          {selectedObs.length == 0 && !isEmpty(obsData) && (
            <FabButton
              icons={!showSumary ? 'scan-outline' : 'scan-outline'}
              ICONS={<Octicons name={'rows'} size={27} color={'#fff'} style={styles.iconStyle} />}
              showIcon={showSumary}
              style={styles.fab}
              onClick={() => {
                prepareSummaryScreen();
                setshowSumary(!showSumary);
              }}
            />
          )}
          {!isEmpty(selectedObs) && selectedObs.length > 0 && !editModalShow && (
            <BottomBadge
              obsCount={obsData.length}
              selectObsCount={selectedObs.length}
              editObservation={() => setEditModalShow(true)}
              handleObservation={(val) => handleSelectstudy(val)}
              deleteObservation={() => deleteObservationFromSQLite()}
            />
          )}
        </>
      )}
    </>
  );
};

export default SubmitDataScreen;

const styles = StyleSheet.create({
  noteStyle: {
    position: 'absolute',
    zIndex: 1,
    bottom: 0,
    alignSelf: 'center',
    fontSize: 16,
    paddingVertical: 4,
    paddingHorizontal: 10,
    backgroundColor: '#0c9fef',
    width: '100%',
    textAlign: 'center',
  },
  contentContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    marginTop: 35,
    alignSelf: 'center',
  },
  noDataview: {
    alignSelf: 'center',
    alignItems: 'center',
    marginTop: 95,
    justifyContent: 'center',
  },
  container: {
    alignItems: 'center',
    width: '100%',
  },
  fab: {
    position: 'absolute',
    marginHorizontal: 16,
    right: 0,
    bottom: windowHeight * 0.05,
    padding: 0,
    borderRadius: windowHeight * 0.05,
  },
  iconStyle: {
    alignSelf: 'center',
    marginRight: 0,
  },
});
