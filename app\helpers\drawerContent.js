import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Image } from 'react-native';
import { Drawer, useTheme } from 'react-native-paper';
import { DrawerContentScrollView, DrawerItem } from '@react-navigation/drawer';
import { useDispatch, useSelector } from 'react-redux';
import { FontStyle, localimage, VERSION } from '_utils';
import { AText, IconButton } from '_theme_components';
import { fetchRecoverData } from '_provider';
import { HIDE_DOWNLOAD, SHOW_DOWNLOAD } from '../store/reducers/netInfo';

export function DrawerContent(props) {
  const dispatch = useDispatch();
  const { dark } = useTheme();
  const { dbDetail, userDetails } = useSelector((state) => state.user);
  const [showLocalData, setShowLocalData] = useState(false);
  const [loader, setLoader] = useState(false);
  const { netConnection } = useSelector((state) => state.netInfo);

  var count = 0;
  const backAction = () => {
    if (count > 8 && count < 10) {
      setShowLocalData(true);
      dispatch({ type: SHOW_DOWNLOAD });
      count = 0;
    } else {
      count += 1;
    }
    return true;
  };

  useEffect(() => {
    if (showLocalData) {
      fetchRecoverData(dbDetail, netConnection);
      setTimeout(() => {
        setShowLocalData(false);
        dispatch({ type: HIDE_DOWNLOAD });
      }, 10000);
    }
  }, [showLocalData]);

  return (
    <>
      {loader ? <AuthLoading /> : null}
      <View style={[styles.drawerContent, { backgroundColor: dark ? '#1f1f1f' : '#FFF' }]}>
        <DrawerContentScrollView {...props}>
          <View style={styles.drawerContent}>
            <View style={styles.userInfoSection}>
              <IconButton
                btnStyle={styles.imageBtnStyle}
                onPress={() => backAction()}
                icon={<Image source={localimage.logo} style={styles.imageStyle} />}
              />
            </View>
            <Drawer.Section style={styles.drawerSection}>
              <DrawerItem
                label={() => (
                  <AText fontWeight={FontStyle.fontBold} fontSize={'medium'}>
                    Help
                  </AText>
                )}
                onPress={() => {
                  props.navigation.closeDrawer();
                  props.navigation.navigate('Help');
                }}
              />

              <DrawerItem
                labelStyle={styles.labelStyle}
                label={() => (
                  <AText fontWeight={FontStyle.fontBold} fontSize={'medium'}>
                    Terms of Services
                  </AText>
                )}
                onPress={() => {
                  props.navigation.closeDrawer();
                  props.navigation.navigate('TermsandCondition');
                }}
              />
              <DrawerItem
                labelStyle={styles.labelStyle}
                label={() => (
                  <AText fontWeight={FontStyle.fontBold} fontSize={'medium'}>
                    Privacy Policy
                  </AText>
                )}
                onPress={() => {
                  props.navigation.closeDrawer();
                  props.navigation.navigate('PrivacyPolicy');
                }}
              />
              {showLocalData && (
                <>
                  <DrawerItem
                    labelStyle={styles.labelStyle}
                    label={() => (
                      <AText fontWeight={FontStyle.fontBold} fontSize={'medium'}>
                        Local Data
                      </AText>
                    )}
                    onPress={() => {
                      props.navigation.closeDrawer();
                      props.navigation.navigate('RecoverScreen');
                    }}
                  />
                  <DrawerItem
                    labelStyle={styles.labelStyle}
                    label={() => (
                      <AText fontWeight={FontStyle.fontBold} fontSize={'medium'}>
                        Error Log
                      </AText>
                    )}
                    onPress={() => {
                      props.navigation.closeDrawer();
                      props.navigation.navigate('ErrorScreen');
                    }}
                  />
                </>
              )}
            </Drawer.Section>
          </View>
        </DrawerContentScrollView>
        <Drawer.Section style={styles.bottomDrawerSection}>
          <DrawerItem
            label={() => (
              <AText
                fontWeight={FontStyle.fontBold}
                fontSize={'medium'}
              >{`Version ${VERSION}`}</AText>
            )}
            labelStyle={styles.versionStyle}
          />
        </Drawer.Section>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  drawerContent: {
    flex: 1,
  },
  userInfoSection: {
    padding: 35,
  },
  imageBtnStyle: {
    width: 80,
    height: 90,
    alignSelf: 'flex-start',
    paddingVertical: 0,
    padding: 0,
  },
  imageStyle: {
    resizeMode: 'contain',
    height: 70,
    width: 70,
  },
  labelStyle: {
    fontFamily: FontStyle.fontBold,
  },
  versionStyle: {
    fontFamily: FontStyle.fontBold,
  },
  userTitle: { marginLeft: 15 },

  title: {
    fontSize: 16,
    marginTop: 3,
    fontWeight: 'bold',
  },
  drawerSection: {
    marginTop: 35,
    paddingLeft: 15,
  },
  bottomDrawerSection: {
    marginBottom: 15,
    borderWidth: 0,
  },
});
