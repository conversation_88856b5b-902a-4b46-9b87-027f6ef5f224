export const THEME_LOADING = 'THEME_LOADING';
export const THEME_LOADING_STOP = 'THEME_LOADING_STOP';
export const THEME_LIGHT = 'THEME_LIGHT';

const initialState = {
  loading: false,
  ThemeLight: true,
};

export default (state = initialState, action) => {
  switch (action.type) {
    case THEME_LOADING:
      return {
        ...state,
        loading: true,
      };
    case THEME_LIGHT:
      return {
        ...state,
        ThemeLight: !state.ThemeLight,
        loading: false,
      };
    case THEME_LOADING_STOP:
      return {
        ...state,
        loading: false,
      };
    default: {
      return state;
    }
  }
};
