import { PROJECT_LOADING, PROJECT_LOADING_FALSE } from '../store/actions/projctAction';
import {
  createTable,
  dropTable,
  getAllData,
  getData,
  getDBConnection,
  getURL,
  insertIntoTable,
  isEmpty,
  storeData,
} from '_helpers';
import { DB_DATABASE } from '../store/reducers/user';
import { ALERT_ERROR } from '../store/reducers/alert';
import { NO_DATA_FOUND, tables } from '_utils';
import { PROJECT_LIST } from '../store/reducers/projectReducer';

export const createDataCase = () => async (dispatch) => {
  const db = await getDBConnection();
  if (!isEmpty(db)) {
    dispatch({
      type: DB_DATABASE,
      payload: db,
    });
  }
};
export const dropTables = (dbDetail) => async (dispatch) => {
  Promise.all([
    dropTable(tables.PROJECTS_TABLE, dbDetail),
    dropTable(tables.LOCATIONS_TABLE, dbDetail),
    dropTable(tables.AREAS_TABLE, dbDetail),
    dropTable(tables.TASKS_TABLE, dbDetail),
    dropTable(tables.ELEMENTS_TABLE, dbDetail),
    dropTable(tables.CATEGORIES_TABLE, dbDetail),
    // dropTable(tables.ROLES_TABLE, dbDetail),
    dropTable(tables.GROUPS_TABLE, dbDetail),
    dropTable(tables.REMINDERS_TABLE, dbDetail),
    dropTable(tables.QUESTIONS_TABLE, dbDetail),
    createTable(tables.ERROR_TABLE, dbDetail),
  ])
    .then((values) => {
      return true;
    })
    .catch((error) => {
      dispatch({
        type: PROJECT_LOADING_FALSE,
      });
      return false;
    });
};

export const createTables = (dbDetail) => async (dispatch) => {
  Promise.all([
    createTable(tables.PROJECTS_TABLE, dbDetail),
    createTable(tables.LOCATIONS_TABLE, dbDetail),
    createTable(tables.AREAS_TABLE, dbDetail),
    createTable(tables.TASKS_TABLE, dbDetail),
    createTable(tables.ELEMENTS_TABLE, dbDetail),
    createTable(tables.CATEGORIES_TABLE, dbDetail),
    // createTable(tables.ROLES_TABLE, dbDetail),
    createTable(tables.GROUPS_TABLE, dbDetail),
    createTable(tables.REMINDERS_TABLE, dbDetail),
    createTable(tables.QUESTIONS_TABLE, dbDetail),
    createTable(tables.ANSWERS_TABLE, dbDetail),
  ])
    .then((values) => {
      return true;
    })
    .catch((error) => {
      dispatch({
        type: PROJECT_LOADING_FALSE,
      });
      return false;
    });
};

export const populateTables = (dbDetail, projectByType) => async (dispatch) => {
  const promises = projectByType.map((project) => {
    return populateProject(project, dbDetail);
  });
  try {
    await Promise.all(promises);
    return true;
  } catch (error) {
    dispatch({
      type: PROJECT_LOADING_FALSE,
    });
    return false;
  }
};

export const populateProject = async (project, dbDetail) => {
  // get the assigned locations for the logged in user
  return Promise.all([
    insertIntoTable(tables.PROJECTS_TABLE, [project], dbDetail),
    insertIntoTable(tables.AREAS_TABLE, project.areasData, dbDetail),
    insertIntoTable(tables.ELEMENTS_TABLE, project.elementsData, dbDetail),
    insertIntoTable(tables.TASKS_TABLE, project.tasksData, dbDetail),
    insertIntoTable(tables.LOCATIONS_TABLE, project.assignedLocations, dbDetail),
    insertIntoTable(tables.CATEGORIES_TABLE, project.categories, dbDetail),
    // insertIntoTable(tables.ROLES_TABLE, project.rolesData, dbDetail),
    insertIntoTable(tables.GROUPS_TABLE, project.groups, dbDetail),
    insertIntoTable(tables.REMINDERS_TABLE, project.reminders, dbDetail),
    insertIntoTable(tables.QUESTIONS_TABLE, project.questions, dbDetail),
  ])
    .then((values) => {
      return project;
    })
    .catch((error) => {
      console.log(error, 'error');
      return project;
    });
};
export const getProjects = (dbDetail) => async (dispatch) => {
  dispatch({
    type: PROJECT_LOADING,
  });
  try {
    let data = await getAllData(tables.PROJECTS_TABLE, dbDetail);

    dispatch({
      type: PROJECT_LIST,
      payload: data,
    });
  } catch (error) {
    console.log(error, 'error');
    dispatch({
      type: PROJECT_LOADING_FALSE,
    });
    dispatch({
      type: ALERT_ERROR,
      payload: NO_DATA_FOUND,
    });
  }
};
