import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Keyboard, StyleSheet, View } from 'react-native';
import React, { useState, useEffect } from 'react';
import { Switch, useTheme, Text } from 'react-native-paper';
import { CommonActions, useIsFocused } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';

import { AText, MainLayout } from '_theme_components';
import { ALERT_TITLE, FontStyle, tables, windowHeight, windowWidth } from '_utils';
import { canStartStudy, saveStudyTitle, studyStarted } from '_provider';
import { createTable, getAllData, insertIntoTable, isEmpty, storeData } from '_helpers';

const StartStudy = ({ navigation, route }) => {
  const { studyTitle } = route.params;

  const isFocused = useIsFocused();
  const dispatch = useDispatch();
  const [timerCount, setTimer] = useState(3);

  const { dbDetail, userDetails } = useSelector((state) => state.user);
  const project = useSelector((state) => state.createStudy.projectSelect);
  const { locationSelect } = useSelector((state) => state.createStudy);

  useEffect(() => {
    if (isFocused) {
      let interval;
      setTimer(3);
      interval = setInterval(() => {
        if (timerCount > 0) {
          setTimer((lastTimerCount) => {
            if (lastTimerCount == 1) return clearInterval(interval), navigateTonext();
            else return lastTimerCount - 1;
          });
        }
      }, 1000);
    }
  }, [isFocused]);
  useEffect(() => {
    if (isFocused) {
      onStart();
    }
  }, [isFocused]);

  const navigateTonext = () => {
    navigation.dispatch(
      CommonActions.reset({
        index: 1,
        routes: [{ name: 'StudyPage' }],
      })
    );
  };
  useEffect(() => {
    const backHandler = BackHandler.addEventListener('hardwareBackPress', () => true);
    return () => backHandler.remove();
  }, []);
  const onStart = async () => {
    dispatch(studyStarted(project._id, dbDetail));
    createTables();
  };
  const createTables = async () => {
    const studyTable = await createTable(tables.STUDY_TABLE, dbDetail);
    const studyDataTable = await createTable(tables.STUDY_DATA_TABLE, dbDetail);
    const recoverDataTable = await createTable(tables.RECOVERY_DATA_TABLE, dbDetail);
    new Promise.all([studyTable, studyDataTable, recoverDataTable]).then(async (result) => {
      saveStudyTitle(studyTitle);
      // /* SETTING ALL DATA OBJECTS */

      var StudyData = [
        {
          name: studyTitle,
          customerID: project.customerID,
          projectID: project._id,
          projectRating: project.rating,
          studyStartTime: new Date().getTime(),
          locationID: locationSelect._id,
          StudyEndTime: '',
          userId: userDetails._id,
          locationName: locationSelect.locationname,
          customerName: project.customer_name,
        },
      ];
      storeData('studyStartedAt', Date.now().toString());
      let addData = insertIntoTable(tables.STUDY_TABLE, StudyData, dbDetail);
      Promise.all([addData]).then(async () => {
        let studyDataStore = await getAllData(tables.STUDY_TABLE, dbDetail);
        StudyData = {
          ...StudyData[0],
          StudyID: isEmpty(studyDataStore) ? 1 : studyDataStore[studyDataStore.length - 1].id,
        };
        dispatch({
          type: 'STUDY_DATA_STORE',
          payload: StudyData,
        });
      });
    });
  };

  return (
    <MainLayout hideScroll navigation={navigation}>
      <View style={styles.container}>
        <AText
          styleText={{ paddingTop: 80, textAlign: 'center' }}
          fontWeight={FontStyle.fontMedium}
          fontSize={'Heading'}
        >
          Study will start..
        </AText>
        <View style={styles.countDownContainerStyle}>
          <View style={styles.countDownStyle}>
            <Text style={styles.counterTextStyle}>{timerCount}</Text>
          </View>
        </View>
      </View>
    </MainLayout>
  );
};

export default StartStudy;

const styles = StyleSheet.create({
  container: {
    padding: 15,
    flex: 1,
    alignSelf: 'center',
  },
  countDownContainerStyle: {
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    flex: 1,
  },
  countDownStyle: {
    height: 300,
    width: 300,
    borderWidth: 5,
    borderRadius: 200,
    justifyContent: 'center',
    alignItems: 'center',
    borderColor: '#DEE1E6',
  },
  counterTextStyle: {
    fontSize: 155,
    fontFamily: FontStyle.fontBold,
  },
});
