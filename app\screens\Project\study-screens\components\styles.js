import { StyleSheet } from 'react-native';

export const styles = StyleSheet.create({
  sortmodalContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 15,
    paddingVertical: 25,
    justifyContent: 'space-between',
    width: '100%',
  },
  sortLineContainer: {
    borderBottomWidth: 1,
    width: '97%',
    marginTop: 15,
    borderColor: '#c7c7c7',
    alignSelf: 'center',
  },
  sortContainer: {
    width: '87%',
    alignSelf: 'center',
  },
  sortBtnStyle: {
    alignItems: 'center',
    padding: 7,
    width: 105,
    marginEnd: 25,
    borderRadius: 40,
    justifyContent: 'center',
  },
  // -create element modal
  modalContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    alignSelf: 'center',
    width: '93%',
  },
  radiobtnView: {
    flexDirection: 'row-reverse',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 22,
    width: '100%',
    paddingVertical: 0,
  },
  backBtnStyle: {
    marginStart: 10,
    width: 60,
    height: 60,
    borderRadius: 100,
    marginEnd: 10,
  },
  btnGrp: {
    flexDirection: 'row',
  },
  allowancesView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 15,
  },
  percentIcon: {
    color: 'gray',
    position: 'absolute',
    alignSelf: 'center',
    paddingTop: 9,
    right: 20,
  },
  allowanceInput: {
    justifyContent: 'center',
    alignSelf: 'flex-start',
    alignItems: 'center',
    flexWrap: 'wrap',
    flexDirection: 'row',
  },
});
