import React from 'react';
import { StyleSheet, Text, Pressable } from 'react-native';
import { useTheme } from 'react-native-paper';
import { FontStyle, getFontSize } from '_utils';
import PropTypes from 'prop-types';

const ButtonStyle = ({
  disabled,
  btnStyle,
  onPress,
  icon,
  styleText,
  title,
  fontWeight = FontStyle.fontMedium,
  fontSize,
  bgColor,
  mode,
}) => {
  ButtonStyle.propTypes = {
    disabled: PropTypes.bool,
    btnStyle: PropTypes.object,
    onPress: PropTypes.func,
    icon: PropTypes.object,
    contentStyles: PropTypes.object,
    styleText: PropTypes.object,
    title: PropTypes.string,
    fontWeight: PropTypes.string,
    alignself: PropTypes.string,
    fontSize: PropTypes.string,
    bgColor: PropTypes.string,
    mode: PropTypes.string,
  };
  const { colors } = useTheme();
  const getButtonStyle = () => {
    return StyleSheet.create({
      buttonStyle: {
        alignSelf: 'center',
        opacity: 0.9,
        flexDirection: 'row',
        justifyContent: 'center',
        padding: 5,
      },
      textStyle: {
        textAlign: 'center',
        fontFamily: fontWeight,
        textAlignVertical: 'center',
        color: '#fff',
        fontSize: fontSize ? getFontSize(fontSize) : getFontSize('medium'),
        textTransform: 'uppercase',
      },
      pressableStyle: { justifyContent: 'center', paddingVertical: 8 },
    });
  };

  const buttonBackground =
    mode === 'text'
      ? 'transparent'
      : disabled && mode !== 'text'
        ? '#c2c2c2'
        : bgColor || colors.primary;

  const buttonStyle = [
    getButtonStyle().buttonStyle,
    {
      backgroundColor: buttonBackground,
      alignItems: mode !== 'text' ? 'center' : 'flex-start',
      paddingVertical: 8,
    },
    btnStyle,
  ];

  return (
    <>
      <Pressable onPress={onPress} disabled={disabled ?? false} style={buttonStyle}>
        {icon ? icon : null}
        <Text style={[getButtonStyle().textStyle, styleText]}>{title}</Text>
      </Pressable>
    </>
  );
};

export default ButtonStyle;
