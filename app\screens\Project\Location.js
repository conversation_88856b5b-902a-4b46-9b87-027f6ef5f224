import {
  <PERSON><PERSON><PERSON><PERSON>,
  FlatList,
  Linking,
  Platform,
  RefreshControl,
  StyleSheet,
  View,
} from 'react-native';
import React, { useContext, useEffect, useState, useCallback } from 'react';
import { useTheme } from 'react-native-paper';
import { useDispatch, useSelector } from 'react-redux';
import { useIsFocused } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/AntDesign';
import Ionicons from 'react-native-vector-icons/Ionicons';

import { isEmpty, useDebounce } from '_helpers';
import { AButton, AText, AuthLoading, BottomModalUI, MainLayout } from '_theme_components';
import { FontStyle, INTERNET_ERROR, windowHeight } from '_utils';
import { LocationCard } from '_components';

import { fetchDataAction } from '_action';
import { ALERT_ERROR } from '../../store/reducers/alert';
import { retrieveStudyItemsData } from '_provider';
import { AppThemeContext } from '../../hooks/themeContext';

const LocationScreen = ({ navigation, route }) => {
  const { dark } = useTheme();
  const dispatch = useDispatch();
  const isFocused = useIsFocused();
  const { debounce } = useDebounce();
  const { themeLoader } = useContext(AppThemeContext);

  const [locationData, setLocationData] = useState([]);
  const [modalShow, setModalShow] = useState(false);
  const [loader, setLoader] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [locationID, setLocationID] = useState('');

  const { dbDetail } = useSelector((state) => state.user);
  const project = useSelector((state) => state.createStudy.projectSelect);
  const { netConnection } = useSelector((state) => state.netInfo);
  const { projectByType } = useSelector((state) => state.projectReducer);
  const { locationList } = useSelector((state) => state.serverReducer);

  useEffect(() => {
    if (isFocused) {
      global.STUDYPAGESELECT = 'area';
      dispatch({ type: 'CLEAR_STUDY' });
    } else {
      setLoader(false);
      setLocationID('');
    }
  }, [isFocused]);

  useEffect(() => {
    if (!isEmpty(locationList)) {
      setLocationData(locationList);
    }
  }, [locationList]);

  const getLocations = async (refresh) => {
    if (!refreshing && isEmpty(refresh)) {
      setLoader(true);
    }
    await dispatch(retrieveStudyItemsData(project._id, dbDetail));
    setRefreshing(false);
    setLoader(false);
  };

  const navigateToNext = () => {
    dispatch({
      type: 'STORE_LOCATION',
      payload: locationData[locationID],
    });
    setModalShow(false);
    navigation.navigate('CreateStudyArea');
  };

  const ItemView = ({ item, index }) => {
    return (
      <View key={item.id}>
        <LocationCard
          navigateTo={() => {
            setModalShow(true);
            setLocationID(index);
          }}
          navigation={navigation}
          data={item}
        />
      </View>
    );
  };

  useEffect(() => {
    const backHandler = BackHandler.addEventListener('hardwareBackPress', backAction);
    return () => backHandler.remove();
  }, []);

  var count = 0;
  const backAction = () => {
    if (count <= 1) {
      count += 1;
      dispatch({
        type: 'ALERT_CLOSEAPP',
        payload: 'Press back again to exit the app',
      });
      setTimeout(() => {
        count = 0;
      }, 2000);
    } else {
      dispatch({ type: 'ALERT_HIDE' });
      count = 0;
      BackHandler.exitApp();
    }
    return true;
  };
  const onRefresh = async () => {
    setRefreshing(true);
    if (!netConnection) {
      dispatch({
        type: ALERT_ERROR,
        payload: INTERNET_ERROR,
      });
      setRefreshing(false);
      return;
    } else {
      await dispatch(fetchDataAction(dbDetail));
    }
  };

  useEffect(() => {
    if (isFocused && !themeLoader) {
      getLocations();
    }
  }, [isFocused, projectByType]);

  return (
    <>
      {loader && <AuthLoading />}
      <MainLayout hideScroll ShowDarkMode back navigation={navigation} headerShow>
        <View style={styles.HeaderContainer}>
          <AText
            fontWeight={FontStyle.fontBold}
            styleText={{ color: dark ? '#777778' : '#3C4555' }}
            fontSize={'jumbo'}
          >
            {project.name}
          </AText>
        </View>
        <View style={styles.contentContainer}>
          {!isEmpty(locationData) ? (
            <FlatList
              refreshControl={
                <RefreshControl refreshing={refreshing} onRefresh={() => debounce(onRefresh())} />
              }
              data={locationData}
              style={{ flex: 1 }}
              showsVerticalScrollIndicator={false}
              showsHorizontalScrollIndicator={false}
              keyExtractor={(key, index) => index.toString()}
              renderItem={ItemView}
            />
          ) : isEmpty(locationData) && !loader ? (
            <View style={styles.emptyContainerStyle}>
              <AText
                styleText={{ textAlign: 'center' }}
                fontSize={'medium'}
                fontWeight={FontStyle.fontBold}
              >
                No location Found
              </AText>
            </View>
          ) : null}
        </View>
      </MainLayout>

      <BottomModalUI
        width={'84%'}
        closeShow={false}
        ModalClose={true}
        closeModal={() => {
          setModalShow(false);
          setLocationID('');
        }}
        height={
          Platform.OS === 'ios'
            ? windowHeight * 0.2
            : windowHeight > 1000
              ? windowHeight * 0.175
              : windowHeight * 0.25
        }
        ModalStyle={styles.modalStyle}
        modalShow={modalShow}
      >
        <View style={styles.modalbuttonContainer}>
          <AButton
            width={'95%'}
            mode={'text'}
            btnStyle={styles.contentStyles}
            icon={
              <Icon
                name="addfile"
                style={styles.iconStyle}
                color={dark ? '#fff' : 'black'}
                size={27}
              />
            }
            fontSize={'large'}
            title={'Create study'}
            styleText={{ color: dark ? '#fff' : 'black' }}
            onPress={() => navigateToNext()}
          />
          <View style={styles.lineContainView} />
          <AButton
            mode={'text'}
            width={'95%'}
            btnStyle={{ ...styles.contentStyles, marginTop: 10 }}
            icon={
              <Ionicons
                name="location-outline"
                style={styles.iconStyle}
                color={dark ? '#fff' : 'black'}
                size={27}
              />
            }
            fontSize={'large'}
            title={'See address in Map'}
            styleText={{ color: dark ? '#fff' : 'black' }}
            onPress={() =>
              Linking.openURL(
                `https://www.google.com/maps/search/?api=1&query=${locationData[locationID].address}${locationData[locationID].postcode}`
              )
            }
          />
        </View>
      </BottomModalUI>
    </>
  );
};

export default LocationScreen;

const styles = StyleSheet.create({
  HeaderContainer: {
    flexDirection: 'row',
    marginTop: 5,
    marginLeft: 10,
    width: '90%',
    alignSelf: 'center',
  },
  contentContainer: {
    width: '92%',
    alignSelf: 'center',
    flex: 1,
    paddingBottom: 0,
    marginTop: 15,
  },
  modalStyle: {
    flexDirection: 'column',
    justifyContent: 'flex-start',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 1.65,
    elevation: Platform.OS == 'ios' ? 0 : 122,
    flex: 1,
    // backgroundColor: 'red'
  },
  modalbuttonContainer: {
    flexDirection: 'column',
    justifyContent: 'flex-start',
  },
  lineContainView: {
    width: '90%',
    alignSelf: 'center',
    height: 1,
    backgroundColor: '#cdcdcd',
  },
  iconStyle: {
    alignSelf: 'center',
    marginEnd: 5,
  },
  contentStyles: {
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    paddingHorizontal: 25,
    width: '90%',
    paddingVertical: 10,
  },
  emptyContainerStyle: {
    flex: 1,
    justifyContent: 'center',
  },
});
