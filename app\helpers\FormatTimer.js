import { isEmpty } from "./isEmpty"

export const formatTime = (timer, disp) => {
    const getSeconds = `0${(timer % 60)}`.slice(-2)
    const minutes = `${Math.floor(timer / 60)}`
    const getMinutes = `0${minutes % 60}`.slice(-2)
    var getHours = `0${Math.floor(timer / 3600)}`.slice(-2)
    var timeDisplay;
    // if(disp=='studyTime'){
    //     timeDisplay=`${getHours} : ${getMinutes} : ${getSeconds}`;
    // }else 
    if (disp == 'studyTime') {
        getHours = (!isEmpty(getHours) && getHours !== '00') ? `${getHours}h : ` : ''
        timeDisplay = `${getHours}${getMinutes}m : ${getSeconds}s`;

    } else {
        timeDisplay = `${getHours} : ${getMinutes} : ${getSeconds}`;
    }

    return timeDisplay
}