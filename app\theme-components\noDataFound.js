import { Image, StyleSheet, Text, View } from 'react-native';
import React from 'react';
import { AText } from '_theme_components';
import { FontStyle, windowHeight, windowWidth } from '_utils';
import { isEmpty } from '_helpers';

const NoDataFound = ({ source, text, imageStyle, styleText }) => {
  return (
    <View style={styles.container}>
      {!isEmpty(source) && (
        <Image
          source={source}
          style={[
            styles.imageStyle,
            { width: windowWidth * 0.45, height: windowHeight * 0.3 },
            imageStyle,
          ]}
        />
      )}
      <AText
        lightGray
        styleText={{ ...styles.styleText, width: windowWidth * 0.6, ...styleText }}
        fontWeight={FontStyle.fontMedium}
        fontSize={'medium'}
      >
        {text}
      </AText>
    </View>
  );
};

export default NoDataFound;

const styles = StyleSheet.create({
  imageStyle: {
    resizeMode: 'contain',

    marginVertical: 15,
  },
  container: {
    alignSelf: 'center',
    justifyContent: 'center',
    alignItems: 'center',
  },
  styleText: { paddingTop: 10, textAlign: 'center' },
});
