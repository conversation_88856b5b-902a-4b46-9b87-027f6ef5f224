import React, { useEffect, useRef, useState } from 'react';
import { View } from 'react-native';
import { FontStyle, tables, windowHeight, windowWidth } from '_utils';
import { AText, BottomModalUI } from '_theme_components';
import {
  LoadingProgressBar,
  ProgressBar,
  deleteRecord,
  isEmpty,
  markStudyAsSynced,
} from '_helpers';
import { useDispatch, useSelector } from 'react-redux';
import { syncingStatusCheck } from '_action';
import Icon from 'react-native-vector-icons/FontAwesome';
import { styles } from './styles';

const SyncingModal = ({}) => {
  const [studiesSyncingID, setStudiesSyncingID] = useState([]);
  const [showModal, setShowModal] = useState(false);
  const [modalHeight, setModalHeight] = useState(windowHeight * 0.32);
  const [modalClose, setModalClose] = useState(false);
  const [syncingDetails, setSyncingDetails] = useState([]);

  const { syncedStudyIDs, syncingStudyDetail, studySumbitStarted, studyCount } = useSelector(
    (state) => state.study
  );
  const dispatch = useDispatch();
  const { dbDetail } = useSelector((state) => state.user);
  var intervalIdRef = useRef(null);

  useEffect(() => {
    setSyncingDetails(syncingStudyDetail);
  }, [syncingStudyDetail]);

  useEffect(() => {
    setTimeout(() => {
      checkStatus();
    }, 2000);
  }, [syncingDetails]);

  const checkStatus = async () => {
    if (!isEmpty(syncingStudyDetail) && !isEmpty(studiesSyncingID)) {
      dispatch({
        type: 'SYNCDATA_LOADING',
      });
      const studiesToMarkAsSynced = [];
      syncingStudyDetail.forEach((item) => {
        let indexFound = studiesSyncingID.findIndex((n) => n.syncedStudyID === item._id);

        if (item.status === 'COMPLETED' && indexFound > -1) {
          markStudyAsSynced(item._id, dbDetail);
          studiesToMarkAsSynced.push(item._id);
          deleteRecord(
            tables.RECOVERY_DATA_TABLE,
            { studyID: studiesSyncingID[indexFound].studyId },
            dbDetail
          );
        } else if (item.status === 'ERROR' && indexFound > -1) {
          dispatch({
            type: 'SYNCDATA_LOADING_FALSE',
          });
          setModalClose(true);
        }
      });
      // Mark studies as synced, update state, and close modal if needed
      if (studiesToMarkAsSynced.length > 0) {
        let syncStudies = studiesSyncingID.filter(
          (val) => !studiesToMarkAsSynced.includes(val.syncedStudyID)
        );

        if (isEmpty(syncStudies)) {
          closeModal();
        } else {
          setStudiesSyncingID(syncStudies);
          dispatch({
            type: 'SYNCDATA_LOADING_FALSE',
          });
        }
      }
    }
  };

  useEffect(() => {
    if (!isEmpty(syncedStudyIDs)) {
      setStudiesSyncingID(syncedStudyIDs);
      fetchData(syncedStudyIDs);
      setShowModal(true);
      if (!showModal) {
        setModalHeight(
          studyCount <= 2
            ? windowWidth > windowHeight
              ? windowHeight * 0.32
              : windowHeight * 0.35
            : studyCount < 4
              ? windowWidth > windowHeight
                ? windowHeight * 0.42
                : windowHeight * 0.45
              : studyCount <= 6
                ? windowWidth > windowHeight
                  ? windowHeight * 0.62
                  : windowHeight * 0.6
                : windowHeight * 0.72
        );
      }
    }
  }, [studySumbitStarted, syncedStudyIDs]);

  const closeModal = () => {
    setShowModal(false);
    setStudiesSyncingID([]);
    clearInterval(intervalIdRef.current);
    intervalIdRef.current = null;
    dispatch({ type: 'CLEAR_SYNCING_STUDIES' });
    setTimeout(() => {
      dispatch({
        type: 'SYNCDATA_LOADING_FALSE',
      });
    }, 600);
  };

  useEffect(() => {
    if (!isEmpty(syncedStudyIDs)) {
      intervalIdRef.current = setInterval(fetchData, 10000);
    }
    // Cleanup function
    return () => {
      console.log('Cleanup function');
      if (intervalIdRef.current) {
        console.log('Clearing interval explicitly');
        clearInterval(intervalIdRef.current);
      } else {
        console.log('Interval was already null or undefined');
      }

      // Set intervalIdRef.current to null explicitly
      intervalIdRef.current = null;
    };
  }, [studiesSyncingID]);

  const fetchData = async (ids) => {
    let idCheck = ids ?? studiesSyncingID;

    if (!isEmpty(idCheck)) {
      const arrayOfIds = idCheck.map((item) => item.syncedStudyID);
      let payload = {
        syncedStudyIDs: arrayOfIds,
      };
      try {
        await dispatch(syncingStatusCheck(payload));
      } catch (error) {
        console.error('Error checking sync status:', error);
      }
    }
  };

  const RenderObservation = ({ id, isSynced, invalidObs, loading, progress }) => {
    return (
      <View style={styles.syncingContainer}>
        <View style={styles.idContainerStyle}>
          <AText fontWeight={FontStyle.fontBold} fontSize="small">
            {id}
          </AText>
          {isSynced == 'ERROR' || isSynced == 'COMPLETED' ? (
            <View
              style={isSynced == 'COMPLETED' ? styles.synceViewStyle : styles.errorSynceViewStyle}
            >
              {isSynced == 'COMPLETED' ? (
                <Icon name="check-circle" style={styles.iconStyle} color={'#0AAF39'} size={15} />
              ) : (
                <View style={styles.errorDotStyle}></View>
              )}
              <AText
                styleText={{ color: '#0AAF39' }}
                fontWeight={FontStyle.fontBold}
                fontSize={'xxtrasmall'}
              >
                {isSynced == 'ERROR' ? ` Unsynced observation -${invalidObs} ` : `Synced`}
              </AText>
            </View>
          ) : null}
        </View>
        <View style={styles.progressViewStyle}>
          {loading ? <LoadingProgressBar /> : <ProgressBar progress={loading ? 0 : progress} />}
          {/* <ProgressBar progress={loading ? 0 : progress} /> */}
        </View>
      </View>
    );
  };

  return (
    <>
      <BottomModalUI
        width={'100%'}
        height={modalHeight}
        closeModal={() => closeModal()}
        closeShow={modalClose}
        showScroll
        title={'Studies Syncing Status'}
        styleTitle={{ marginHorizontal: 35, marginVertical: 15 }}
        ModalClose={false}
        modalShow={showModal}
      >
        <View style={styles.ViewcontainerStyle}>
          {studiesSyncingID.map(({ syncedStudyID, studyName }) =>
            syncingDetails.findIndex((n) => n._id == syncedStudyID) < 0 ? (
              <RenderObservation id={studyName} isSynced={'PENDING'} loading={true} progress={0} />
            ) : null
          )}
          {syncingDetails.map(({ studyName, status, progress, invalidObs }) => (
            <RenderObservation
              id={studyName}
              isSynced={status}
              invalidObs={invalidObs}
              loading={false}
              progress={progress}
            />
          ))}
        </View>
      </BottomModalUI>
    </>
  );
};

export default SyncingModal;
