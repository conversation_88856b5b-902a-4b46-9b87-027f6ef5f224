import { getData, isEmpty } from '_helpers';
import { LoginAction } from '_action';
import ReactNativeBiometrics, { BiometryTypes } from 'react-native-biometrics';

export const biometricDetail = async () => {
  try {
    const biometricLoginData = await getData('BiometricLogin');
    let bioMetricDetails = biometricLoginData ? JSON.parse(biometricLoginData) : false;
    const rnBiometrics = new ReactNativeBiometrics();
    const { available, biometryType } = await rnBiometrics.isSensorAvailable();
    let biometricData = {
      title: 'Face ID Login',
      biometricSupport: false,
      biometricEnabled: false,
    };
    if (
      available &&
      (biometryType === BiometryTypes.FaceID ||
        biometryType === BiometryTypes.TouchID ||
        biometryType === BiometryTypes.Biometrics)
    ) {
      biometricData = {
        title: biometryType === BiometryTypes.FaceID ? 'FaceID Login' : 'Fingerprint ID Login',
        biometricSupport: true,
        biometricEnabled: isEmpty(biometricLoginData)
          ? ''
          : biometricLoginData && bioMetricDetails.email && bioMetricDetails.passKey
            ? true
            : false,
      };
    }
    return biometricData;
  } catch (error) {
    // Handle error, e.g., log or return a default biometricData object
    return {
      title: 'FaceID Login',
      biometricSupport: false,
      biometricEnabled: false,
    };
  }
};

export const biometricAuthentication = async () => {
  try {
    const rnBiometrics = new ReactNativeBiometrics();

    const { success } = await rnBiometrics.simplePrompt({
      promptMessage: 'Authenticate with your biometrics',
    });
    if (success) {
      return true;
    }
  } catch (errors) {
    return false;
  }
};
