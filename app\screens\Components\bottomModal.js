import React from 'react';
import { View } from 'react-native';
import { FontStyle, windowHeight, windowWidth } from '_utils';
import { AButton, AText, BottomModalUI, LinearGradientButton } from '_theme_components';
import { styles } from './styles';
import { useTheme } from 'react-native-paper';

const BottomModal = ({
  modalShow,
  cancelShow,
  title,
  body,
  confirm,
  reject,
  height,
  confirmPress,
  ModalClose,
  clrBody,
  ButtonContainer,
}) => {
  const { dark } = useTheme();
  return (
    <>
      {modalShow && (
        <BottomModalUI
          width={'100%'}
          height={
            height || (windowWidth > windowHeight ? windowHeight * 0.32 : windowHeight * 0.27)
          }
          closeModal={cancelShow}
          ModalClose={ModalClose ?? false}
          modalShow={modalShow}
        >
          <View style={styles.bottomModalContainerStyle}>
            <AText fontWeight={FontStyle.fontBold} fontSize={'title'}>
              {title}
            </AText>
            <AText
              styleText={{ color: clrBody ? clrBody : dark ? '#fff' : '#000', paddingTop: 10 }}
              fontWeight={FontStyle.fontRegular}
              fontSize={'medium'}
            >
              {body}
            </AText>
            <View style={[styles.buttonContainerStyle, ButtonContainer]}>
              <AButton
                mode="text"
                btnStyle={styles.bottomModalConfirmBtnStyles}
                styleText={{ color: 'red', textTransform: 'capitalize' }}
                title={confirm}
                onPress={confirmPress}
              />
              <View style={{ width: windowWidth * 0.35 }}>
                <LinearGradientButton
                  contentStyles={styles.bottomModalBtnContentStyle}
                  styleText={{ textTransform: 'capitalize' }}
                  title={reject}
                  onPress={cancelShow}
                />
              </View>
            </View>
          </View>
        </BottomModalUI>
      )}
    </>
  );
};

export default BottomModal;
