import React, { useEffect, useState } from 'react';
import { Keyboard, ScrollView } from 'react-native';
import { Card, useTheme } from 'react-native-paper';
import { useDispatch, useSelector } from 'react-redux';

import { AButton } from '_theme_components';
import { FontStyle, windowWidth } from '_utils';
import { CustomSwitch, Formiks, getData, storeData } from '_helpers';

import { BottomModal } from '../Components';
import { LogoutAction, updateProfileAction } from '_action';
import { biometricDetail } from '_provider';
import { prolfileValidationSchema } from '_validation';
import { generateButtonData, profilefieldsArray } from './constants';
import { styles } from './styles';

const ProfileScreen = ({ navigation }) => {
  const { dark } = useTheme();
  const dispatch = useDispatch();

  const [biometricDetails, setBiometriDetails] = useState({
    biometricType: 'FaceID Login',
    showBiometicOption: false,
    biometricSwitch: false,
  });
  const { userDetails, dbDetail } = useSelector((state) => state.user);
  const [logoutModal, setLogoutModal] = useState(false);
  const setBiometricData = async () => {
    const updatedSwitchState = !biometricDetails.biometricSwitch;
    setBiometriDetails({
      ...biometricDetails,
      biometricSwitch: updatedSwitchState,
    });
    let storeKey = {};
    if (updatedSwitchState) {
      storeKey = {
        email: await getData('mailID'),
        passKey: await getData('passKey'),
      };
    }
    const dataToStore = updatedSwitchState ? JSON.stringify(storeKey) : 'false';
    await storeData('BiometricLogin', dataToStore);
  };

  const buttonData = generateButtonData(dark, setBiometricData, setLogoutModal, navigation);

  const SubmitForm = (val) => {
    Keyboard.dismiss();
    const payload = {
      name: val.firstName,
      lastname: val.lastName,
    };
    dispatch(updateProfileAction(payload, userDetails._id));
  };

  useEffect(() => {
    const viewModal = async () => {
      const biometricData = await biometricDetail();
      setBiometriDetails({
        biometricType: biometricData.title,
        showBiometicOption: biometricData.biometricSupport,
        biometricSwitch: biometricData.biometricEnabled,
      });
    };

    viewModal();
  }, []);

  const ListComponent = ({ item }) => {
    const { value, style, name, arrowIcon } = item;
    const handlePress = () => {
      value(); // Assuming value is a function to be executed on press
    };
    return (biometricDetails.showBiometicOption && name == 'Biometriclogin') ||
      name !== 'Biometriclogin' ? (
      <AButton
        mode="text"
        onPress={handlePress}
        btnStyle={style}
        fontWeight={FontStyle.fontMedium}
        fontSize={'medium'}
        title={name == 'Biometriclogin' ? biometricDetails.biometricType : name}
        styleText={{ color: dark ? '#fff' : '#000', textAlign: 'left' }}
        icon={
          name == 'Biometriclogin' ? (
            <CustomSwitch
              selected={biometricDetails.biometricSwitch}
              option1={'ON'}
              option2={'OFF'}
              updatedSwitchData
              onSelectSwitch={() => {
                setBiometricData();
              }}
              selectionColor={'blue'}
            />
          ) : (
            arrowIcon
          )
        }
      />
    ) : null;
  };

  return (
    <>
      <ScrollView
        showsVerticalScrollIndicator={false}
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollViewContent}
        style={styles.Viewcontainer}
      >
        <Card style={styles.projectCardstyle}>
          <Card.Content>
            <Formiks
              validation={prolfileValidationSchema}
              fieldArray={profilefieldsArray}
              btntitle={'save changes'}
              btnContentStyle={styles.btnContentStyle}
              loginPage={false}
              btnStyle={styles.saveBtnStyle}
              viewStyle={[styles.saveChangeButton, { width: windowWidth * 0.35 }]}
              submitChange={(values) => SubmitForm(values)}
            />
          </Card.Content>
        </Card>
        <Card style={styles.profileProjectCardstyle}>
          <Card.Content>
            {buttonData.map((item) => (
              <ListComponent item={item} />
            ))}
          </Card.Content>
        </Card>
      </ScrollView>
      <BottomModal
        modalShow={logoutModal}
        confirmPress={() => dispatch(LogoutAction(dbDetail))}
        cancelShow={() => setLogoutModal(false)}
        title={'Logout Confirm'}
        body={'Are you sure that you want to logout?'}
        confirm={'yes'}
        reject={' no '}
      />
    </>
  );
};

export default ProfileScreen;
