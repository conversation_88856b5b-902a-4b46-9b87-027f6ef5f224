import { View } from 'react-native';
import React from 'react';
import { Card } from 'react-native-paper';
import { AText } from '_theme_components';
import { FontStyle } from '_utils';
import Icon from 'react-native-vector-icons/Ionicons';
import { isEmpty } from '_helpers';
import { styles } from './styles';
const LocationCard = ({ data, navigateTo }) => {
  return (
    <Card onPress={navigateTo} style={styles.locationCardstyle}>
      <>
        <Card.Content>
          <AText
            styleText={{ textTransform: 'capitalize' }}
            fontSize={'large'}
            fontWeight={FontStyle.fontBold}
          >
            {data.locationname}
          </AText>
          <View style={styles.locationCardRow}>
            {!isEmpty(data.contactname) && (
              <View style={styles.locationCardElement}>
                <Icon name="person-outline" style={styles.iconStyle} color={'#878787'} size={20} />
                <AText fontSize={'small'} lightGray fontWeight={FontStyle.fontMedium}>
                  {data.contactname}
                </AText>
              </View>
            )}
            {!isEmpty(data.telephone) && (
              <View style={styles.locationCardElement}>
                <Icon name="call-outline" style={styles.iconStyle} color={'#878787'} size={20} />
                <AText fontSize={'small'} lightGray fontWeight={FontStyle.fontMedium}>
                  {data.telephone}
                </AText>
              </View>
            )}
          </View>
          {!isEmpty(data.address) && (
            <View style={styles.locationCardElement}>
              <Icon name="location-outline" style={styles.iconStyle} color={'#878787'} size={20} />
              <AText fontSize={'small'} lightGray fontWeight={FontStyle.fontMedium}>
                {data.address} {data.postcode}
              </AText>
            </View>
          )}
        </Card.Content>
      </>
    </Card>
  );
};

export default LocationCard;
