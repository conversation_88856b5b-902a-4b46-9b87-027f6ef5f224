import { StyleSheet, View } from 'react-native';
import React, { useEffect, useState } from 'react';
import { useIsFocused } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';

import {
  AText,
  Textinputs,
  AuthLoading,
  LinearGradientButton,
  MainLayout,
} from '_theme_components';
import { FontStyle, windowWidth, EMAIL_REGEXP } from '_utils';
import { isEmpty } from '_helpers';
import { resetPasswordAction } from '_action';

const ResetPasswordScreen = ({ navigation }) => {
  const isFocused = useIsFocused();
  const dispatch = useDispatch();
  const { loading } = useSelector((state) => state.login);
  const [emailInfo, setEmailInfo] = useState('');
  const [errors, setErrors] = useState('');

  useEffect(() => {
    if (!isFocused) {
      setErrors('');
      setEmailInfo('');
    }
  }, [isFocused]);

  const submitForm = () => {
    setErrors('');
    if (isEmpty(emailInfo)) {
      setErrors('Please enter email');
      return;
    } else if (!EMAIL_REGEXP.test(emailInfo)) {
      setErrors('Please enter valid email');
      return;
    }
    dispatch(resetPasswordAction(emailInfo, navigation));
  };
  return (
    <>
      {loading ? <AuthLoading /> : null}
      <MainLayout
        headerShow
        hideScroll
        paddingtop={0}
        paddinghorizontal={0}
        bgColor={true}
        title={``}
        back
        navigation={navigation}
      >
        <View
          style={[
            styles.containerStyle,
            { width: windowWidth * 0.65, marginTop: windowWidth * 0.05 },
          ]}
        >
          <AText fontSize={'Heading'} fontWeight={FontStyle.fontBold}>
            Reset Password
          </AText>
          <AText fontSize={'medium'} color={'#8A8A8A'} fontWeight={FontStyle.fontMedium}>
            Please provide an email address so we can send you a reset link.
          </AText>
          <View style={styles.inputStyle}>
            <Textinputs
              label={'Email Address'}
              placeholder="Email"
              value={emailInfo}
              onchange={(val) => setEmailInfo(val)}
              error={errors ?? ''}
            />
          </View>
          <View style={styles.btnStyleView}>
            <LinearGradientButton
              btnStyle={styles.btnStyle}
              title={'SEND RESET LINK'}
              onPress={() => submitForm()}
            />
          </View>
        </View>
      </MainLayout>
    </>
  );
};

export default ResetPasswordScreen;

const styles = StyleSheet.create({
  containerStyle: {
    padding: 15,
    alignSelf: 'center',
  },
  btnStyle: {
    borderRadius: 70,
    padding: 10,
  },
  btnStyleView: {
    alignSelf: 'center',
    marginTop: 40,
    width: '55%',
  },
  inputStyle: {
    marginTop: 20,
  },
});
