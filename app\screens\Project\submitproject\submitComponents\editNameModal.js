import { StyleSheet, Text, View } from 'react-native';
import React, { useEffect, useState } from 'react';
import { AText, Textinputs, BottomModalUI, LinearGradientButton } from '_theme_components';
import { FontStyle, windowHeight, windowWidth } from '_utils';
import { isEmpty } from '_helpers';

const EditNameModal = ({
  editNameModal,
  updateStudyName,
  editStudyName,
  settStudyName,
  closeModal,
}) => {
  const [studyTitleErrorMessage, setStudyTitleErrorMessage] = useState('');
  const [keyBoardFocus, setKeyBoardFocus] = useState(false);

  useEffect(() => {
    if (editNameModal) {
      setKeyBoardFocus(true);
    } else {
      setKeyBoardFocus(false);
    }
  }, [editNameModal]);
  console.log(keyBoardFocus, 'sjsa');
  const validateTitle = () => {
    let regex = new RegExp('^[A-Za-z0-9_]*$');
    let isValidTitle = regex.test(editStudyName);
    if (!isValidTitle) {
      setStudyTitleErrorMessage('only letters, numbers and underscores are allowed');
    } else if (editStudyName.length > 30) {
      isValidTitle = false;
      setStudyTitleErrorMessage('Maximum 30 characters are allowed');
    } else {
      setStudyTitleErrorMessage('');
    }
  };

  useEffect(() => {
    validateTitle();
  }, [editStudyName]);

  return (
    <BottomModalUI
      width={'100%'}
      closeModal={() => {
        closeModal();
      }}
      height={windowHeight * 0.25}
      modalShow={editNameModal}
      // showScroll={true}
      closeShow
    >
      <View style={styles.container}>
        <AText fontWeight={FontStyle.fontBold} fontSize={'title'}>
          Edit study name{' '}
        </AText>
        <LinearGradientButton
          disabled={!isEmpty(studyTitleErrorMessage) || isEmpty(editStudyName)}
          btnStyle={{ width: windowWidth * 0.27 }}
          contentStyles={{ paddingVertical: 13 }}
          onPress={() => {
            updateStudyName();
          }}
          title={'Update'}
          fontSize={'small'}
          compact={true}
        />
      </View>
      <Textinputs
        heading={'Study Name'}
        placeholder=""
        autofocus={keyBoardFocus}
        textViewInputStyle={{ width: '87%', alignSelf: 'center' }}
        // keyboardtype={'email-address'}
        mt={10}
        stylesTextInput={{ padding: 5 }}
        value={editStudyName}
        onerror={false}
        error={studyTitleErrorMessage ?? ''}
        onchange={(val) => settStudyName(val)}
      />
    </BottomModalUI>
  );
};

export default EditNameModal;

const styles = StyleSheet.create({
  container: {
    paddingVertical: 15,
    width: '87%',
    alignSelf: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
});
