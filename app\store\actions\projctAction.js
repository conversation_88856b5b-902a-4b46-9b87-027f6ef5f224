import { executeFetch, isEmpty, mergeColumnName } from '_helpers';
import { APP_ID, INTERNET_ERROR, NO_DATA_FOUND, TOKEN_EXPIRED } from '_utils';
import { ALERT_ERROR } from '../reducers/alert';
import { createTables, dropTables, populateProject } from '_provider';
import { LogoutAction } from './loginAction';

export const fetchDataAction = (dbDetail) => async (dispatch) => {
  dispatch({
    type: PROJECT_LOADING,
  });
  try {
    const [merge, appDataResult, dropTablesResult, createTablesResult] = await Promise.all([
      await mergeColumnName(dbDetail),
      dispatch(getAppDataActionNew(dbDetail)),
      // await dispatch(dropTables(dbDetail)),
      // await dispatch(createTables(dbDetail)),
    ]);
  } catch (error) {
    dispatch({
      type: PROJECT_LOADING_FALSE,
    });
    dispatch({
      type: ALERT_ERROR,
      payload: INTERNET_ERROR,
    });
  }
};

export const getAppDataActionNew = (dbDetail) => (dispatch) => {
  executeFetch(`projects/get`, 'GET')
    .then(async (response) => {
      const { data: { data } = {} } = response;
      if (response.status === 200 && !isEmpty(data)) {
        await dispatch(dropTables(dbDetail));
        await dispatch(createTables(dbDetail));
        let projectsByType = await Promise.all(
          data.map(async (project) => {
            const projectID = checkRequestData(project._id);
            const [
              assignedLocations,
              areasData,
              tasksData,
              elementsData,
              categories,
              groups,
              reminders,
              questions,
            ] = await Promise.all([
              executeFetch('locations/get', 'Getmultiple', projectID),
              executeFetch('areas/get', 'Getmultiple', projectID),
              executeFetch('tasks/get', 'Getmultiple', projectID),
              executeFetch('elements/get', 'Getmultiple', projectID),
              executeFetch('categories/get', 'Getmultiple', projectID),
              executeFetch('groups/get', 'Getmultiple', projectID),
              executeFetch('reminders/get', 'Getmultiple', projectID),
              executeFetch('questions/get', 'Getmultiple', projectID),
            ]);
            const populatedProject = await populateProject(
              {
                ...project,
                assignedLocations,
                areasData,
                tasksData,
                elementsData: elementsData || [],
                categories,
                groups,
                reminders,
                questions,
              },
              dbDetail
            );
            return populatedProject;
            // }
          })
        );
        dispatch({
          type: PROJECT_BY_TYPE,
          payload: projectsByType,
        });
      } else if (response.status === 401 && !response.data.auth) {
        dispatch({
          type: PROJECT_LOADING_FALSE,
        });
        dispatch({
          type: ALERT_ERROR,
          payload: response.data.message ?? TOKEN_EXPIRED,
        });
        setTimeout(() => {
          dispatch(LogoutAction(dbDetail));
        }, 800);
      } else {
        dispatch({
          type: PROJECT_LOADING_FALSE,
        });
        dispatch({
          type: 'ALERT_CLOSEAPP',
          payload: NO_DATA_FOUND,
        });
      }
    })
    .catch((error) => {
      dispatch({
        type: PROJECT_LOADING_FAILED,
      });
      // dispatch({
      //   type: ALERT_ERROR,
      //   payload: 'Something went wrong. Please try again later.',
      //   // payload: INTERNET_ERROR
      // });
    });
};

const checkRequestData = (data) => {
  return data.length > 0 ? data : [];
};
const fetchTasksANDElement = async (projectID) => {
  const response = await executeFetch('tasks/get', 'Getmultiple', projectID);
  const flatarray = response.reduce((arr, tas) => arr.concat(tas.elementsDetail), []);
  return [response, flatarray];
};

export const PROJECT_LOADING = 'PROJECT_LOADING';
export const PROJECT_LOADING_FALSE = 'PROJECT_LOADING_FALSE';
export const PROJECT_BY_TYPE = 'PROJECT_BY_TYPE';
export const PROJECT_LOADING_FAILED = 'PROJECT_LOADING_FAILED';
