import { StyleSheet, Text, View } from 'react-native';
import React from 'react';
import { AText, BottomModalUI } from '_theme_components';
import { FontStyle, windowHeight, windowWidth } from '_utils';
import { isEmpty } from '_helpers';

const EditUnsyncObservation = ({ editUnSyncModal, unsynData, setEditUnSyncModal }) => {
  return (
    <BottomModalUI
      width={'100%'}
      closeModal={() => {
        setEditUnSyncModal(false);
      }}
      height={windowHeight * 0.27}
      modalShow={editUnSyncModal}
      showScroll={true}
      closeShow
    >
      <View
        style={{
          paddingVertical: 15,
          width: '87%',
          alignSelf: 'center',
          // alignItems: 'center',
        }}
      >
        <AText fontWeight={FontStyle.fontBold} fontSize={'title'}>
          Data not Synced
        </AText>
        <AText fontWeight={FontStyle.fontMedium} styleText={{ paddingTop: 10 }} fontSize={'medium'}>
          Following Data is not synced please edit observations to sync them successfully
        </AText>
        {!isEmpty(unsynData.area) && (
          <AText
            fontWeight={FontStyle.fontMedium}
            styleText={{ paddingTop: 10 }}
            fontSize={'medium'}
          >
            Areas
          </AText>
        )}

        {!isEmpty(unsynData.area) &&
          unsynData.area.map((item) => (
            <AText
              fontWeight={FontStyle.fontRegular}
              styleText={{ paddingTop: 10 }}
              fontSize={'medium'}
            >
              Area name-{item.name} at observation number-{item.index}
            </AText>
          ))}

        {!isEmpty(unsynData.element) && (
          <AText
            fontWeight={FontStyle.fontMedium}
            styleText={{ paddingTop: 10 }}
            fontSize={'medium'}
          >
            Elements
          </AText>
        )}

        {!isEmpty(unsynData.element) &&
          unsynData.element.map((item) => (
            <AText
              fontWeight={FontStyle.fontRegular}
              styleText={{ paddingTop: 10 }}
              fontSize={'medium'}
            >
              Element name-{item.name} at observation number-{item.index}
            </AText>
          ))}
      </View>
    </BottomModalUI>
  );
};

export default EditUnsyncObservation;

const styles = StyleSheet.create({});
