import { styles } from './styles';
import Icon from 'react-native-vector-icons/Ionicons';

export const changePasswordfieldsArray = [
  { id: 1, name: 'New Password', value: 'newpassword', isPassword: true },
  { id: 2, name: 'Confirm Password', value: 'confirmpassword', isPassword: true },
];

export const profilefieldsArray = [
  { id: 1, name: 'First Name', value: 'firstName', isPassword: false },
  { id: 2, name: 'Last Name', value: 'lastName', isPassword: false },
];

export const generateButtonData = (dark, setBiometricData, setLogoutModal, navigation) => {
  const buttonData = [
    {
      id: 1,
      name: 'Change password',
      style: styles.buttonStyle,
      arrowIcon: <Icon name="arrow-forward-sharp" color={dark ? '#ffff' : 'black'} size={30} />,
      value: () =>
        navigation.navigate('ProfileNav', {
          screen: 'ChangePassword',
        }),
    },
    {
      id: 2,
      name: 'Biometriclogin',
      style: styles.buttonStyle,
      arrowIcon: null,
      value: () => setBiometricData(true),
    },
    {
      id: 3,
      name: 'Logout',
      style: styles.logoutButtonStyle,
      arrowIcon: null,
      value: () => setLogoutModal(true),
    },
  ];

  return buttonData;
};
