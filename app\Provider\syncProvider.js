import { createAction, syncingStatus<PERSON>heck } from '_action';
import {
  deleteById,
  deleteRecord,
  getAllData,
  getPhotos,
  getStudiesBYSyncID,
  isEmpty,
  markStudyAsSynced,
  updateOfflineRoleInStudies,
  updateTable,
} from '_helpers';
import { tables } from '_utils';
import { uploadPhoto } from './studyPhotoProvider';

const ENTITY_CONFIG = {
  area: {
    endpoint: 'areas/add-area-app',
    updateTableName: tables.AREAS_TABLE,
    offlineTable: tables.CREATE_AREA_TABLE,
  },
  element: {
    endpoint: 'elements/add-element-app',
    updateTableName: tables.ELEMENTS_TABLE,
    offlineTable: tables.CREATE_ELEMENT_TABLE,
  },
  role: {
    endpoint: 'roles/add-role-app',
    updateTableName: tables.ROLES_TABLE,
    offlineTable: tables.CREATE_ROLE_TABLE,
  },
};

export const syncOfflineEntries = (dbDetail, areaSelectID) => async (dispatch) => {
  try {
    const [areas, elements, roles] = await Promise.all([
      getAllData(tables.CREATE_AREA_TABLE, dbDetail),
      getAllData(tables.CREATE_ELEMENT_TABLE, dbDetail),
      getAllData(tables.CREATE_ROLE_TABLE, dbDetail),
    ]);
    const processEntity = async (entityType, entityData) => {
      const { endpoint, updateTableName, offlineTable } = ENTITY_CONFIG[entityType];
      const entityUpdatePromises = entityData.map(async (entity) => {
        const request = await dispatch(
          createEntrySyncRequest(entity, endpoint, updateTableName, dbDetail)
        );
        if (!request[`${entityType}ID`]) {
          return; // Skip if ID is empty;
        }
        const data = {
          _id: request[`${entityType}ID`],
          name: entity.name,
          offline: entity._id,
          numericID: request.numericID,
        };
        if (!isEmpty(areaSelectID) && areaSelectID == data._id) {
          var areaSlct = {
            date: new Date(),
            name: request.area.name,
            popularity: 0,
            projectID: data.projectID,
            status: 'active',
            visible: true,
            _id: request.areaID,
          };
          dispatch({
            type: 'STORE_AREA',
            payload: areaSlct,
          });
        }
        var updatePromises = [
          updateTable(updateTableName, entityData, data, dbDetail),
          deleteById(offlineTable, entity._id, dbDetail),
        ];

        if (updateTableName === tables.ROLES_TABLE) {
          updatePromises.push(updateOfflineRoleInStudies(data.offline, data._id, dbDetail));
        } else {
          updatePromises.push(
            updateTable(tables.STUDY_DATA_TABLE, entityType, data, dbDetail),
            updateTable(tables.RECOVERY_DATA_TABLE, entityType, data, dbDetail)
          );
        }
        return Promise.all(updatePromises);
      });
      return Promise.all(entityUpdatePromises);
    };
    const [areaUpdates, elementUpdates, roleUpdates] = await Promise.all([
      processEntity('area', areas),
      processEntity('element', elements),
      processEntity('role', roles),
    ]);

    // Add your return statement here
    return { areaUpdates, elementUpdates, roleUpdates };
  } catch (error) {
    // Handle any errors here
  }
};

export const createEntrySyncRequest = (entry, endPoint, table, dbDetail) => async (dispatch) => {
  var data = initFormForOfflineData(entry);
  var res = await dispatch(createAction(endPoint, data, table, dbDetail, false));
  return res;
};

/* SETTING VALUES TO FORM FOR OFFLINE ENTRIES */
export const initFormForOfflineData = (data) => {
  /* FORM DATA FOR ROLES DOCUMENT */
  let dataForms;
  if (data.position) {
    dataForms = {
      name: data.name,
      position: data.position,
      addedBy: {
        _id: data.id_of_addedby,
        name: data.addedby,
        date: data.date,
      },
      status: data.status,
      projectID: data.projectID,
    };
  } else if (data._id.indexOf('element') > -1) {
    /* FORM DATA FOR ELEMENTS DOCUMENT */
    dataForms = {
      name: data.name,
      studyTypes: [2, 3],
      type: data.type,
      rating: data.rating,
      category: data.category,
      addedBy: {
        _id: data.userId,
        name: data.addedby,
        date: data.date,
      },
      count: data.count,
      projectID: data.projectID,
      status: data.status,
      userAdded: data.userAdded,
      taskID: data.taskID,
      addPosition: data.addPosition,
    };
  } else {
    /* FORM DATA FOR AREAS DOCUMENT */
    dataForms = {
      name: data.name,
      addedBy: {
        _id: data.id_of_addedby,
        name: data.addedby,
        date: data.date,
      },
      projectID: data.projectID,
      status: data.status,
    };
  }

  return dataForms;
};
export const syncOfflinePhotos = (dbDetail, netConnection) => async (dispatch) => {
  if (netConnection) {
    return new Promise(async (resolve, reject) => {
      try {
        const res = await getPhotos(dbDetail);
        if (!isEmpty(res)) {
          await Promise.all(
            res.map(async (elem, ind) => {
              let photo = elem.photo;
              let ImageName = elem.customerName + `_` + elem.name;
              let customerID = elem.customerID;
              let projectID = elem.projectID;
              let payload = {
                ImageName,
                customerID,
                projectID,
              };
              await dispatch(updateWithSyncedPhoto(photo, dbDetail, payload));
            })
          );
        }

        resolve(); // Resolve the promise when the syncOfflinePhotos logic is complete
      } catch (error) {
        reject(error); // Reject the promise if an error occurs
      }
    });
  } else {
    return;
  }
};
const updateWithSyncedPhoto = (photos, dbDetail, ImageName) => async (dispatch) => {
  const photoData = JSON.parse(JSON.stringify(photos));
  let photo = photos.split(',');
  let tempData = [];
  const promises = photo.map(async (elem, index) => {
    if (elem.startsWith('file')) {
      let type = elem.split('.');
      const image = {
        uri: elem,
        type: `image/${type[type.length - 1]}`,
        name: elem.substr(elem.lastIndexOf('/') + 1),
      };
      // return
      try {
        const img = await dispatch(uploadPhoto(image, true, ImageName));

        // photo.splice(index, 1, img);
        if (!isEmpty(img)) {
          tempData.push(img);
        }
      } catch (error) {
        // Handle the error appropriately
        console.error(error);
      }
    } else {
      tempData.push(elem);
    }
  });

  try {
    await Promise.all(promises);
    if (!isEmpty(tempData)) {
      await updateSyncedPhoto(photoData, tempData, dbDetail);
    }
    return;
  } catch (error) {
    return;

    // Handle the error appropriately
    console.error(error);
  }
};

const updateSyncedPhoto = async (oldPath, newPath, db) => {
  let newPaths = newPath.toString();
  const data = {
    path: newPaths,
    photo: oldPath,
  };
  await Promise.all([
    updateTable(tables.STUDY_DATA_TABLE, 'photo', data, db),
    updateTable(tables.RECOVERY_DATA_TABLE, 'photo', data, db),
  ]);
  return;
};

export const checkstudySync = (dbDetail) => async (dispatch) => {
  try {
    const result = await getStudiesBYSyncID(dbDetail);
    if (result && result.length > 0) {
      const arrayOfIds = result.map((item) => item._id);
      let payload = {
        syncedStudyIDs: arrayOfIds,
      };
      try {
        const syncResponse = await dispatch(syncingStatusCheck(payload, true));
        if (!isEmpty(syncResponse)) {
          await Promise.all(
            syncResponse.map(async (item) => {
              if (item.status === 'COMPLETED') {
                const idFound = result.find((n) => n._id === item._id);
                await markStudyAsSynced(item._id, dbDetail);
                await deleteRecord(tables.RECOVERY_DATA_TABLE, { studyID: idFound.id }, dbDetail);
              }
            })
          );
        }
      } catch (error) {
        console.error('Error checking sync status:', error);
      }
    }
  } catch (error) {
    dispatch({
      type: 'SYNCDATA_LOADING_FALSE',
    });
  }
};
