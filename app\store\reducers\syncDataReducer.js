const initialState = {
  syncLoading: false,
  studyID: '',
  unsyncObsrvationData: [],
  loadExclamation: false,
};

export default (state = initialState, action) => {
  switch (action.type) {
    case 'SYNCDATA_LOADING':
      return {
        ...state,
        syncLoading: true,
      };
    case 'SYNCDATA_LOADING_FALSE':
      return {
        ...state,
        syncLoading: false,
      };
    case 'SYNCDATA_OBSERVATION':
      return {
        ...state,
        syncLoading: false,
        unsyncObsrvationData: action.payload,
      };
    case 'SYNCDATA_CLEAR':
      return {
        ...state,
        syncLoading: false,
        unsyncObsrvationData: [],
      };
    case 'HIDE_EXCLAMATION':
      return {
        ...state,
        loadExclamation: false,
      };
    case 'SHOW_EXCLAMATION':
      return {
        ...state,
        loadExclamation: true,
      };
    case 'USER_LOGOUT':
      return { ...initialState };
    default: {
      return state;
    }
  }
};
