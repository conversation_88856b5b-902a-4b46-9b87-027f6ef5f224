import 'react-native-gesture-handler';
import React from 'react';
import { LogBox } from 'react-native';
import { Provider } from 'react-redux';
import store from './app/store';
import MasterScreen from './app/master-screen';
import { NavigationContainer } from '@react-navigation/native';
import { useKeepAwake } from '@sayem314/react-native-keep-awake';

// LogBox.ignoreLogs(['Each child in a list should have a unique "key" prop']);
// LogBox.ignoreLogs(['VirtualizedLists should never be nested inside plain ScrollViews......']);

const App = () => {
  global.HEADERSELECT = 'projects';
  global.STUDYPAGESELECT = 'area';
  useKeepAwake();
  const IGNORED_LOGS = [
    'Non-serializable values were found in the navigation state',
    // '[Reanimated] Reading from `value` during component render',
    'Warning: Each child in a list should have a unique',
    // '[Reanimated] Tried to modify key',
    'VirtualizedLists should never be nested',
    'Possible Unhandled Promise Rejection',
    'Warning: Encountered two children with the same key',
    'Error adding id column to',
    'Warning: Failed prop type',
    'Warning: Failed prop type: Textinputs',
    'Warning: Cannot update a component',
  ];

  LogBox.ignoreLogs(IGNORED_LOGS);

  if (__DEV__) {
    const withoutIgnored =
      (logger) =>
      (...args) => {
        const output = args.join(' ');

        if (!IGNORED_LOGS.some((log) => output.includes(log))) {
          logger(...args);
        }
      };

    console.log = withoutIgnored(console.log);
    console.info = withoutIgnored(console.info);
    console.warn = withoutIgnored(console.warn);
    console.error = withoutIgnored(console.error);
  }

  return (
    <Provider store={store}>
      <NavigationContainer>
        <MasterScreen />
      </NavigationContainer>
    </Provider>
  );
};
export default App;
