import { AText } from '_theme_components';
import { FontStyle } from '_utils';
import React, { useEffect, useRef, useState } from 'react';
import { View, StyleSheet, Animated, Easing } from 'react-native';

const LoadingProgressBar = ({ dontShowPercent }) => {
  const animatedValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.loop(
      Animated.timing(animatedValue, {
        toValue: 1,
        duration: 1000, // Adjust the duration for a smoother animation
        easing: Easing.linear,
        useNativeDriver: false,
      })
    ).start();
  }, [animatedValue]);

  const barWidth = animatedValue.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: ['20%', '50%', '20%'], // Adjust the range for a more fluid animation
  });
  const barMargin = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['0%', '90%'], // Adjust the range for a more fluid animation
  });

  return (
    <View style={styles.containerStyle}>
      <View style={styles.progressBarContainer}>
        <Animated.View style={[styles.loadingBar, { marginLeft: barMargin, width: barWidth }]} />
      </View>
      <AText styleText={styles.idText} fontWeight={FontStyle.fontBold} fontSize="small">
        {dontShowPercent ? '' : '0%'}
      </AText>
    </View>
  );
};
const styles = StyleSheet.create({
  progressBarContainer: {
    height: 5,
    width: '90%',
    backgroundColor: '#e0e0e0',
    borderRadius: 10,
    overflow: 'hidden',
    marginBottom: 10,
    marginTop: 10,
  },
  containerStyle: {
    flexDirection: 'row',
    width: '100%',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  loadingBar: {
    height: '100%',
    backgroundColor: '#4caf50', // Green color, change as needed
    borderRadius: 10,
  },
  idText: {
    textAlignVertical: 'center',
    marginStart: 5,
    width: '15%',
    // height: '95%',
  },
});

export default LoadingProgressBar;
