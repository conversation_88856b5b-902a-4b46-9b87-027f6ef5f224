import {
  <PERSON><PERSON>,
  <PERSON>H<PERSON><PERSON>,
  ImageBackground,
  Keyboard,
  Platform,
  StyleSheet,
  View,
} from 'react-native';
import React, { useState, useEffect } from 'react';
import Icon from 'react-native-vector-icons/Feather';
import { Switch, useTheme, Text } from 'react-native-paper';
import { CommonActions, useIsFocused } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import moment from 'moment';

import {
  AText,
  Textinputs,
  AuthLoading,
  CreateStudyLayout,
  IconButton,
  LinearGradientButton,
} from '_theme_components';
import { ALERT_TITLE, FontStyle, getFontSize, localimage, windowWidth } from '_utils';
import { CustomSwitch, getData, isEmpty, storeData } from '_helpers';
import { canStartStudy } from '_provider';

const CreateStudyAreaScreen = ({ navigation, route }) => {
  const isFocused = useIsFocused();
  const { colors, dark } = useTheme();
  const dispatch = useDispatch();

  const { dbDetail, userDetails } = useSelector((state) => state.user);
  const project = useSelector((state) => state.createStudy.projectSelect);
  const { locationSelect } = useSelector((state) => state.createStudy);
  const { areaList, hiddenArea } = useSelector((state) => state.serverReducer);
  const { netConnection } = useSelector((state) => state.netInfo);

  const [loader, setLoader] = useState(false);
  const [backButtonPressed, setBackButtonPressed] = useState(false);

  const [areaData, setAreaData] = useState([]);
  const [studyTitleErrorMessage, setStudyTitleErrorMessage] = useState('');
  const [studyTitle, setStudyTitle] = useState('');
  const [studyPress, setStudyPress] = useState(false);
  const [btndisable, setBtndisable] = useState(false);
  const [hiddenarealist, setHiddenarealist] = useState([]);
  useEffect(() => {
    if (isFocused) {
      ionViewWillEnter();
      canCreateEntry();
    } else {
      setStudyPress(false);
      setHiddenarealist([]);
    }
  }, [isFocused]);

  const backAction = () => {
    setBackButtonPressed(true);
    return true;
  };
  useEffect(() => {
    const backHandler = BackHandler.addEventListener('hardwareBackPress', backAction);
    return () => backHandler.remove();
  }, []);

  const ionViewWillEnter = () => {
    setLoader(true);
    setAreaData(areaList);
    if (project.autoGenerateStudyNames) {
      getNewStudyTitle(locationSelect);
    }
    storeData('showCustomerCount', 'false');
    storeData('showTracking', 'false');
    setLoader(false);

    // getDataByTableAndID(tables.CATEGORIES_TABLE, project._id, dbDetail)
    //   .then(categories => {
    //     categories.forEach(category => {
    //       if (category.name.toLowerCase() === "tracking") {
    //         storeData("showTracking", "true");
    //       }
    //       if (category.name.toLowerCase() === "customer count") {
    //         storeData("showCustomerCount", "true");
    //       }
    //     });
    //     setLoader(false);

    //   }).catch(() => {
    //     setLoader(false);
    //   })
    // // setFocus();
  };

  const canCreateEntry = () => {
    let createData = {
      roleCreate: project.canCreateRoles ? true : false,
      areaCreate: project.canCreateAreas ? true : false,
      elementCreate: project.canCreateElements ? true : false,
    };
    dispatch({
      type: 'CREATE_ENTRY_DATA',
      payload: createData,
    });
  };
  const validateTitle = () => {
    let regex = new RegExp('^[A-Za-z0-9_]*$');
    let isValidTitle = regex.test(studyTitle);
    if (!isValidTitle) {
      setStudyTitleErrorMessage('only letters, numbers and underscores are allowed');
    } else if (studyTitle.length > 30) {
      isValidTitle = false;
      setStudyTitleErrorMessage('Maximum 30 characters are allowed');
    } else {
      setStudyTitleErrorMessage('');
    }
  };
  useEffect(() => {
    if (studyTitle) {
      validateTitle();
    }
  }, [studyTitle]);

  const getNewStudyTitle = async () => {
    const user = userDetails;
    let studyName = locationSelect.locationname.substring(0, 2);
    studyName += user.name[0] + user.lastname[0];
    studyName += moment().format('DDMMmmss');
    let studyCounter = 1;

    let studyTitles = await getData('studyTitles');
    studyTitles = studyTitles || [];

    while (studyTitles.indexOf(studyName + studyCounter) != -1) {
      studyCounter++;
    }

    studyName += studyCounter;
    setStudyTitle(studyName.trim());
    return;
  };

  let hiddenAreasForStudy = hiddenarealist;
  const onToggleSwitch = async (area) => {
    let areaID = area._id;
    area.visible = !area.visible;

    hiddenAreasForStudy = area.visible
      ? hiddenAreasForStudy.filter((rID) => rID !== areaID)
      : [...hiddenAreasForStudy, areaID];

    if (hiddenAreasForStudy.length == areaData.length) {
      setHiddenarealist(hiddenAreasForStudy);
      setBtndisable(true);
    } else if (btndisable && hiddenAreasForStudy.length == areaData.length - 1) {
      setHiddenarealist(hiddenAreasForStudy);
      setBtndisable(false);
    }
  };
  const onStart = async () => {
    Keyboard.dismiss();
    dispatch({
      type: 'HIDDEN_AREA',
      payload: hiddenAreasForStudy,
    });
    if (studyPress) {
      return;
    }
    if (isEmpty(studyTitle)) {
      return;
    }
    setStudyPress(true);
    const result = await canStartStudy(dbDetail, netConnection, userDetails._id);
    if (!result.canStart) {
      Alert.alert(ALERT_TITLE, result.message || 'Can not Start study', [
        {
          text: 'Cancel',
          onPress: () => {},
          style: 'cancel',
        },
      ]);
      return;
    }
    navigation.dispatch(
      CommonActions.reset({
        index: 1,
        routes: [{ name: 'StartStudy', params: { studyTitle: studyTitle } }],
      })
    );
  };

  return (
    <>
      {loader ? <AuthLoading /> : null}
      <CreateStudyLayout
        backPressed={backButtonPressed}
        backPressCancel={() => setBackButtonPressed(false)}
        areaselectedfalse={hiddenarealist.length == areaData.length}
        navigation={navigation}
        title={'Study name and areas'}
      >
        <View style={styles.container}>
          <AText
            styleText={{ color: dark ? '#777778' : '#000', textAlign: 'left' }}
            fontWeight={FontStyle.fontMedium}
            fontSize={'medium'}
          >
            Study name
          </AText>

          <Textinputs
            // heading={'Study name'}
            placeholder="Enter name"
            onerror={false}
            textViewInputStyle={{ marginTop: 0 }}
            disabled={!project.allowStudyNameEdit && project.autoGenerateStudyNames}
            mode={'flat'}
            maxLength={30}
            value={studyTitle}
            stylesTextInput={styles.textInputStyle}
            onchange={(txt) => {
              setStudyTitle(txt);
            }}
            error={studyTitleErrorMessage ?? ''}
          />
          {project.allowStudyNameEdit || !project.autoGenerateStudyNames ? (
            <IconButton
              icon={<Icon name="edit-3" style={styles.iconStyle} size={18} />}
              btnStyle={styles.editbuttonStyle}
              bgColor={colors.secondary}
              onPress={() => {
                // handlemodal("Element");
              }}
            />
          ) : null}
          <View style={styles.contentContainer}>
            <AText
              fontWeight={FontStyle.fontMedium}
              styleText={{ color: '#777778', paddingBottom: 20 }}
              fontSize={'large'}
            >
              Areas visibility
            </AText>
            {!isEmpty(areaData) ? (
              areaData.map((item, index) => (
                <View
                  key={item._id}
                  style={{
                    marginTop: 7,
                    justifyContent: 'space-between',
                    flexDirection: 'row',
                    alignContent: 'center',
                  }}
                >
                  <AText
                    fontWeight={FontStyle.fontMedium}
                    // styleText={{ fontWeight: '700' }}
                    fontSize={'medium'}
                  >
                    {item.name}
                  </AText>
                  <CustomSwitch
                    selected={item.visible}
                    option1={'ON'}
                    option2={'OFF'}
                    onSelectSwitch={() => {
                      onToggleSwitch(item);
                    }}
                  />
                  {/* </View> */}
                </View>
              ))
            ) : isEmpty(areaData) && !loader ? (
              <AText
                fontSize={'large'}
                styleText={{ textAlign: 'center', color: '#878787', paddingTop: 70 }}
                fontWeight={FontStyle.fontBold}
              >
                No Data Found{' '}
              </AText>
            ) : null}
          </View>
        </View>
      </CreateStudyLayout>
      <ImageBackground
        resizeMode="stretch"
        source={dark ? localimage.blackbg : localimage.whitebg}
        style={[styles.studyButtonView, {}]}
      ></ImageBackground>
      {/* </View> */}
      <View style={[styles.studyStartViewButton, { width: windowWidth * 0.34 }]}>
        <LinearGradientButton
          disabled={
            !isEmpty(studyTitleErrorMessage) ||
            isEmpty(studyTitle) ||
            studyPress ||
            hiddenAreasForStudy.length == areaData.length
          }
          btnStyle={styles.studyButton}
          contentStyles={styles.btnContentStyle}
          title={'Start Study'}
          onPress={() => {
            onStart();
          }}
        />
      </View>
    </>
  );
};

export default CreateStudyAreaScreen;

const styles = StyleSheet.create({
  container: {
    width: '80%',
    marginTop: 15,
  },
  contentContainer: {
    marginTop: 25,
    width: '100%',
    alignSelf: 'center',
  },
  studyButtonView: {
    bottom: -2,
    position: 'absolute',
    width: '100%',
    zIndex: 0,
    alignSelf: 'center',
    height: 40,
  },
  studyButton: {
    bottom: 15,
    zIndex: 0,
    position: 'absolute',
  },
  btnContentStyle: {
    paddingVertical: 18,
  },
  editbuttonStyle: {
    position: 'absolute',
    right: 6,
    top: 40,
    borderRadius: 40,
    width: 40,
    height: 40,
  },
  textInputStyle: {
    backgroundColor: 'rgba(255, 255, 255, 0)',
    color: 'red',
    fontSize: getFontSize('large'),
    fontWeight: '500',
  },
  studyStartViewButton: {
    alignSelf: 'center',
  },
});
