import AsyncStorage from '@react-native-async-storage/async-storage';

export const storeData = async (key, value) => {
  try {
    await AsyncStorage.setItem(key, value);
  } catch (e) {
    // saving error
  }
};

export const removeStorage = async () => {
  try {
    await AsyncStorage.removeItem('token');
  } catch (e) {
    // removing error
  }

};


export const getToken = async () => {
  var token = '';
  try {
    const storageToken = await AsyncStorage.getItem('token');
    if (storageToken !== null) {
      token = storageToken;
    }
  } catch (e) {
    // Error
  }
  return token;
};

export const getData = async (key) => {
  var data = '';
  try {
    const storageToken = await AsyncStorage.getItem(key);
    if (storageToken !== null) {
      data = storageToken;
    }
  } catch (e) {
    // Error
  }
  return data;
};

export const clearStorage = async () => {
  try {
    await AsyncStorage.removeItem('userID');
    await AsyncStorage.removeItem('userName');
    await AsyncStorage.removeItem('currentUser');
    await AsyncStorage.removeItem('URL');
    await AsyncStorage.removeItem('token');

  } catch (e) {
    // saving error
  }
};

export const setURL = async (serverURL) => {
  try {
    await AsyncStorage.setItem("URL", serverURL);
  } catch (e) {
    // saving error
  }
}

export const getURL = async () => {
  let URL;
  try {
    const url = await AsyncStorage.getItem("URL");
    if (url !== null) {
      URL = url;
    }
  } catch (e) {
    // Error
  }
  return URL;
}