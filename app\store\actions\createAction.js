import { executeFetch, deleteRecord, updateCreationTable } from '_helpers';
import { ALERT_ERROR, ALERT_SUCCESS } from '../reducers/alert';
import { tables } from '../../utils/config';

export const createAction =
  (endPoint, payload, dataTable, dbDetail, showSuccessMsg = true) =>
  async (dispatch) => {
    dispatch({
      type: CREATE_LOADING,
    });
    return executeFetch(endPoint, 'POST', payload)
      .then(async (response) => {
        if (response.status === 200 && response.data.success) {
          if (showSuccessMsg) {
            await deleteEntries(dataTable, payload, dbDetail);
            if (dataTable == tables.ELEMENTS_TABLE) {
              dispatch({
                type: ALERT_SUCCESS,
                payload: response.data.data.message ?? `${dataTable} created successfully`,
              });
            } else {
              dispatch({
                type: ALERT_SUCCESS,
                payload: `${dataTable} created successfully`,
              });
            }
          }
          if (dataTable == tables.AREAS_TABLE) {
            console.log(payload, ' payloadidddn');
            updateCreationTable(
              tables.CREATE_AREA_TABLE,
              payload.name,
              payload.projectID,
              dbDetail
            );
          } else if (dataTable == tables.ELEMENTS_TABLE) {
            updateCreationTable(
              tables.CREATE_ELEMENT_TABLE,
              payload.name,
              payload.projectID,
              dbDetail
            );
          }
          return response.data;
        } else {
          if (showSuccessMsg) {
            // await deleteEntries(dataTable, payload, dbDetail);
            dispatch({
              type: ALERT_ERROR,
              payload: response.data.data.message || 'Something went wrong.Please try again later.',
            });
          }
          return [];
        }
      })
      .catch((error) => {
        dispatch({
          type: ALERT_ERROR,
          payload: 'Something went wrong. Please try again later.',
        });
        return;
      });
  };

export const deleteEntries = async (dataTable, payload, dbDetail) => {
  let queryData = null;
  queryData = { projectID: payload.projectID };
  await deleteRecord(dataTable, queryData, dbDetail)
    .then((result) => {
      return;
    })
    .catch((error) => {});
};
export const CREATE_LOADING = 'CREATE_LOADING';
