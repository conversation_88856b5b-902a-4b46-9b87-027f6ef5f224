import React from 'react';
import { StyleSheet, View } from 'react-native';
import { ActivityIndicator } from 'react-native-paper';

const AuthLoading = ({}) => {
  return (
    <View style={[styles.loadingMainWrapper]} justifyContent="center">
      <ActivityIndicator color={'#fff'} accessibilityLabel="Loading posts" size={'sm'} />
      {/* <Subheading color={'#fff'} fontSize="md" marginTop={5}>
                    Please wait...
                </Subheading> */}
    </View>
  );
};

export default AuthLoading;
const styles = StyleSheet.create({
  loadingMainWrapper: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.6)',
    zIndex: 9999,
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'space-between',
    paddingHorizontal: 10,
  },
  loadingSection: {
    alignItems: 'center',
    paddingTop: 100,
  },
});
