const initialState = {
  loading: false,
  studyStart: false,
  reminderList: [],
  timeofday_reminder: [],
  timeinstudy_reminder: [],
  showonelements_reminder: [],
  showondays_reminder: [],
  show_byTime: [],
};

export default (state = initialState, action) => {
  switch (action.type) {
    case 'REMINDER_LOADING':
      return {
        ...state,
        loading: true,
      };
    case 'REMINDER_LOADING_FALSE':
      return {
        ...state,
        loading: false,
      };
    case 'SAVE_REMINDER_LIST':
      return {
        ...state,
        loading: false,
        studyStart: true,
        showonelements_reminder: action.payload.show_on_task_elements,
        show_byTime: action.payload.show_byTime,
        reminderList: action.payload,
      };
    case 'SAVE_TIME_IN_STUDY_REMINDER':
      return {
        ...state,
        loading: false,
        timeinstudy_reminder: action.payload,
      };
    case 'SAVE_SHOW_ON_DAY_REMINDER':
      return {
        ...state,
        loading: false,
        showondays_reminder: action.payload,
      };
    case 'SAVE_TIME_Of_DAY_REMINDER':
      return {
        ...state,
        loading: false,
        timeofday_reminder: action.payload,
      };
    case 'STUDY_STOP':
      return {
        ...state,
        loading: false,
        studyStart: false,
        questionList: [],
      };
    case 'USER_LOGOUT':
      return { ...initialState };
    default: {
      return state;
    }
  }
};
