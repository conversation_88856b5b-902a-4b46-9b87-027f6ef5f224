import 'react-native-gesture-handler';
import { AppRegistry } from 'react-native';
import App from './App';
import { name as appName } from './app.json';
import FirebaseApp from '@react-native-firebase/app';
import crashlytics from '@react-native-firebase/crashlytics';
import { firebaseConfig } from '_utils';

const firebaseApps = FirebaseApp.apps.length;
if (firebaseApps.length) {
  FirebaseApp.initializeApp(firebaseConfig);
} else {
  FirebaseApp.app(); // if already initialized, use that one
}
// FirebaseApp.initializeApp(, 'bugreport');

// Enable Crashlytics
crashlytics().setCrashlyticsCollectionEnabled(true);

AppRegistry.registerComponent(appName, () => App);
