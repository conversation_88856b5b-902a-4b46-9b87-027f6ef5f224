{
  "root": true,
  "env": {
    "node": true,
    "react-native/react-native": true,
    "es2023": true
  },
  "plugins": ["import", "react", "react-native"],
  "extends": [
    "@react-native",
    "plugin:react/recommended",
    "airbnb",
    "airbnb/hooks",
    "plugin:prettier/react",
    "plugin:react-hooks/recommended",
    "prettier",
    "eslint:recommended",
    "plugin:react/recommended",
    "@react-native-community"
  ],
  "parser": "babel-eslint",
  "parserOptions": {
    "ecmaFeatures": {
      "jsx": true
    },
    "ecmaVersion": 12,
    "sourceType": "module"
  },
  "rules": {
    "prettier/prettier": ["error", { "singleQuote": true }],
    "react-native/no-unused-styles": 2,
    "react-native/split-platform-components": 2,
    "react-native/no-inline-styles": 2,
    "react-native/no-color-literals": 2,
    "react-native/no-raw-text": 2,
    "react-native/sort-styles": [
      "error",
      "asc",
      {
        "ignoreClassNames": false,
        "ignoreStyleProperties": false
      }
    ],
    // allow .js files to contain JSX code
    "react/jsx-filename-extension": [1, { "extensions": [".ts", ".tsx", ".js", ".jsx"] }],
    // prevent eslint to complain about the "styles" variable being used before it was defined
    "no-use-before-define": ["error", { "variables": false }],
    // ignore errors for the react-navigation package
    "react/prop-types": ["error", { "ignore": ["navigation", "navigation.navigate"] }],
    // ignore errors for import directives
    "import/extensions": [
      "error",
      "ignorePackages",
      {
        "js": "never",
        "jsx": "never",
        "ts": "never",
        "tsx": "never",
        "ignoreClassNames": false,
        "ignoreStyleProperties": false
      }
    ]
  },
  "settings": {
    "import/resolver": {
      "node": {
        "paths": ["app"],
        "alias": {
          "_helpers": "./app/helpers",
          "_hooks": "./app/hooks",
          "_navigations": "./app/navigations",
          "_provider": "./app/Provider",
          "_screen": "./app/screens",
          "_components": "./app/screens/Components",
          "_action": "./app/store/actions",
          "_reducer": "./app/store/reducer",
          "_theme_components": "./app/theme-components",
          "_utils": "./app/utils/config",
          "_validation": "./app/validation"
        }
      }
    }
  }
}
