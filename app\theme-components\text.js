import React from 'react';
import { Platform, StyleSheet } from 'react-native';
import { Text } from 'react-native-paper';
import { FontStyle, getFontSize } from '_utils';
import PropTypes from 'prop-types';

const TextStyle = ({ fontWeight, error, lightGray, fontSize, children, styleText }) => {
  TextStyle.propTypes = {
    error: PropTypes.bool,
    lightGray: PropTypes.bool,
    styleText: PropTypes.object,
    fontWeight: PropTypes.string,
    fontSize: PropTypes.string,
  };
  const fontType = () => {
    let type = 'normal';
    if (fontWeight === 'NeoSansStdBlack') type = '900';
    else if (fontWeight == 'NeoSansStdBold') type = '700';
    else if (fontWeight == 'NeoSansStdMedium') type = '400';
    else type = 'normal';
    return type;
  };
  const getTextStyle = () => {
    return StyleSheet.create({
      textStyleIOS: {
        fontWeight: fontWeight ? fontType() : 'normal',
        fontSize: fontSize ? getFontSize(fontSize) : 15,
      },
      textStyleAndroid: {
        fontFamily: fontWeight || FontStyle.fontRegular,
        fontSize: fontSize ? getFontSize(fontSize) : 15,
      },
    });
  };

  return (
    <>
      {error ? (
        <Text
          style={[
            Platform.OS == 'ios' ? getTextStyle().textStyleIOS : getTextStyle().textStyleAndroid,
            { color: 'red' },
            styleText,
          ]}
        >
          {children}
        </Text>
      ) : lightGray ? (
        <Text
          style={[
            Platform.OS == 'ios' ? getTextStyle().textStyleIOS : getTextStyle().textStyleAndroid,
            { color: '#878787' },
            styleText,
          ]}
        >
          {children}
        </Text>
      ) : (
        <Text
          style={[
            Platform.OS == 'ios' ? getTextStyle().textStyleIOS : getTextStyle().textStyleAndroid,
            styleText,
          ]}
        >
          {children}
        </Text>
      )}
    </>
  );
};

export default TextStyle;
