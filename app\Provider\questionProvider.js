import moment from 'moment';
import { dynamicSort, getDataByTableAndID, isEmpty } from '_helpers';
import { tables } from '_utils';

export const questioninitForProject = async (projectID, db) => {
  try {
    let questions = await getDataByTableAndID(tables.QUESTIONS_TABLE, projectID, db);
    if (isEmpty(questions)) {
      return;
    }
    let sortedQuestion = {
      end_of_study: [],
      show_on_task_elements: [],
      other: [],
      show_byTime: [],
    };

    // --divide questions by type
    questions.forEach((item) => {
      let { triggerType, triggerTime, showOnDays } = item;
      item.title = 'Question';
      item.answerRequired = item.answerRequired == 1 ? true : false;
      item.photo = item.photo == 1 ? true : false;
      let duration, asMinutes;
      item.repeat = false;
      item.numericPads = item.numericPads == 1 ? true : false;

      switch (triggerType) {
        case 'END_OF_STUDY':
          sortedQuestion.end_of_study.push(item);
          break;
        case 'SHOW_ON_DAYS':
          let triggeringTime;
          let show = showOnDays.some((key) => {
            const time = moment(key);
            // Replace hour and minute
            time.set({ hour: triggerTime.hours, minute: triggerTime.minutes });
            // let time = `${key} ${triggerTime.hours}:${triggerTime.minutes}:00`;
            let curentTime = moment().format(); //moment(new Date(), 'DD MM YYYY HH:mm:ss');
            triggeringTime = moment(time, 'DD/MM/YYYY HH:mm:ss');

            duration = moment.duration(triggeringTime.diff(curentTime));
            hours = duration.asHours().toFixed(2);
            asMinutes = duration.asMinutes();
            item.time = 60000 * asMinutes;
            // console.log(duration);
            return 0 < hours && hours <= 10;
          });
          if (show) {
            sortedQuestion.show_byTime.push(item);
          }
          break;
        case 'SHOW_ON_TASK_ELEMENTS':
          sortedQuestion.show_on_task_elements.push(item);
          break;
        case 'TIME_IN_STUDY':
          item.time = 60000 * (triggerTime.hours * 60 + triggerTime.minutes);
          sortedQuestion.show_byTime.push(item);
          break;
        default:
          sortedQuestion.other.push(item);
      }
    });
    return sortedQuestion;
  } catch (error) {
    console.error('Error fetching reminders: ', error);
    // Optionally, return some error response
    return { error: 'Failed to initialize reminders' };
  }
};

export const checkTaskQuestionTrigger = (questions, elementID, taskID) => {
  let questionToAsked = [];
  if (!isEmpty(questions)) {
    questions.map((item) => {
      if (item.taskID == taskID) {
        if (item?.elementID && item.elementID.findIndex((n) => n === elementID) !== -1) {
          questionToAsked.push(item);
        }
      }
    });
  }
  return questionToAsked;
};
