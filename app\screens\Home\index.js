import { BackHandler, Dimensions, StyleSheet, Pressable, View } from 'react-native';
import React, { useContext, useEffect, useState } from 'react';
import { useIsFocused } from '@react-navigation/native';
import { useTheme } from 'react-native-paper';
import { useDispatch, useSelector } from 'react-redux';

import { AText, AuthLoading, FabButton, MainLayout } from '_theme_components';
import { FontStyle, INTERNET_ERROR, changeWindowHeightWidth, tables, windowHeight } from '_utils';
import { BiometricModal, NetinfoAlert, isEmpty, useDebounce } from '_helpers';

import ProjectScreen from '../Project/projectScreen';
import SyncScreen from '../SyncStudies';
import ProfileScreen from '../Profile';
import { fetchDataAction } from '_action';
import {
  checkstudySync,
  creatingData,
  getProjects,
  getSyncExclamation,
  mergeOfflineData,
  syncOfflinePhotos,
} from '_provider';
import { ALERT_ERROR } from '../../store/reducers/alert';
import { SyncingModal } from '../Components';
import { AppThemeContext } from '../../hooks/themeContext';
import MarqueeText from '../Components/marqueeText';
import { getStudyTable } from '_helpers';

const HomeScreen = ({ navigation }) => {
  const { dark } = useTheme();
  const dispatch = useDispatch();
  const isFocused = useIsFocused();
  const { netConnection } = useSelector((state) => state.netInfo);
  const { debounce } = useDebounce();
  const { themeLoader } = useContext(AppThemeContext);

  const { loading } = useSelector((state) => state.projectReducer);
  const { syncLoading, loadExclamation } = useSelector((state) => state.syncData);
  const { dbDetail, userDetails, reload } = useSelector((state) => state.user);
  const { projectByType, projectList, serverFailed } = useSelector((state) => state.projectReducer);

  const [headerValue, setHeaderValue] = useState(global.HEADERSELECT);
  const [refreshData, setRefreshData] = useState(false);
  const [size, setSize] = useState(0);
  const [disableRefresh, setDisableRefresh] = useState(true);
  const { roleList, elementList, areaList } = useSelector((state) => state.serverReducer);

  const headerData = [
    { id: 1, label: 'Projects', value: 'projects' },
    { id: 2, label: 'Sync', value: 'sync' },
    { id: 3, label: 'Profile', value: 'profile' },
  ];

  const syncAreaElementRole = async () => {
    try {
      console.log('hyyyyggg', loading);
      let elements = await getStudyTable(tables.CREATE_ELEMENT_TABLE, dbDetail);
      let area = await getStudyTable(tables.CREATE_AREA_TABLE, dbDetail);

      console.log(elements, area, Array.isArray(area), Array.isArray(elements), ' aarea');
      if (netConnection) {
        Array.isArray(elements) &&
          elements.map(async (ele) => {
            try {
              const {
                name,
                category,
                // orderAfter, //notofund
                rating,
                count,
                relaxationAllowance,
                contingencyAllowance,
                projectID,
                taskID,
                addPosition,
                addedby,
              } = ele;
              var payload = {
                name: name.trim(),
                projectID: projectID,
                // customerID: project.customerID,
                categoryID: category,
                relaxationAllowance: relaxationAllowance ? relaxationAllowance : 0,
                contingencyAllowance: contingencyAllowance ? contingencyAllowance : 0,
                studyTypes: [2, 3],
                addPosition: addPosition,
                taskID: taskID,
                type: 2,
                count: count === 1 ? true : false,
                userAdded: true,
                rating: rating,
                status: 'active',
                addedBy: addedby,
              };
              // let elementFound = await searchElementByName(name, projectID, dbDetail);
              // if (!isEmpty(elementFound)) {
              //   let elemPick = elementFound[0];
              //   payload = {
              //     ...elemPick,
              //     userAdded: true,
              //     status: 'active',
              //     addPosition: addPosition,
              //     projectID: payload.projectID,
              //     categoryID: category,
              //     taskID: payload.taskID,
              //     addedBy: addedby,
              //   };
              // }
              await dispatch(
                creatingData(elementList, payload, netConnection, dbDetail, 'element', false)
              );
            } catch (e) {
              console.log(e, ' elemness');
            }
          });

        Array.isArray(area) &&
          area.map(async (are) => {
            const payload = {
              name: are.name.trim(),
              projectID: are.projectID,
              customerID: are.customerID,
            };
            await dispatch(creatingData(areaList, payload, netConnection, dbDetail, 'area', false));
          });
      }
    } catch (e) {
      console.log(e, 'lololo');
    }
  };
  useEffect(() => {
    const timerId = setTimeout(() => {
      syncAreaElementRole();
    }, 3000);
    return () => clearTimeout(timerId);
  }, [loading]);

  useEffect(() => {
    if (reload) {
      global.HEADERSELECT = 'sync';
      setHeaderValue('sync');
      dispatch({ type: 'RELOAD_FALSE' });
    }
  }, [reload]);

  const loadData = async () => {
    await dispatch(syncOfflinePhotos(dbDetail, netConnection));
  };
  const saveUnSyncData = async () => {
    await dispatch(mergeOfflineData(dbDetail, projectList));
  };

  useEffect(() => {
    if (!isEmpty(projectList) && headerValue === 'projects') {
      saveUnSyncData();
    }
  }, [projectList]);

  const checkexclamation = async () => {
    await dispatch(checkstudySync(dbDetail, userDetails._id));
    await dispatch(getSyncExclamation(dbDetail, userDetails._id));
  };
  useEffect(() => {
    if (isFocused && !isEmpty(dbDetail) && !loading && headerValue === 'projects') {
      checkexclamation();
      dispatch({
        type: 'CANCELING_STUDY',
      });
      if (netConnection && !themeLoader) {
        loadData();
        setRefreshData(true);
        dispatch(fetchDataAction(dbDetail));
        setRefreshData(false);
      } else if (!netConnection) {
        console.log(netConnection, 'netConnection');
        // dispatch(getProjects(dbDetail));
      }
    }
  }, [isFocused]);

  useEffect(() => {
    if (
      (!isEmpty(dbDetail) && !isEmpty(projectByType) && !themeLoader) ||
      serverFailed ||
      !netConnection
    ) {
      dispatch(getProjects(dbDetail));
    }
  }, [projectByType, serverFailed]);

  useEffect(() => {
    setTimeout(() => {
      dispatch({
        type: 'PROJECT_LOADING_FALSE',
      });
      setDisableRefresh(false);
    }, 8000);
  }, []);

  const headerChange = (item) => {
    (global.HEADERSELECT = item.value), setHeaderValue(item.value);
  };

  const onRefresh = async () => {
    if (refreshData || disableRefresh) {
      if (disableRefresh) {
        dispatch({
          type: 'PROJECT_LOADING',
        });
      }
      return;
    }
    if (!netConnection) {
      dispatch({
        type: ALERT_ERROR,
        payload: INTERNET_ERROR,
      });
      return;
    } else {
      setRefreshData(true);
      dispatch(fetchDataAction(dbDetail));
      setRefreshData(false);
    }
  };

  useEffect(() => {
    const backHandler = BackHandler.addEventListener('hardwareBackPress', backAction);
    return () => backHandler.remove();
  }, []);

  var count = 0;
  const backAction = () => {
    if (count <= 1) {
      count += 1;
      dispatch({
        type: 'ALERT_CLOSEAPP',
        payload: 'Press back  again to exit the app',
      });
      setTimeout(() => {
        count = 0;
      }, 2000);
    } else {
      dispatch({
        type: 'ALERT_HIDE',
      });
      count = 0;
      BackHandler.exitApp();
    }

    return true;
  };

  useEffect(() => {
    const updateWindowHeight = () => {
      const newWindowHeight = Dimensions.get('window').height;
      const newWindowWidth = Dimensions.get('window').width;
      changeWindowHeightWidth(newWindowHeight, newWindowWidth);
      setSize(newWindowWidth);
    };

    var dim = Dimensions.addEventListener('change', updateWindowHeight);

    return () => {
      dim.remove('change', updateWindowHeight);
    };
  }, []);

  const renderHeaderButton = ({ id, label, value }) => (
    <Pressable key={id} onPress={() => headerChange({ value })} style={styles.headerButtonStyle}>
      <AText
        styleText={{
          color:
            headerValue === value && !dark
              ? 'black'
              : headerValue === value && dark
                ? '#fff'
                : '#757575',
        }}
        fontWeight={FontStyle.fontBold}
        fontSize={'homeTitle'}
      >
        {label}
      </AText>
      {value === 'sync' && loadExclamation && (
        <AText error styleText={styles.exclamationStyle} fontSize={'homeTitle'}>
          !
        </AText>
      )}
    </Pressable>
  );

  const renderContent = () => {
    switch (headerValue) {
      case 'projects':
        return !loading && <ProjectScreen navigation={navigation} />;
      case 'sync':
        return (
          <SyncScreen
            loadExclamtion={() => dispatch(getSyncExclamation(dbDetail, userDetails._id))}
            navigation={navigation}
          />
        );
      case 'profile':
        return <ProfileScreen navigation={navigation} />;
      default:
        return null;
    }
  };

  if (loading && disableRefresh) {
    return <AuthLoading />;
  }

  return (
    <>
      {loading || syncLoading ? <AuthLoading /> : null}
      <MainLayout hideScroll ShowDarkMode headerShow drawerOption navigation={navigation}>
        <View style={styles.HeaderContainer}>
          {!isEmpty(headerData) && headerData.map(renderHeaderButton)}
        </View>
        <View style={styles.contentContainer}>{renderContent()}</View>
        {headerValue == 'projects' && (
          <FabButton
            icons={'refresh'}
            style={styles.FabRefresh}
            onClick={() => {
              debounce(() => onRefresh());
            }}
          />
        )}
      </MainLayout>
      <BiometricModal />
      {headerValue == 'sync' && (
        <SyncingModal
          loadExclamtion={() => dispatch(getSyncExclamation(dbDetail, userDetails._id))}
          backToProjectPage={() => headerChange(headerData[0])}
        />
      )}
      <MarqueeText
        text="Unsynced Studies Found! We recommend you to sync your studies immediately."
        style={{
          backgroundColor: '#ff6347',
          paddingVertical: 5,
          flexDirection: 'row',
          justifyContent: 'center',
        }}
        show={loadExclamation}
      />
      <NetinfoAlert />
    </>
  );
};

export default HomeScreen;

const styles = StyleSheet.create({
  HeaderContainer: {
    flexDirection: 'row',
    width: '70%',
    justifyContent: 'space-evenly',
    alignSelf: 'flex-start',
    marginStart: 20,
  },
  contentContainer: {
    marginTop: 5,
    flex: 1,
  },
  headerButtonStyle: {
    paddingLeft: 10,
    flexDirection: 'row',
  },
  FabRefresh: {
    position: 'absolute',
    right: 0,
    bottom: 90,
    padding: 5,
    borderRadius: windowHeight * 0.05,
  },
  exclamationStyle: { lineHeight: 32 },
});
