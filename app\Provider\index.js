import {
  canStartStudy,
  saveStudyTitle,
  studyStarted,
  markStudyAsCancelledIfEmpty,
  saveStudyData,
  getStudyData,
  removeStudyTitle,
  clearOldStudyTitles,
  getSyncExclamation,
  fetchRecoverData,
} from './studyProvider';
import { questioninitForProject, checkTaskQuestionTrigger } from './questionProvider';
import { reminderinitForProject, checkTaskReminderTrigger } from './reminderProvider';
import {
  canContinueElement,
  ContinueSelectedElement,
  isYesOrNo,
  checkNotesEC,
  checkECNotesINObservation,
  findLastEC,
  loadElementDataFetch,
} from './observationProvider';
import {
  checkIfNameExists,
  createOfflineTable,
  createOfflineEntry,
  createOfflineStructure,
  addOfflineEntry,
  reorderTask,
  getUniqueName,
  creatingData,
} from './creationProvider';
import {
  syncOfflineEntries,
  createEntrySyncRequest,
  initFormForOfflineData,
  syncOfflinePhotos,
  checkstudySync,
} from './syncProvider';

import { uploadPhoto, Cameraopen, updateLastObsImage } from './studyPhotoProvider';
import {
  createDataCase,
  dropTables,
  createTables,
  populateTables,
  populateProject,
  getProjects,
} from './projectProvider';
import {
  setNextElementSet,
  getAreaDataFetch,
  getTaskDataFetch,
  getElementDataFetch,
  getElementCategoryDataFetch,
  getLocationData,
  retrieveStudyItemsData
} from './elementProvider';
import { mergeOfflineData } from './duplicacyProvider';
import { biometricDetail, biometricAuthentication } from './biometricProvider';
export {
  canStartStudy,
  saveStudyTitle,
  studyStarted,
  questioninitForProject,
  markStudyAsCancelledIfEmpty,
  reminderinitForProject,
  saveStudyData,
  getStudyData,
  canContinueElement,
  checkIfNameExists,
  createOfflineTable,
  createOfflineEntry,
  createOfflineStructure,
  addOfflineEntry,
  syncOfflineEntries,
  uploadPhoto,
  createDataCase,
  dropTables,
  createTables,
  populateTables,
  populateProject,
  getProjects,
  reorderTask,
  createEntrySyncRequest,
  initFormForOfflineData,
  removeStudyTitle,
  clearOldStudyTitles,
  ContinueSelectedElement,
  checkTaskQuestionTrigger,
  checkTaskReminderTrigger,
  Cameraopen,
  isYesOrNo,
  checkNotesEC,
  checkECNotesINObservation,
  syncOfflinePhotos,
  findLastEC,
  setNextElementSet,
  getAreaDataFetch,
  getTaskDataFetch,
  getElementDataFetch,
  getSyncExclamation,
  fetchRecoverData,
  getUniqueName,
  mergeOfflineData,
  getElementCategoryDataFetch,
  updateLastObsImage,
  checkstudySync,
  biometricDetail,
  biometricAuthentication,
  loadElementDataFetch,
  creatingData,
  getLocationData,
  retrieveStudyItemsData
};
