import { StyleSheet, Image, View, Platform } from 'react-native';
import React from 'react';
import { Appbar, useTheme } from 'react-native-paper';
import { AButton, AText, Textinputs, IconButton } from '_theme_components';
import { FontStyle, getFontSize, localimage, windowHeight, windowWidth } from '_utils';

const StudyHeader = ({
  navigation,
  Title,
  SubTitle,
  showBack,
  searchPress,
  ShowIcon,
  icon,
  showSearch,
  OnPressIcon,
  ShowMagnify,
  OnChangeSearch,
  modalView,
  FrequencyOUM,
  showSorting,
  bachkHandler,
  rate,
}) => {
  const { dark } = useTheme();

  return (
    <View
      style={[styles.containerStyle, { justifyContent: 'center', marginTop: windowWidth * 0.037 }]}
    >
      {showBack && !modalView ? (
        <Appbar.Action
          animated={false}
          icon={'arrow-left'}
          color={dark ? '#fff' : '#000'}
          size={32}
          style={styles.backButonStyle}
          onPress={() => bachkHandler()}
        />
      ) : null}
      <View
        style={{
          flexDirection: 'row',
          alignSelf: 'center',
          alignItems: 'center',
          width:
            modalView && showSearch && windowWidth > windowHeight
              ? windowWidth * 0.3
              : modalView && showSearch && windowWidth < windowHeight
                ? windowWidth * 0.6
                : modalView && !showSearch && windowWidth > windowHeight
                  ? windowWidth * 0.3
                  : modalView && !showSearch && windowWidth < windowHeight
                    ? windowWidth * 0.5
                    : windowWidth * 0.72,
        }}
      >
        {showSearch ? (
          <Textinputs
            textViewInputStyle={styles.textViewInputStyle}
            stylesTextInput={{
              ...styles.serachTextinputStyle,
              width:
                modalView && windowWidth > windowHeight
                  ? windowWidth * 0.3
                  : modalView && windowWidth < windowHeight
                    ? windowWidth * 0.6
                    : windowWidth * 0.72,
            }}
            placeholder={`Search ${Title}`}
            maxLength={30}
            onerror={false}
            mode={'Flat'}
            onchange={(txt) => OnChangeSearch(txt)}
          />
        ) : (
          <View
            style={{
              flexDirection: 'row',
              alignSelf: 'center',
              alignItems: 'center',
              width:
                modalView && windowWidth > windowHeight
                  ? windowWidth * 0.3
                  : modalView && windowWidth < windowHeight
                    ? windowWidth * 0.5
                    : windowWidth * 0.72,
              marginLeft:
                rate && windowWidth > 900 && Platform.OS == 'ios'
                  ? 95
                  : rate && windowWidth > 900 && Platform.OS == 'android'
                    ? 30
                    : rate && windowWidth < 900 && Platform.OS == 'ios'
                      ? 20
                      : 0,
            }}
          >
            <View>
              <AText
                fontWeight={FontStyle.fontBold}
                styleText={{ textTransform: 'capitalize', color: dark ? '#fff' : '#3C4555' }}
                fontSize={'homeTitle'}
              >
                {Title}
              </AText>
              <AText
                fontWeight={FontStyle.fontBold}
                lightGray
                styleText={{ paddingTop: 12 }}
                fontSize={'small'}
              >
                {SubTitle}{' '}
                <AText
                  fontWeight={FontStyle.fontBold}
                  error
                  styleText={{ textTransform: 'capitalize' }}
                >
                  {FrequencyOUM}
                </AText>
              </AText>
            </View>
          </View>
        )}
        <View style={{ flexDirection: 'row' }}>
          {ShowIcon ? (
            <Appbar.Action
              animated={false}
              icon={icon}
              color={dark ? '#fff' : '#000'}
              style={styles.eyeShow}
              onPress={() => {
                OnPressIcon();
              }}
            />
          ) : showSorting ? (
            <IconButton
              icon={
                <Image
                  source={localimage.sort}
                  style={[styles.imageStyle, { tintColor: dark ? '#fff' : '#000' }]}
                />
              }
              btnStyle={{ ...styles.eyeShow, marginRight: 15 }}
              onPress={() => {
                OnPressIcon();
              }}
            />
          ) : null}
          {ShowMagnify ? (
            <Appbar.Action
              animated={false}
              icon="magnify"
              style={styles.searchMagnify}
              color={dark ? '#fff' : '#000'}
              onPress={() => {
                searchPress();
              }}
            />
          ) : null}
        </View>
      </View>
    </View>
  );
};

export default StudyHeader;

const styles = StyleSheet.create({
  containerStyle: {
    width: '100%',
  },
  imageStyle: {
    resizeMode: 'contain',
    height: 45,
    width: 25,
  },
  searchMagnify: {
    position: 'absolute',
    alignSelf: 'center',
    right: 5,
  },
  eyeShow: {
    position: 'absolute',
    alignSelf: 'center',
    right: 50,
  },
  magnify: {
    alignSelf: 'center',
  },
  textViewInputStyle: {
    marginTop: 0,
    marginBottom: 0,
  },
  serachTextinputStyle: {
    fontSize: getFontSize('large'),
    alignself: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0)',
  },
  backButonStyle: {
    marginEnd: 15,
    borderRadius: 0,
    position: 'absolute',
    left: 12,
  },
});
