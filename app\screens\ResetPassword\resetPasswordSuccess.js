import { Image, StyleSheet, View, Linking } from 'react-native';
import React from 'react';
import { useTheme } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';

import { AButton, AText, LinearGradientButton } from '_theme_components';
import { FontStyle, localimage } from '_utils';

const ResetPasswordSuccessScreen = ({ navigation, route }) => {
  const { email } = route.params;
  const { colors } = useTheme();

  return (
    <View style={[styles.modalCointainer, { backgroundColor: colors.onSurface }]}>
      <Image source={localimage.keys} style={[styles.keyStyle, { tintColor: colors.primary }]} />
      <View style={styles.LinkSentView}>
        <View style={styles.linkSentTextStyle}>
          <AText fontSize={'title'} fontWeight={FontStyle.fontBold}>
            Link Sent.
          </AText>
          <View style={{ paddingVertical: 15 }}>
            <AText fontSize={'medium'} fontWeight={FontStyle.fontRegular}>
              If the email{' '}
              <AText fontSize={'medium'} fontWeight={FontStyle.fontBold}>
                {email}
              </AText>{' '}
              is registered.You will receive a link to reset your password
            </AText>
          </View>
        </View>
        <View style={styles.btnStyleView}>
          <LinearGradientButton
            onPress={() => {
              Linking.openURL('mailto:' + email);
            }}
            btnStyle={styles.btnStyle}
            title="OPEN EMAIL APP"
          />
        </View>

        <View style={styles.loginTextStyle}>
          <AButton
            onPress={() => navigation.navigate('Login')}
            mode="text"
            icon={
              <Icon name="arrow-back" style={styles.iconStyle} color={colors.primary} size={25} />
            }
            btnStyle={styles.btnStyle}
            title={'RETURN TO LOGIN'}
            fontSize={'small'}
            styleText={{ color: colors.primary }}
          />
        </View>
      </View>
    </View>
  );
};

export default ResetPasswordSuccessScreen;

const styles = StyleSheet.create({
  containerStyle: {
    margin: 20,
    width: '90%',
    justifyContent: 'center',
    alignSelf: 'center',
  },
  modalCointainer: {
    flex: 1,
    justifyContent: 'center',
  },
  keyStyle: {
    resizeMode: 'contain',
    height: '30%',
    width: '50%',
    alignSelf: 'center',
  },
  LinkSentView: {
    marginVertical: 20,
    borderRadius: 7,
    paddingTop: 12,
  },
  linkSentTextStyle: {
    marginVertical: 20,
    alignSelf: 'center',
    width: '75%',
    paddingHorizontal: 12,
  },
  loginTextStyle: {
    flexDirection: 'row',
    alignSelf: 'center',
    paddingHorizontal: 10,
    marginTop: 35,
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconStyle: {
    alignSelf: 'center',
  },
  btnStyle: {
    borderRadius: 70,
    padding: 10,
    alignItems: 'center',
  },
  btnStyleView: {
    alignSelf: 'center',
    marginTop: 30,
    width: '40%',
  },
});
