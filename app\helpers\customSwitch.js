import { useIsFocused } from '@react-navigation/native';
import React, { useEffect, useRef, useState } from 'react';
import { StyleSheet, Text, View, Pressable, Animated, Easing, Platform } from 'react-native';
import { getFontSize } from '_utils';

const CustomSwitch = ({
    navigation,
    selected,
    option1,
    option2,
    onSelectSwitch,
}) => {
    const positionButton = useRef(new Animated.Value(0)).current;
    const [getSelectionMode, setSelectionMode] = useState(selected);
    const isOnRef = useRef(false);
    const isFocused = useIsFocused()

    useEffect(() => {
        if (selected) {
            isOnRef.current = false

            startAnimToOff()
        } else {
            startAnimToOn()
            isOnRef.current = true

        }
    }, [isFocused])

    const startAnimToOff = () => {
        Animated.timing(positionButton, {
            toValue: 0,
            duration: 200,
            delay: 0,
            easing: Easing.ease,
            useNativeDriver: false
        }).start()
    };

    const startAnimToOn = () => {
        Animated.timing(positionButton, {
            toValue: 1,
            duration: 200,
            delay: 0,
            easing: Easing.ease,
            useNativeDriver: false
        }).start()

    };

    const positionInterPol = positionButton.interpolate({ inputRange: [0, 1], outputRange: [Platform.OS == 'ios' ? 45 : 48, Platform.OS == 'ios' ? 5 : 0] })


    const initialOpacityOff = positionButton.interpolate({ inputRange: [0, 1], outputRange: [0, 1] })

    const initialOpacityOn = positionButton.interpolate({ inputRange: [0, 1], outputRange: [1, 0] })

    const backgroundColorAnim = positionButton.interpolate({ inputRange: [0, 1], outputRange: ["#98CB4F", "#D0D0D0"] })

    const onPress = () => {
        setSelectionMode(!getSelectionMode);
        onSelectSwitch();
        if (isOnRef.current) {
            startAnimToOff();
            isOnRef.current = false
            // setIsOn(false);
        } else {
            startAnimToOn();
            isOnRef.current = true
            // setIsOn(true);
        }
    };
    return (
        <Pressable activeOpacity={0.9} onPress={() => onPress()} style={styles.containerStyle} >
            <Animated.View style={[styles.mainStyes, {
                backgroundColor: backgroundColorAnim
            }]} >
                <Animated.Text
                    style={[
                        getSelectionMode ? styles.eahcStylesOf : styles.eahcStyles,

                        {
                            opacity: getSelectionMode ? initialOpacityOn : initialOpacityOff,
                            color: getSelectionMode ? '#fff' : '#000'
                        },
                    ]}>
                    {getSelectionMode ? option1 : option2}
                </Animated.Text>
                <Animated.View style={[styles.basicStyle, {
                    transform: [{
                        translateX: positionInterPol
                    }]
                }]} />
            </Animated.View>
        </Pressable>
    );
};
export default CustomSwitch;

const styles = StyleSheet.create({
    containerStyle: {
        height: Platform.OS == 'ios' ? 40 : 50,
        width: Platform.OS == 'ios' ? 87 : 100,
        borderRadius: 5,
        flexDirection: 'row',
        justifyContent: 'space-evenly',
        alignItems: 'center',
        alignContent: 'center',
        padding: Platform.OS == 'ios' ? 0 : 5,
        paddingHorizontal: Platform.OS == 'ios' ? 0 : 15,

    },
    mainStyes: {
        backgroundColor: '#81b0ff',
        height: Platform.OS == 'ios' ? 40 : 50,
        width: Platform.OS == 'ios' ? 87 : 100,
        borderRadius: 25,
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: Platform.OS == 'ios' ? 0 : 5,
        paddingHorizontal: Platform.OS == 'ios' ? 0 : 5,
        zIndex: 1,
        borderWidth: 0.9,
        borderColor: "#c2c2c2"

    },
    basicStyle: {
        backgroundColor: '#FFF',
        borderRadius: 60,
        height: Platform.OS == 'ios' ? 34 : 38,
        width: Platform.OS == 'ios' ? 35 : 39,
        alignSelf: 'center',
        shadowColor: "#000",
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 1.84,

        elevation: Platform.OS == 'ios' ? 2 : 5,
    },
    eahcStylesOf: {
        fontSize: getFontSize('medium'),
        position: 'absolute',
        fontWeight: "800",
        top: Platform.OS == 'ios' ? 7 : 8,
        left: 10,
        zIndex: 1,
        alignSelf: 'center',
        justifyContent: 'center',
    },
    eahcStyles: {
        fontSize: getFontSize('medium'),
        position: 'absolute',
        fontWeight: "800",
        top: Platform.OS == 'ios' ? 7 : 8,
        right: 10,
        zIndex: 1,
        alignSelf: 'center',
        justifyContent: 'center',
    },
})