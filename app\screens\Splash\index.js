import { View, StatusBar, StyleSheet, Image, ImageBackground, Dimensions } from 'react-native';
import React, { useEffect, useState } from 'react';
import { changeWindowHeightWidth, localimage, windowHeight, windowWidth } from '_utils';
import LinearGradient from 'react-native-linear-gradient';

const SplashScreen = ({ navigation }) => {
  const [size, setSize] = useState(0);

  useEffect(() => {
    const updateWindowHeight = () => {
      // const newWindowHeight = Dimensions.get('window').height;
      const newWindowWidth = Dimensions.get('window').width;
      changeWindowHeightWidth();
      setSize(newWindowWidth);
    };
    var dim = Dimensions.addEventListener('change', updateWindowHeight);
    return () => {};
  }, []);
  return (
    <>
      <StatusBar animated hidden={true} />
      <LinearGradient
        colors={['#0C9FEF', '#0A64F0']}
        start={{ x: 0.7, y: 0.3 }}
        end={{ x: 0.3, y: 0.65 }}
        // start={{ x: 0.4, y: 0.5 }}
        // end={{ x: 0.45, y: 0.46 }}
        locations={[0.5, 0.6]}
        style={[styles.flexContainer, {}]}
      >
        <View style={styles.container}>
          <Image
            source={windowWidth > windowHeight ? localimage.splashLandscape : localimage.splash}
            style={styles.imageStyle}
          />
        </View>
      </LinearGradient>
    </>
  );
};

export default SplashScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  imageStyle: {
    alignSelf: 'center',
    height: '100%',
    width: '100%',
    resizeMode: 'stretch',
  },
  flexContainer: {
    width: '100%',
    height: '100%',
    flex: 1,
    alignSelf: 'center',
  },
});
