import {
  FlatList,
  Image,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  View,
} from 'react-native';
import React, { useState } from 'react';
import { AButton, AText, Textinputs } from '_theme_components';
import { useTheme, Modal, Portal } from 'react-native-paper';
import { FontStyle, getFontSize, localimage, windowHeight, windowWidth } from '_utils';
import LinearGradient from 'react-native-linear-gradient';
import { OpenCameraView, isEmpty, launchCamera } from '_helpers';
import Icon from 'react-native-vector-icons/Ionicons';
import { useSelector } from 'react-redux';

const StudyPoPUP = ({
  modalShow,
  onDismiss,
  onConfirm,
  onPhotoSubmit,
  question,
  freetext,
  setFreetext,
  selectedVal,
  setSelectedVal,
  selectedCheckboxVal,
  setselectedCheckboxVal,
}) => {
  const {
    title,
    numericPads,
    freeText,
    questionType,
    answerRequired,
    questionOptions,
    text,
    photo,
  } = question;
  const showAnwerInput = title == 'Question' ?? false;
  const clickPhoto = photo == 1 ?? false;

  const { colors, dark } = useTheme();
  const { projectSelect, studyData } = useSelector((state) => state.createStudy);
  // const [selectedVal, setSelectedVal] = useState('');
  // const [freetext, setFreetext] = useState('');
  // const [selectedCheckboxVal, setselectedCheckboxVal] = useState([]);
  const [viewCamera, setViewCamera] = useState(false);

  const checkmultiplecheckbox = (item) => {
    if (selectedCheckboxVal.includes(item)) {
      setselectedCheckboxVal((old) => old.filter((val) => val != item));
    } else {
      setselectedCheckboxVal((old) => [...old, item]);
    }
  };

  const onsubmit = () => {
    let value = selectedCheckboxVal.length > 0 ? selectedCheckboxVal.toString() : selectedVal;
    value = value + ',' + freetext;
    setselectedCheckboxVal([]);
    setSelectedVal('');
    setFreetext('');
    onConfirm(value);
  };

  const onDismissPress = () => {
    setselectedCheckboxVal([]);
    setSelectedVal('');
    setFreetext('');
    onDismiss();
  };

  const renderAnswerOption = ({ item }) => {
    const isRadio = questionType === 'radio';
    const isSelected = isRadio ? item === selectedVal : selectedCheckboxVal.includes(item);

    const onPress = () => {
      isRadio ? setSelectedVal(item) : checkmultiplecheckbox(item);
    };

    const iconName = isRadio
      ? isSelected
        ? 'radio-button-on'
        : 'radio-button-off'
      : isSelected
        ? 'checkbox'
        : 'square-outline';

    return (
      <AButton
        mode="text"
        onPress={onPress}
        btnStyle={styles.radioLineView}
        icon={<Icon name={iconName} color={'#fff'} style={styles.iconStyle} size={23} />}
        fontSize={'medium'}
        styleText={{ color: '#fff', textTransform: 'capitalize' }}
        title={item}
      />
    );
  };
  const openCamera = async () => {
    let camerpermision = await launchCamera();
    if (camerpermision) {
      setViewCamera(true);
    }
  };
  return (
    <>
      {modalShow && !viewCamera && (
        <Portal>
          <SafeAreaView style={styles.safecontainer}>
            <Modal
              animationType="none"
              transparent={true}
              dismissable={false}
              onDismiss={''}
              onRequestClose={() => {}}
              hideModalContentWhileAnimating={true}
              style={[{ zIndex: 1, flex: 1 }]}
              contentContainerStyle={[{ justifyContent: 'center', flex: 1 }]}
              visible={modalShow}
            >
              <AButton onPress={() => {}} btnStyle={styles.modalWrapper} />
              <KeyboardAvoidingView
                behavior={Platform.OS === 'ios' ? 'position' : 'height'}
                enabled={Platform.OS == 'ios'}
                contentContainerStyle={[
                  styles.container,
                  {
                    height:
                      showAnwerInput &&
                      (questionType == 'radio' || questionType == 'checkbox') &&
                      questionOptions?.length > 2
                        ? 550
                        : showAnwerInput && (questionType == 'radio' || questionType == 'checkbox')
                          ? 450
                          : 410,
                  },
                ]}
                style={[
                  styles.modalConatiner,
                  Platform.OS == 'android' ? styles.container : {},
                  Platform.OS == 'android'
                    ? {
                        height:
                          showAnwerInput &&
                          (questionType == 'radio' || questionType == 'checkbox') &&
                          questionOptions?.length > 2
                            ? 550
                            : showAnwerInput &&
                                (questionType == 'radio' || questionType == 'checkbox')
                              ? 450
                              : 410,
                      }
                    : {},
                ]}
              >
                <LinearGradient
                  colors={['rgba(0, 164, 253, 0.9)', 'rgba(0, 170, 250, 0.9)']}
                  start={{ x: 0.7, y: 0.55 }}
                  end={{ x: 0.5, y: 1.0 }}
                  locations={[0.5, 0.6]}
                  style={[styles.modalContentConatiner, {}]}
                >
                  <ScrollView
                    showsHorizontalScrollIndicator={false}
                    showsVerticalScrollIndicator={false}
                    scrollEnabled={false}
                    // keyboardShouldPersistTaps={'handled'}
                    contentContainerStyle={styles.scrollContainerStyle}
                    style={[styles.viewStyle, { paddingBottom: 5 }]}
                  >
                    <View style={[styles.viewStyle]}>
                      <Image
                        source={
                          title == 'Reminder'
                            ? localimage.reminder
                            : title == 'Question'
                              ? localimage.question
                              : localimage.reminder
                        }
                        style={[styles.watchStyle]}
                      />
                      <View style={styles.contentStyle}>
                        <AText
                          fontWeight={FontStyle.fontBold}
                          styleText={{ color: '#fff', paddingTop: 15 }}
                          fontSize={'title'}
                        >
                          {title}
                        </AText>
                        <AText
                          fontWeight={FontStyle.fontMedium}
                          styleText={{ color: '#fff', paddingTop: 25, textTransform: 'capitalize' }}
                          fontSize={'large'}
                        >
                          {text}
                        </AText>
                        {showAnwerInput &&
                        (questionType == 'radio' || questionType == 'checkbox') ? (
                          <View style={{ flex: 0.7 }}>
                            <FlatList
                              data={questionOptions}
                              showsVerticalScrollIndicator={false}
                              showsHorizontalScrollIndicator={false}
                              contentContainerStyle={{ flexGrow: 1 }}
                              keyExtractor={(key, index) => index.toString()}
                              renderItem={renderAnswerOption}
                            />
                            {freeText == 1 && (
                              <Textinputs
                                textcolor={'white'}
                                textViewInputStyle={styles.textViewInputStyle}
                                stylesTextInput={[styles.serachTextinputStyle]}
                                keyboardtype={
                                  numericPads && Platform.OS == 'ios'
                                    ? 'numbers-and-punctuation'
                                    : numericPads && Platform.OS == 'android'
                                      ? 'numeric'
                                      : 'default'
                                }
                                placeholder={`Enter Answer`}
                                placeholderTextColor={'#fff'}
                                underlineColor={'#fff'}
                                maxLength={30}
                                onerror={false}
                                mode={'flat'}
                                value={freetext}
                                onchange={(txt) => setFreetext(txt)}
                              />
                            )}
                          </View>
                        ) : (
                          showAnwerInput && (
                            <Textinputs
                              textcolor={'white'}
                              textViewInputStyle={styles.textViewInputStyle}
                              stylesTextInput={[styles.serachTextinputStyle]}
                              keyboardtype={
                                questionType == 'number' && Platform.OS == 'ios'
                                  ? 'numbers-and-punctuation'
                                  : questionType == 'number' && Platform.OS == 'android'
                                    ? 'numeric'
                                    : 'default'
                              }
                              placeholder={`Enter answer ${
                                answerRequired ? '(required)' : '(not required)'
                              }`}
                              placeholderTextColor={'#fff'}
                              underlineColor={'#fff'}
                              maxLength={30}
                              onerror={false}
                              mode={'flat'}
                              value={selectedVal}
                              onchange={(txt) => setSelectedVal(txt)}
                            />
                          )
                        )}
                      </View>
                    </View>

                    <View style={styles.buttonContainer}>
                      <View>
                        {!answerRequired && (
                          <AButton
                            mode="text"
                            styleText={{ color: '#fff' }}
                            btnStyle={{ alignSelf: 'flex-start', padding: 20 }}
                            title={'Dismiss'}
                            onPress={() => {
                              onDismissPress();
                            }}
                          />
                        )}
                      </View>

                      <View style={{ flexDirection: 'row', marginEnd: 10 }}>
                        {title == 'Question' && (
                          <AButton
                            mode="contained"
                            bgColor={'#fff'}
                            btnStyle={{ ...styles.submitBtnStyle, width: windowWidth * 0.25 }}
                            styleText={{ color: colors.primary }}
                            disabled={
                              answerRequired && isEmpty(selectedVal) && isEmpty(selectedCheckboxVal)
                            }
                            title={'submit'}
                            onPress={() => {
                              onsubmit();
                            }}
                          />
                        )}
                        {clickPhoto && (
                          <AButton
                            mode="contained"
                            bgColor={'#fff'}
                            btnStyle={{ ...styles.submitBtnStyle, width: windowWidth * 0.25 }}
                            styleText={{ color: colors.primary }}
                            title={'Photo'}
                            onPress={() => {
                              onPhotoSubmit();
                              // openCamera();
                            }}
                          />
                        )}
                      </View>
                    </View>
                  </ScrollView>
                </LinearGradient>
              </KeyboardAvoidingView>
            </Modal>
          </SafeAreaView>
        </Portal>
      )}
      {viewCamera && (
        <OpenCameraView
          cancel={() => setViewCamera(false)}
          projectID={projectSelect._id}
          customerID={projectSelect.customerID}
          ImageName={studyData.customerName + `_` + studyData.name}
        />
      )}
    </>
  );
};

export default StudyPoPUP;

const styles = StyleSheet.create({
  safecontainer: {
    flex: 1,
    // backgroundColor: 'red',
  },
  modalWrapper: {
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    flex: 1,
    width: '100%',
  },
  cancelBtnStyle: {
    position: 'absolute',
    zIndex: 1,
    alignSelf: 'flex-end',
  },
  viewStyle: {
    alignSelf: 'center',
    flexDirection: 'row',
    flex: 1,
    paddingVertical: 5,
    width: '95%',
  },
  container: {
    backgroundColor: 'rgba(255,255,255,0.1)',
    height: windowHeight * 0.3,
    alignSelf: 'center',
    width: '100%',
    // paddingVertical: 10,
    position: 'absolute',
    // paddingHorizontal: 25,
    alignSelf: 'center',
    bottom: 0,
    borderTopLeftRadius: 28,
    borderTopRightRadius: 28,
    shadowColor: '#000',
    // paddingBottom:24,
    padding: 1,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 1.84,
    elevation: Platform.OS == 'ios' ? 2 : 10,
  },
  modalContentConatiner: {
    width: '100%',
    height: '100%',
    // flex: 1,
    borderTopLeftRadius: 28,
    borderTopRightRadius: 28,
    alignSelf: 'center',
  },
  modalConatiner: {
    alignSelf: 'center',
    position: 'absolute',
    width: '100%',
    bottom: 0,
  },
  contentStyle: {
    marginHorizontal: 10,
    justifyContent: 'flex-start',
    flexDirection: 'column',
    width: '80%',
    marginTop: 20,
  },

  watchStyle: {
    marginTop: 15,
    resizeMode: 'contain',
    height: 170,
    width: 150,
  },
  buttonContainer: {
    justifyContent: 'space-between',
    width: '92%',
    alignSelf: 'center',
    flexDirection: 'row',
    position: 'absolute',
    bottom: 25,
  },
  serachTextinputStyle: {
    fontSize: getFontSize('large'),
    width: '100%',
    alignself: 'center',
    marginTop: 25,
    textAlign: 'left',
  },
  textViewInputStyle: {
    marginTop: 0,
    marginBottom: 0,
  },
  radiobuttonView: {
    width: '95%',
  },
  radioLineView: {
    flexDirection: 'row',
    paddingTop: 5,
    width: '100%',
    justifyContent: 'flex-start',
  },
  iconStyle: {
    alignSelf: 'center',
    marginEnd: 12,
    marginStart: 5,
  },
  scrollContainerStyle: {
    flexGrow: 1,
    paddingBottom: 45,
    maxWidth: '100%',
    // backgroundColor: 'red'
    // justifyContent: "center",
  },
  submitBtnStyle: { borderRadius: 35, padding: 20, marginEnd: 19 },
});
