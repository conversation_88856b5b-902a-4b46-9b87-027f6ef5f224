export const dynamicSort = (property, numricSorting) => {
  var sortOrder = 1;
  if (property[0] === '-') {
    sortOrder = -1;
    property = property.substr(1);
  }

  return function (a, b) {
    /* next line works with strings and numbers,
     * and you may want to customize it to your needs
     */

    var result;
    if (numricSorting) {
      result = a[property] < b[property] ? -1 : a[property] > b[property] ? 1 : 0;
    } else {
      result =
        a[property].toLowerCase() < b[property].toLowerCase()
          ? -1
          : a[property].toLowerCase() > b[property].toLowerCase()
            ? 1
            : 0;
    }
    return result * sortOrder;
  };
};

export const alphanumericSort = () => {
  let regex = /(\d+)|(\D+)/g;
  const getChunks = (str) => {
    const chunks = [];
    str.replace(regex, (_, num, nonNum) => {
      chunks.push([num || Infinity, nonNum || '']);
    });
    return chunks;
  };
  return (a, b) => {
    var ax = [],
      bx = [];
    ax = getChunks(a.name);
    bx = getChunks(b.name);

    while (ax.length && bx.length) {
      var an = ax.shift();
      var bn = bx.shift();
      var nn = an[0] - bn[0] || an[1].localeCompare(bn[1]);
      if (nn) return nn;
    }

    return ax.length - bx.length;
  };
};
