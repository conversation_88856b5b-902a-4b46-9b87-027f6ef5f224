export const ALERT_SUCCESS = 'ALERT_SUCCESS';
export const ALERT_ERROR = 'ALERT_ERROR';
export const ALERT_HIDE = 'ALERT_HIDE';

const initialState = {
  success: false,
  message: '',
  error: false,
  closeapp: false,
  alerts: false,
  alert_show: false,
};

export default (state = initialState, action) => {
  switch (action.type) {
    case ALERT_SUCCESS:
      return {
        success: true,
        message: action.payload,
        error: false,
      };

    case ALERT_ERROR:
      return {
        success: false,
        message: action.payload,
        error: true,
      };
    case 'ALERT_CLOSEAPP':
      return {
        success: false,
        message: action.payload,
        error: false,
        closeapp: true,
      };
    case 'ALERT':
      return {
        success: false,
        message: action.payload,
        error: false,
        closeapp: false,
        alerts: true,
      };
    case 'ALERT_SHOW':
      return {
        success: false,
        message: action.payload,
        error: false,
        closeapp: false,
        alert_show: true,
      };

    case ALERT_HIDE:
      return {
        message: '',
        error: false,
        success: false,
        closeapp: false,
        alerts: false,
        alert_show: false,
      };

    default:
      return state;
  }
};
