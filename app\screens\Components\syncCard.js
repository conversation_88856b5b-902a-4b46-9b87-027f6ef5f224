import React from 'react';
import { View } from 'react-native';
import { ActivityIndicator, Card, useTheme } from 'react-native-paper';
import { AButton, AText, IconButton } from '_theme_components';
import { FontStyle } from '_utils';
import Icon from 'react-native-vector-icons/Ionicons';
import { isEmpty } from '_helpers';
import IconFeather from 'react-native-vector-icons/Feather';
import { styles } from './styles';

const SyncCard = ({
  data,
  showEditButton,
  showDataAlways,
  navigation,
  syncCheckBox,
  ISselected,
  selectstudy,
  showObservations,
  showDetail,
  showActivityIndicator,
  showObservationId,
}) => {
  const { colors } = useTheme();
  const fieldsArray = [
    { id: 1, name: 'taskName', showExtra: 'startTime', styles: styles.taskViewStyle },
    { id: 1, name: 'elementName', showExtra: 'EC', styles: styles.elementViewStyle },
    { id: 1, name: 'rating', showExtra: '', styles: styles.ratingViewStyle },
    { id: 1, name: 'frequency', showExtra: '', styles: styles.frequencyViewStyle },
    { id: 1, name: 'areaName', showExtra: '', styles: styles.areaViewStyle },
  ];
  const TextStyle = ({ value, showGray = false, fontBold = false }) => {
    return (
      <AText
        fontSize={'small'}
        lightGray={showGray}
        fontWeight={fontBold ? FontStyle.fontBold : FontStyle.fontMedium}
      >
        {value ?? ''}
      </AText>
    );
  };
  return (
    <Card style={styles.syncCardstyle}>
      <Card.Content style={styles.syncCardContainer}>
        <View>
          <View style={styles.syncCardName}>
            {syncCheckBox && (
              <IconButton
                onPress={() => {
                  selectstudy();
                }}
                btnStyle={styles.pressableStyle}
                icon={
                  <Icon
                    name={ISselected ? 'checkbox' : 'square-outline'}
                    color={ISselected ? colors.primary : '#878787'}
                    style={styles.syncCardiconStyle}
                    size={23}
                  />
                }
              />
            )}
            <AText fontSize={'large'} fontWeight={FontStyle.fontMedium}>
              {data.name}
            </AText>
            {showActivityIndicator && showObservationId == data.id && (
              <ActivityIndicator
                style={{
                  alignSelf: 'center',
                  justifyContent: 'center',
                  marginStart: 15,
                }}
                color={colors.primary}
                size={'small'}
              />
            )}
          </View>
          <View style={styles.syncCardRow}>
            {!isEmpty(data.customerName) && (
              <View style={styles.syncCardElement}>
                <Icon
                  name="folder-open-outline"
                  style={styles.syncCardiconStyle}
                  color={'#878787'}
                  size={20}
                />
                <TextStyle
                  showGray
                  fontBold
                  value={
                    data.customerName.length > 20
                      ? data.customerName.substring(0, 20) + '...'
                      : data.customerName
                  }
                />
              </View>
            )}

            <View style={styles.syncCardElement}>
              <Icon
                name="layers-outline"
                style={styles.syncCardiconStyle}
                color={'#878787'}
                size={22}
              />
              <TextStyle showGray fontBold value={data.project.name} />
            </View>
            <View style={styles.syncCardElement}>
              <Icon
                name="location-outline"
                style={styles.syncCardiconStyle}
                color={'#878787'}
                size={20}
              />
              <TextStyle showGray fontBold value={data.locationName} />
            </View>
          </View>
        </View>
        {/* {!showDataAlways && ( */}
        <AButton
          onPress={() => {
            showObservations(data.id);
          }}
          btnStyle={{ alignSelf: 'flex-start' }}
          mode="text"
          disabled={showActivityIndicator}
          title={showDetail && showObservationId == data.id ? 'HIDE DetailS' : 'SEE DetailS'}
          fontSize={'medium'}
          styleText={{ color: colors.primary }}
        />
        {/* )} */}
      </Card.Content>
      {showDetail && showObservationId == data.id ? (
        // || showDataAlways ?
        <View style={styles.cardTableContent}>
          {!isEmpty(data.data) &&
            data.data.map((item) => (
              <View key={Math.random()} style={styles.cardTableView}>
                {fieldsArray.map(({ styles, showExtra, name }) => (
                  <View
                    style={[
                      styles,
                      name === 'elementName'
                        ? {
                            paddingTop: isEmpty(item.continuesObservation) ? 17 : 0,
                          }
                        : {},
                    ]}
                  >
                    {showExtra === 'startTime' ||
                    (showExtra === 'EC' && !isEmpty(item.continuesObservation)) ? (
                      <TextStyle
                        showGray
                        value={
                          showExtra === 'startTime'
                            ? new Date(item[showExtra]).toLocaleTimeString().replace(/:[^:]*$/, '')
                            : !isEmpty(item.continuesObservation) && name === 'elementName'
                              ? 'EC'
                              : null
                        }
                      />
                    ) : null}
                    <TextStyle value={item[name]} />
                  </View>
                ))}
                {showEditButton && (
                  <IconButton
                    onPress={() =>
                      navigation.navigate('ProjectWrapper', {
                        screen: 'ObservationDetail',
                        params: { obsData: item, fromSyncPage: true },
                      })
                    }
                    btnStyle={{ ...styles.syncCardBtnStyle, borderColor: colors.secondary }}
                    icon={<IconFeather color={colors.primary} name="edit-3" size={20} />}
                  />
                )}
              </View>
            ))}
        </View>
      ) : null}
    </Card>
  );
};

export default SyncCard;
