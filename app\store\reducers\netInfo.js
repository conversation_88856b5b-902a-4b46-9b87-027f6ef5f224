export const NET_ON = 'NET_ON';
export const NET_OFF = 'NET_OFF';
export const SHOW_DOWNLOAD = 'SHOW_DOWNLOAD';
export const HIDE_DOWNLOAD = 'HIDE_DOWNLOAD';

const initialState = {
  success: false,
  netConnection: false,
  showDownload: false,
};

export default (state = initialState, action) => {
  switch (action.type) {
    case NET_ON:
      return {
        ...state,
        netConnection: true,
      };
    case NET_OFF:
      return {
        ...state,
        netConnection: false,
      };
    case SHOW_DOWNLOAD:
      return {
        ...state,
        showDownload: true,
      };
    case HIDE_DOWNLOAD:
      return {
        ...state,
        showDownload: false,
      };
    default:
      return state;
  }
};
