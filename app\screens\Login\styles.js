import { StyleSheet } from 'react-native';

export const styles = StyleSheet.create({
  textInputStyle: {
    padding: 5,
  },
  containerStyle: {
    margin: 10,
    marginTop: 28,
    width: '90%',
    justifyContent: 'center',
    alignSelf: 'center',
  },
  HeaderViewStyle: {
    marginTop: 75,
  },
  paswdErrorView: {
    marginVertical: 20,
    borderRadius: 7,
    backgroundColor: '#FCE7EC',
    paddingTop: 12,
  },
  wrngpaswdErrorView: {
    flexDirection: 'row',
    alignSelf: 'center',
    width: '99%',
    marginVertical: 20,
    borderRadius: 7,
    flexWrap: 'wrap',
    backgroundColor: '#FCE7EC',
    justifyContent: 'space-between',
    padding: 2,
    alignContent: 'center',
    alignItems: 'center',
  },
  wrngpassTextView: {
    width: '50%',
    alignSelf: 'center',
  },
  paswdErrortextview: {
    marginVertical: 25,
    paddingHorizontal: 12,
  },
  serverViewLine: {
    flexDirection: 'row-reverse',
    alignItems: 'center',
    paddingTop: 10,
    width: '100%',
    justifyContent: 'space-between',
  },
  iconStyle: {
    marginRight: 10,
  },
  serverChangeView: {
    marginTop: 20,
    borderWidth: 4,
    borderColor: '#E8EAED',
    width: '60%',
    padding: 15,
    alignSelf: 'center',
  },
  paswrdRstbtnStyle: {
    marginTop: 15,
  },
  frgtBtnStyle: {
    color: 'black',
  },
  submitBtn: {
    marginTop: 30,
    borderRadius: 50,
  },
  submitBtnContent: {
    paddingVertical: 15,
  },
  submitViewBtn: {
    marginTop: 30,
    width: '50%',
    alignSelf: 'center',
  },
  invalidUserTextstyle: { color: '#D81A4B', textAlign: 'center' },
});
