import { StyleSheet, Text, View } from 'react-native';
import React from 'react';
import { AButton, AText, BottomModalUI } from '_theme_components';
import { FontStyle, windowHeight } from '_utils';
import { styles } from './styles';
import { CustomSwitch } from '_helpers';
import { useTheme } from 'react-native-paper';

const sortingModal = ({
  closeModal,
  sortModalShow,
  GroupToggle,
  showGroup,
  showSwitch,
  orderTask,
  toggleOrdering,
  title,
}) => {
  const { colors } = useTheme();
  return (
    <BottomModalUI
      width={'100%'}
      closeShow
      showScroll
      closeModal={() => closeModal(false)}
      height={
        showSwitch && windowHeight > 1000
          ? windowHeight * 0.35
          : !showSwitch && windowHeight > 1000
            ? windowHeight * 0.22
            : showSwitch && windowHeight < 1000
              ? windowHeight * 0.5
              : windowHeight * 0.35
      }
      modalShow={sortModalShow}
    >
      <View style={styles.sortContainer}>
        <AText
          fontWeight={FontStyle.fontBold}
          styleText={{ paddingTop: 15 }}
          color={'#3C4555'}
          fontSize={'title'}
        >
          Sort {title}
        </AText>
        {showSwitch && (
          <>
            <View style={[styles.sortmodalContainer]}>
              <AText
                fontWeight={FontStyle.fontMedium}
                styleText={{ paddingTop: 15 }}
                color={'#3C4555'}
                fontSize={'large'}
              >
                Show group first
              </AText>
              <CustomSwitch
                selected={showGroup}
                option1={'ON'}
                option2={'OFF'}
                updatedSwitchData
                onSelectSwitch={() => {
                  GroupToggle(!showGroup);
                }}
                selectionColor={'blue'}
              />
            </View>
            <View style={[styles.sortLineContainer]} />
          </>
        )}

        <View style={styles.sortmodalContainer}>
          <AText fontWeight={FontStyle.fontMedium} fontSize={'large'} color={'#3C4555'}>
            Sort alphabetically
          </AText>
          <View style={{ flexDirection: 'row', alignSelf: 'flex-end' }}>
            <AButton
              btnStyle={styles.sortBtnStyle}
              title={'A-Z'}
              styleText={{ color: orderTask == 'asec' ? '#fff' : '#000' }}
              bgColor={orderTask == 'asec' ? colors.primary : colors.secondary}
              onPress={() => {
                closeModal(false), toggleOrdering('asec');
              }}
            />
            <AButton
              title={'Z-A'}
              btnStyle={styles.sortBtnStyle}
              styleText={{ color: orderTask == 'desc' ? '#fff' : '#000' }}
              bgColor={orderTask == 'desc' ? colors.primary : colors.secondary}
              onPress={() => {
                closeModal(false), toggleOrdering('desc');
              }}
            />
          </View>
        </View>
      </View>
    </BottomModalUI>
  );
};

export default sortingModal;
