import React, { useEffect } from 'react';
import { ScrollView, SafeAreaView, Platform, View, StyleSheet } from 'react-native';
import { FontStyle, STUDY_CANCELING_MESSAGE2 } from '../../utils/config';
import { AButton, AText } from '_theme_components';
import { useTheme } from 'react-native-paper';
import { BottomModal } from '_components';
import { useState } from 'react';

const CreateStudyLayout = ({
  children,
  navigation,
  title,
  areaselectedfalse,
  backPressed,
  backPressCancel,
}) => {
  const ref = React.useRef(null);
  const [cancelStudyModal, setCancelStudyModal] = useState(false);
  const [cancelModalTwo, setCancelModalTwo] = useState(false);

  const { colors, dark } = useTheme();

  useEffect(() => {
    if (backPressed) {
      setCancelStudyModal(true);
    }
  }, [backPressed]);
  const confirmPress = () => {
    setCancelStudyModal(false);
    backPressCancel();
    cancelModalTwo ? navigation.navigate('HomeNav', { screen: 'Home' }) : setCancelModalTwo(true);
  };
  return (
    <View style={[styles.containerStyle, { backgroundColor: colors.onSurface }]}>
      <SafeAreaView style={styles.safeAreaStyle}>
        <View style={styles.headerStyle}>
          <AButton
            mode="text"
            styleText={{ color: '#777778' }}
            fontWeight={FontStyle.fontMedium}
            btnStyle={styles.cancelBtnStyle}
            fontSize={'xtrasmall'}
            title={'Cancel'}
            onPress={() => {
              setCancelStudyModal(true);
            }}
          />
        </View>
        <View style={styles.headercontentContainer}>
          <AText
            fontWeight={FontStyle.fontBold}
            styleText={{ color: '#777778' }}
            fontSize={'title'}
          >
            Create Study
          </AText>
          <AText fontWeight={FontStyle.fontBold} fontSize={'medium'}>
            {title}
          </AText>
          {areaselectedfalse && (
            <AText fontSize={'medium'} styleText={styles.textStyle} fontWeight={FontStyle.fontBold}>
              Please select at least one area
            </AText>
          )}
        </View>
        <ScrollView
          showsVerticalScrollIndicator={false}
          showsHorizontalScrollIndicator={false}
          style={{
            backgroundColor: dark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(255, 255, 255, 0.9)',
          }}
          contentContainerStyle={styles.contentContainerstyle}
          ref={ref}
        >
          {children}
        </ScrollView>
        <BottomModal
          modalShow={cancelStudyModal ? true : cancelModalTwo ? true : false}
          confirmPress={() => {
            confirmPress();
          }}
          cancelShow={() => {
            setCancelStudyModal(false), setCancelModalTwo(false);
          }}
          title={'Do you want to cancel this study?'}
          body={STUDY_CANCELING_MESSAGE2}
          clrBody={'red'}
          ButtonContainer={cancelStudyModal ? { flexDirection: 'row-reverse' } : {}}
          confirm={'Cancel Study'}
          reject={'Continue Study'}
        />
      </SafeAreaView>
    </View>
  );
};

export default CreateStudyLayout;

const styles = StyleSheet.create({
  containerStyle: { flex: 1, paddingTop: 15 },
  safeAreaStyle: { flex: 1, paddingBottom: Platform.OS === 'ios' ? 100 : 0 },
  headerStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    alignSelf: 'center',
    alignItems: 'center',
  },
  cancelBtnStyle: { marginStart: 15, padding: 1, alignSelf: 'flex-start' },
  textStyle: { color: '#fd5c63', textAlign: 'center', paddingTop: 15 },
  headercontentContainer: {
    marginTop: 4,
    marginBottom: 10,
    padding: 10,
    alignSelf: 'center',
    width: '79%',
  },
  contentContainerstyle: {
    padding: 15,
    flexGrow: 1,
    alignItems: 'center',
    paddingBottom: 150,
  },
  buttonContainer: {
    flexDirection: 'row',
    marginHorizontal: 7,
  },
  trashIconStyle: {
    marginRight: 15,
    padding: 10,
    height: 50,
    width: 55,
    borderRadius: 100,
  },
});
