import { Image, StyleSheet, Text, View } from 'react-native';
import React from 'react';
import { AText, BottomModalUI } from '_theme_components';
import { FontStyle, localimage, windowHeight, windowWidth } from '_utils';
import { useTheme } from 'react-native-paper';
import { LoadingProgressBar, ProgressBar, isEmpty } from '_helpers';

const SubmittingModal = ({ submitingModalShow, progress }) => {
  const { colors, dark } = useTheme();

  return (
    <BottomModalUI
      width={'100%'}
      ModalClose={false}
      height={windowHeight}
      backgroundColor={dark ? colors.onSurface : '#fff'}
      modalShow={submitingModalShow}
    >
      <View style={[styles.modalCointainer]}>
        <Image
          source={localimage.submittingdata}
          style={[
            styles.submitStyle,
            {
              height: windowWidth > windowHeight ? windowHeight * 0.55 : windowWidth * 0.55,
              width: windowWidth > windowHeight ? windowHeight * 0.65 : windowWidth * 0.55,
            },
          ]}
        />
        <View
          style={[
            styles.SubmittingView,
            {
              paddingTop: 5,
              width: windowWidth > windowHeight ? windowHeight * 0.65 : windowWidth * 0.5,
            },
          ]}
        >
          {isEmpty(progress) ? <LoadingProgressBar /> : <ProgressBar progress={progress} />}
        </View>
        <View
          style={[
            styles.SubmittingView,
            {
              width: windowWidth > windowHeight ? windowHeight * 0.65 : windowWidth * 0.5,
            },
          ]}
        >
          <AText fontSize={'xtralarge'} fontWeight={FontStyle.fontBold}>
            Submitting data...
          </AText>
        </View>
      </View>
    </BottomModalUI>
  );
};

export default SubmittingModal;

const styles = StyleSheet.create({
  modalCointainer: {
    flex: 1,
    justifyContent: 'center',
  },
  submitStyle: {
    resizeMode: 'contain',
    alignSelf: 'center',
  },
  SubmittingView: {
    // marginVertical: 20,
    alignSelf: 'center',
    paddingTop: 25,
  },
  SubmitedView: {
    // marginVertical: 20,
    alignSelf: 'center',
  },
});
