import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import {
  AreaScreen,
  ChangePasswordScreen,
  CreateStudyAreaScreen,
  CreatestudyProjectsScreen,
  CreateStudyScreen,
  ElementScreen,
  FrequencyScreen,
  HelpScreen,
  HomeScreen,
  LocationScreen,
  NextElementScreen,
  ObservationDetail,
  PrivacyPolicyScreen,
  ProfileScreen,
  ProjectScreen,
  RateScreen,
  SubmitDataScreen,
  SyncScreen,
  TermsandConditionScreen,
  TaskScreen,
  StartStudy,
  StudyPage,
  RecoverScreen,
  ErrorData,
} from '_screen';
import { DrawerContent, UpdateVesionModal } from '_helpers';
import { createDrawerNavigator } from '@react-navigation/drawer';
import { AppProvider } from '../hooks/TimeContext';

const AppNaivgation = () => {
  const Stack = createStackNavigator();
  const Drawer = createDrawerNavigator();

  const HomeNavigations = () => {
    return (
      <Drawer.Navigator
        screenOptions={{
          headerShown: false,
          unmountOnBlur: true,
          lazy: false,
          swipeEnabled: false,
        }}
        backBehavior={'initialRoute'}
        swipeEnabled={false}
        detachInactiveScreens={true}
        drawerContent={(props) => <DrawerContent {...props} />}
      >
        <Drawer.Screen component={HomeScreen} options={{ unmountOnBlur: false }} name="Home" />
        <Drawer.Screen component={AllProjectScreen} name="ProjectWrapper" />
        <Drawer.Screen component={SyncScreen} name="Sync" />
        <Drawer.Screen component={ProfileNav} name="ProfileNav" />
      </Drawer.Navigator>
    );
  };
  const ProfileNav = () => {
    return (
      <Stack.Navigator initialRouteName="Profile" screenOptions={{ headerShown: false }}>
        <Stack.Screen name="Profile" component={ProfileScreen} />
        <Stack.Screen name="ChangePassword" component={ChangePasswordScreen} />
      </Stack.Navigator>
    );
  };
  const AllProjectScreen = () => {
    return (
      <AppProvider>
        <Stack.Navigator
          detachInactiveScreens={true}
          initialRouteName="Project"
          screenOptions={{ headerShown: false, detachPreviousScreen: true }}
        >
          <Stack.Screen
            name="Project"
            screenOptions={{ freezeOnBlur: true }}
            options={{ gestureEnabled: false }}
            component={ProjectScreen}
          />
          <Stack.Screen
            name="Locations"
            screenOptions={{ freezeOnBlur: true }}
            options={{ gestureEnabled: false }}
            component={LocationScreen}
          />
          <Stack.Screen
            name="CreateStudyArea"
            screenOptions={{ freezeOnBlur: true }}
            options={{ gestureEnabled: false }}
            component={CreateStudyAreaScreen}
          />
          <Stack.Screen name="Area" options={{ gestureEnabled: false }} component={AreaScreen} />
          <Stack.Screen name="Task" options={{ gestureEnabled: false }} component={TaskScreen} />
          <Stack.Screen
            name="Element"
            options={{ gestureEnabled: false }}
            component={ElementScreen}
          />
          <Stack.Screen name="Rate" options={{ gestureEnabled: false }} component={RateScreen} />
          <Stack.Screen
            name="Frequency"
            options={{ gestureEnabled: false }}
            component={FrequencyScreen}
          />
          <Stack.Screen
            name="ObservationDetail"
            options={{ gestureEnabled: false }}
            component={ObservationDetail}
          />
          <Stack.Screen
            name="SubmitData"
            options={{ gestureEnabled: false }}
            component={SubmitDataScreen}
          />
          <Stack.Screen
            name="NextElement"
            options={{ gestureEnabled: false }}
            component={NextElementScreen}
          />
          <Stack.Screen
            name="StartStudy"
            options={{ gestureEnabled: false }}
            component={StartStudy}
          />
          <Stack.Screen
            name="StudyPage"
            options={{ gestureEnabled: false }}
            component={StudyPage}
          />
        </Stack.Navigator>
      </AppProvider>
    );
  };

  return (
    <>
      <Stack.Navigator initialRouteName="HomeNav" screenOptions={{ headerShown: false }}>
        <Stack.Screen name="HomeNav" component={HomeNavigations} />
        <Stack.Screen name="Help" component={HelpScreen} />
        <Stack.Screen name="PrivacyPolicy" component={PrivacyPolicyScreen} />
        <Stack.Screen name="TermsandCondition" component={TermsandConditionScreen} />
        <Stack.Screen name="RecoverScreen" component={RecoverScreen} />
        <Stack.Screen name="ErrorScreen" component={ErrorData} />
      </Stack.Navigator>
      <UpdateVesionModal />
    </>
  );
};

export default AppNaivgation;
