import { createTable, insertIntoTable, isEmpty, getElementsGroupByTask, isJson } from '_helpers';
import { ENTRY_ALREADY_EXIST, tables } from '_utils';
import { createAction, deleteEntries } from '_action';
import { ALERT_ERROR, ALERT_SUCCESS } from '../store/reducers/alert';

export const creatingData =
  (dataList, payload, netConnection, dbDetail, type, showmsg = true) =>
  async (dispatch) => {
    let table, createTable, api;
    if (type == 'role') {
      table = tables.ROLES_TABLE;
      createTable = tables.CREATE_ROLE_TABLE;
      api = 'role/add';
    } else if (type == 'area') {
      table = tables.AREAS_TABLE;
      createTable = tables.CREATE_AREA_TABLE;
      api = 'area/add';
    } else {
      table = tables.ELEMENTS_TABLE;
      createTable = tables.CREATE_ELEMENT_TABLE;
      api = 'element/add';
    }
    try {
      var nameExists = await checkIfNameExists(dataList, payload.name);
      if (nameExists) {
        dispatch({ type: ALERT_ERROR, payload: ENTRY_ALREADY_EXIST });
        return;
      }

      if (netConnection) {
        await dispatch(createAction(api, payload, table, dbDetail, showmsg));
      } else {
        await createOfflineTable(payload, createTable, dbDetail, table);
        dispatch({ type: ALERT_SUCCESS, payload: `${table} created successfully` });
      }
    } catch (error) {
      // Handle any errors here
      console.error('Error in creatingData:', error);
    }
  };
export const getUniqueName = (name) => {
  if (!name) {
    return '';
  }
  return formatStringForComparison(name).replace(/\s+/g, '_');
};

export const formatStringForComparison = (string) => {
  if (!string) {
    return;
  }
  return string.toString().toLowerCase().trim();
};

export const checkIfNameExists = (data, fieldName) => {
  return new Promise((resolve, reject) => {
    let result = false;
    const itemExist = data.filter((item) => {
      if (getUniqueName(item.name) == getUniqueName(fieldName)) return (result = true);
    });
    resolve(result);
  });
};

export const createOfflineTable = async (payload, creationTable, dbDetail, dataTable) => {
  const offlineEntryData = createOfflineStructure(payload, creationTable);
  const createTables = await createTable(creationTable, dbDetail);
  var createTableInsert = await insertIntoTable(creationTable, offlineEntryData, dbDetail);
  var addOfflineData = await insertIntoTable(dataTable, offlineEntryData, dbDetail);

  Promise.all([createTables, createTableInsert, addOfflineData])
    .then(async () => {
      return true;
    })
    .catch((error) => {
      return false;
    });
};
export const createOfflineStructure = (dataItem, dataTable) => {
  let _id = null;
  let name = dataItem.name;
  let rating = null;
  let userAdded = null;
  let category = null;
  let type = null;
  let position = null;
  let studyTypes = null;
  let taskID = null;
  let addPosition = null;
  let count;
  let unitOfMeasure;
  let relaxationAllowance;
  let contingencyAllowance;

  if (dataTable == tables.AREAS_TABLE || dataTable == tables.CREATE_AREA_TABLE) {
    _id = `${Date.now()}-${name}-area`;
  } else if (dataTable == tables.ELEMENTS_TABLE || dataTable == tables.CREATE_ELEMENT_TABLE) {
    _id = `${Date.now()}-${name}-element`;
    rating = dataItem.rating;
    userAdded = dataItem.userAdded;
    category = dataItem.categoryID;
    studyTypes = [2, 3];
    type = dataItem.type;
    taskID = dataItem.taskID;
    count = dataItem.count;
    addPosition = dataItem.addPosition;
    contingencyAllowance = dataItem.contingencyAllowance;
    relaxationAllowance = dataItem.relaxationAllowance;
  } else if (dataTable == tables.ROLES_TABLE || dataTable == tables.CREATE_ROLE_TABLE) {
    _id = `${Date.now()}-${name}-role`;
    position = dataItem.position;
  }

  let data = [
    {
      _id,
      name,
      position,
      type,
      rating,
      category,
      studyTypes,
      taskID,
      addPosition,
      count,
      projectID: dataItem.projectID,
      customerID: dataItem.customerID,
      userAdded,
      unitOfMeasure,
      relaxationAllowance,
      contingencyAllowance,
    },
  ];

  return data;
};

export const reorderTask = async ([offlineEntryData], dbDetail) => {
  const taskID = offlineEntryData.taskID;
  var resoponse;
  return new Promise(async (res) => {
    resoponse = await getElementsGroupByTask(offlineEntryData.projectID, dbDetail, taskID);
    res(resoponse);
  })
    .then(async ([task]) => {
      if (isEmpty(task)) {
        return;
      }
      const payload = {
        taskID: taskID,
        projectID: offlineEntryData.projectID,
      };
      await deleteEntries(tables.ELEMENTS_TABLE, payload, dbDetail);

      let oldElementOrder = task.grouped_data.slice();
      oldElementOrder = isJson(oldElementOrder) ? JSON.parse(oldElementOrder) : oldElementOrder;

      let newElementOrder = [];
      if (offlineEntryData.addPosition == 'addFirst') {
        newElementOrder.push(offlineEntryData, ...oldElementOrder);
      } else if (offlineEntryData.addPosition == 'addLast') {
        newElementOrder.push(...oldElementOrder, offlineEntryData);
      } else {
        oldElementOrder.forEach((elementID) => {
          newElementOrder.push(elementID);
          if (elementID == offlineEntryData.addPosition) {
            newElementOrder.push(offlineEntryData);
          }
        });
      }
      task.elements = newElementOrder;

      await insertIntoTable(tables.ELEMENTS_TABLE, newElementOrder, dbDetail);
      return;
    })
    .catch((error) => {
      return;
    });
};
