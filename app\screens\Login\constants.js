import { DEVELOPMENT_URL, PRODUCTION_URL, STAGING_URL } from '_utils';

export const serverList = [
  { id: 1, label: 'Production Server', value: 'productionServer', url: PRODUCTION_URL },
  { id: 4, label: 'Staging Server', value: 'stagingServer', url: STAGING_URL },
  { id: 5, label: 'Development Server', value: 'developmentServer', url: DEVELOPMENT_URL },
];

export const fieldsArray = [
  { id: 1, name: 'Email Address', placeholder: 'Email', value: 'email', isPassword: false },
  { id: 2, name: 'Password', placeholder: 'Password', value: 'password', isPassword: true },
];

export const UserrNotFound = (email) =>
  `Sorry, we can't find an account matching the email address: ${email}\n\n` +
  'If you do not have an account, please contact your system administrator. ' +
  'Otherwise, try again using a different email address.';

export const invalidData = 'Wrong email or password';
