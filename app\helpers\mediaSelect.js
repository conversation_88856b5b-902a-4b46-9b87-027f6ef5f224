// import ImagePicker from 'react-native-image-crop-picker';
// import * as ImagePicker from "react-native-image-picker"
import { <PERSON>ert, PermissionsAndroid } from "react-native";
import { windowHeight, windowWidth } from "_utils";

export const launchCamera = async () => {
  const requestCameraPermission = async () => {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.CAMERA,
          {
            title: 'Camera Permission',
            message: 'App needs camera permission',
          },
        );
        // If CAMERA Permission is granted
        return granted === PermissionsAndroid.RESULTS.GRANTED;
      } catch (err) {
        return false;
      }
    } else return true;
  };

  const requestExternalWritePermission = async () => {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
          {
            title: 'External Storage Write Permission',
            message: 'App needs write permission',
          },
        );
        // If WRITE_EXTERNAL_STORAGE Permission is granted
        return granted === PermissionsAndroid.RESULTS.GRANTED;
      } catch (err) {
        alert('Write permission err', err);
      }
      return false;
    } else return true;
  };
  let isCameraPermitted = await requestCameraPermission();
  let isStoragePermitted = await requestExternalWritePermission();
  if (isCameraPermitted) {
    return true
    // let options = {
    //   mediaType: 'photo',
    //   maxWidth: windowWidth*0.85,
    //   maxHeight: windowHeight*0.85,
    //   quality: 0.7,
    //   saveToPhotos: false,
    // };
    // return new Promise((resolve, reject) => {
    //   ImagePicker.launchCamera(options, (res) => {
    //     var response = res
    //   }).then((res) => {
    //     resolve(res);
    //   }).catch((error) => {
    //     reject(error);
    //   });
    // })
  } else {
    alert('Please give permission to open camera in setting');
    return false
  }

}


// export const MultipleselectGellery = async () => {
//   let options = {
//     mediaType: 'photo'
//     maxWidth: 300,
//     maxHeight: 550,
//     quality: 1,
//     videoQuality: 'low',
//     durationLimit: 30, //Video max duration in seconds
//     saveToPhotos: true,
//     selectionLimit: 0,
//     // cropping: cropvalue,
//     // includeBase64: true,
//     // includeExif: true,
//     multiple: true,
//     maxFiles: 10,
//     showsSelectedCount: true,
//     compressImageQuality: 0.4,
//   };
//   return new Promise((resolve, reject) => {
//     // ImagePicker.launchImageLibrary({
//     ImagePicker.launchImageLibrary(options, (res) => {
//       var response = res
//     }).then((res) => {
//       resolve(res);
//     }).catch((error) => {
//       reject(error);
//     });
//   });
// }

// export const vedioRecorder = async (crop) => {
//   let cropvalue = crop == null ? false : crop
//   return new Promise((resolve, reject) => {
//     ImagePicker.openCamera({
//       mediaType: 'video',
//       width: 300,
//       height: 400,
//       includeBase64: true,
//       duration: 3,
//       cropping: cropvalue,
//     }).then((res) => {
//       resolve(res);
//     }).catch((error) => {
//       reject(error);
//     });
//   });
// }



