import {
  CREATE_AREA_TABLE,
  CREATE_ELEMENT_TABLE,
  CREATE_ROLE_TABLE,
  ROLES_TABLE,
  tables,
} from '_utils';
import {
  deleteById,
  getDataByTableAndID,
  getDataByTableAndName,
  getStudiesBYProjectID,
  getofflineRoleStudies,
  isEmpty,
  updateOfflineRoleInStudies,
  updateTable,
} from '_helpers';

export const mergeOfflineData = (dbDetail, data) => async (dispatch) => {
  try {
    const getProjectData = async (type, elem) => {
      const projectData = await getStudiesBYProjectID(type, elem._id, dbDetail);
      return { type, projectData };
    };

    const results = await Promise.all(
      data.map(async (elem) => [
        await getProjectData('area', elem),
        await getProjectData('element', elem),
      ])
    );

    const processItem = async (item) => {
      if (item.projectData && item.projectData.length > 0) {
        const promises = item.projectData.map((projectItem) => {
          if (projectItem[item.type].indexOf(item.type) !== -1)
            deleteIfNotUnique(item.type, projectItem[item.type], dbDetail, projectItem.projectID);
        });
        return Promise.all(promises);
      }
      return null;
    };

    const dataUpload = results.flat().map(processItem);

    await Promise.all(dataUpload.filter(Boolean));
  } catch (error) {
    console.error('An error occurred:', error);
  }
};

export const deleteIfNotUnique = async (type, elemData, dbDetail, projectID) => {
  const keyMap = {
    role: { key: 'OfflineRole', table: CREATE_ROLE_TABLE },
    area: { key: 'OfflineArea', table: CREATE_AREA_TABLE },
    element: { key: 'OfflineElement', table: CREATE_ELEMENT_TABLE },
  };

  const { key, table } = keyMap[type];
  try {
    const [localData, data] = await Promise.all([
      getDataByTableAndID(key, elemData, dbDetail),
      getDataByTableAndName(table, elemData, projectID, dbDetail),
    ]);
    localData = localData[0];
    data = data[0];
    if (!isEmpty(data) && localData.name === data.name) {
      const deletePromise = deleteById(table, elemData, dbDetail);
      const processPromise = processStudyItem(elemData, type, dbDetail, data._id);
      return Promise.all([deletePromise, processPromise]);
    }
  } catch (error) {
    console.error(`Error in deleteIfNotUnique: ${error}`);
    throw error; // Rethrow the error for higher-level handling if needed.
  }
};

export const processStudyItem = (offline, matchSyntex, dbDetail, foundId) => {
  const payload = {
    _id: foundId,
    offline: offline,
  };
  if (matchSyntex == 'role') {
    updateOfflineRoleInStudies(offline, foundId, dbDetail);
  } else {
    updateTable(tables.STUDY_DATA_TABLE, matchSyntex, payload, dbDetail);
    updateTable(tables.RECOVERY_DATA_TABLE, matchSyntex, payload, dbDetail);
  }
};
