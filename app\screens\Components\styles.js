import { windowHeight } from '_utils';
import { Platform, StyleSheet } from 'react-native';

export const styles = StyleSheet.create({
  buttonContainerStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 30,
    width: '100%',
    alignContent: 'center',
    alignItems: 'center',
  },
  bottomModalContainerStyle: {
    justifyContent: 'center',
    width: '90%',
    marginTop: 15,
    alignSelf: 'center',
  },
  bottomModalConfirmBtnStyles: {
    // backgroundColor: '#fff',
    borderRadius: 0,
    padding: 20,
  },
  bottomModalBtnContentStyle: {
    paddingVertical: 10,
  },

  //---breadcrumb style
  iconStyle: {
    alignSelf: 'center',
    marginHorizontal: 4,
  },
  breadCrumbContainer: {
    position: 'absolute',
    top: 0,
    flexDirection: 'row',
    backgroundColor: '#A9AFC6',
    justifyContent: 'space-evenly',
    paddingHorizontal: 5,
    alignItems: 'center',
  },
  textStyle: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: Platform.OS == 'ios' ? 3 : 0,
  },
  btnStyle: {
    alignItems: 'center',
  },

  //location card style
  locationCardstyle: {
    width: '99%',
    alignSelf: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    // shadowColor: "rgba(0,0,0,0.5)",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 1.65,

    elevation: Platform.OS == 'ios' ? 0 : 5,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#fff',
    marginVertical: 12,
    paddingVertical: 16,
  },
  locationCardRow: {
    flexDirection: 'row',
    width: '70%',
    alignItems: 'center',
    marginVertical: 7,
    justifyContent: 'space-between',
  },
  locationCardElement: {
    flexDirection: 'row',
    alignItems: 'center',
    flexDirection: 'row',
    marginTop: 11,
    alignSelf: 'flex-start',
    width: '97%',
  },

  // ---modal slct container

  modalSelectContainerStyle: {
    flex: 1,
    justifyContent: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
  },
  selectElemetStyle: {
    width: '100%',
    borderWidth: 0.9,
    marginTop: 5,
    paddingHorizontal: 15,
    borderRadius: 10,
    paddingVertical: 15,
    color: '#C7C7C7',
    borderColor: '#c7c7c7',
    alignItems: 'center',
    justifyContent: 'space-between',
    flexDirection: 'row-reverse',
  },
  modalSelectInsideContainerStyle: {
    backgroundColor: '#fff',
    maxHeight: windowHeight * 0.3,
    zIndex: 1000,
    alignSelf: 'center',
    justifyContent: 'center',
  },
  itemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 5,
    paddingBottom: 7,
    paddingHorizontal: 10,
    justifyContent: 'space-between',
  },
  modalSelectBtncontainer: {
    flexDirection: 'row',
    alignItems: 'center',
    alignContent: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    justifyContent: 'space-between',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 1.84,
    elevation: Platform.OS == 'ios' ? 0 : 5,
  },
  modalSelectScrollContainer: {
    borderColor: '#c7c7c7',
    borderTopWidth: 0.7,
    borderBottomWidth: 0.7,
    // marginTop: 35,
    paddingBottom: 25,
    paddingTop: 5,
  },
  modalSelectHeadingTextStyle: {
    textTransform: 'capitalize',
    paddingTop: 12,
    paddingBottom: 12,
    paddingLeft: 20,
  },
  // project card style
  projectCardstyle: {
    width: '99%',
    alignSelf: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1.5,
    },
    shadowOpacity: 0.9,
    shadowRadius: 1.5,
    elevation: Platform.OS == 'ios' ? 0 : 5,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#fff',
    marginVertical: 12,
    paddingVertical: 27,
    paddingHorizontal: 12,
  },
  projectCardContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    flex: 1,
  },
  projectcontentContainer: {
    width: '80%',
  },

  projectCardRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 2,
    justifyContent: 'space-between',
    width: '100%',
    alignSelf: 'flex-start',
  },
  projectCardElement: {
    flexDirection: 'row',
    alignItems: 'center',
    flexDirection: 'row',
    marginTop: 12,
    alignSelf: 'flex-start',
  },
  imageStyle: {
    resizeMode: 'contain',
    marginTop: 4,
    height: 75,
    width: 75,
  },

  //submitcard style
  submitCardstyle: {
    flexDirection: 'row',
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 10,
    marginBottom: 8,
    opacity: 0.9,
    // backgroundColor: 'rgba(255, 255, 255, 0.2)',
    shadowColor: '#000',
    // padding: 2,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 1.22,
    borderWidth: 1,
    elevation: Platform.OS == 'ios' ? 0 : 2,
    flex: 1,
  },
  slectedContainer: {
    height: '100%',
    width: '100%',
    alignSelf: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'space-between',
  },
  textContainer: {
    width: '12%',
    paddingVertical: 10,
    alignItems: 'flex-start',
    justifyContent: 'center',
  },
  timeContainer: {
    width: '12%',
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 0,
  },

  ecViewContainer: { marginStart: 5, padding: 7, flexWrap: 'wrap', borderRadius: 15 },

  //syncCardStyle

  syncCardstyle: {
    width: '90%',
    justifyContent: 'center',
    shadowColor: 'rgba(0,0,0,0.5)',
    shadowOffset: {
      width: 0,
      height: 5,
    },
    borderRadius: 12,
    shadowOpacity: 0.3,
    shadowRadius: 1.0,
    elevation: Platform.OS == 'ios' ? 0 : 5,
    marginVertical: 12,
    paddingBottom: 14,
    paddingTop: 14,
    paddingHorizontal: 2,
  },
  syncCardContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  syncCardRow: {
    flexDirection: 'column',
    alignItems: 'center',
    marginVertical: 2,
    alignSelf: 'flex-start',
  },
  syncCardName: {
    flexDirection: 'row',
    marginTop: 5,
    alignSelf: 'flex-start',
  },
  syncCardElement: {
    flexDirection: 'row',
    marginTop: 8,
    justifyContent: 'center',
    alignSelf: 'flex-start',
  },
  syncCardiconStyle: {
    alignSelf: 'center',
    marginEnd: 12,
    marginStart: 5,
  },
  cardTableContent: {
    flexDirection: 'column',
    width: '100%',
    paddingHorizontal: 15,
    marginTop: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cardTableView: {
    flexDirection: 'row',
    width: '100%',
    marginTop: 12,
    alignItems: 'center',
    alignSelf: 'center',
    alignContent: 'center',
    justifyContent: 'space-evenly',
  },
  pressableStyle: {
    width: 38,
    padding: 0,
    paddingVertical: 0,
  },
  syncCardBtnStyle: {
    marginStart: 12,
    alignItems: 'center',
    borderWidth: 0.9,
    borderRadius: 150,
    width: 50,
    marginTop: 18,
    height: 50,
    alignSelf: 'center',
  },
  taskViewStyle: { width: '22%', justifyContent: 'center', alignItems: 'flex-start' },
  elementViewStyle: { width: '24%', alignItems: 'flex-start' },
  ratingViewStyle: { width: '10%', paddingTop: 17, alignItems: 'center' },
  frequencyViewStyle: { width: '7%', paddingTop: 17, alignItems: 'center' },
  areaViewStyle: { width: '24%', paddingTop: 17, alignItems: 'center' },

  //syncingModal style
  syncingContainer: {
    marginVertical: 14,
    paddingHorizontal: 10,
    paddingVertical: 5,
    justifyContent: 'center',
  },
  ViewcontainerStyle: {
    paddingHorizontal: 35,
  },
  errorSynceViewStyle: {
    paddingVertical: 5,
    backgroundColor: '#F9DDDE',
    alignItems: 'center',
    marginStart: 5,
    alignSelf: 'flex-start',
    paddingHorizontal: 15,
    flexWrap: 'wrap',
    flexDirection: 'row',
    borderRadius: 15,
    justifyContent: 'space-evenly',
    backgroundColor: '#F9DDDE',
  },
  synceViewStyle: {
    paddingVertical: 5,
    backgroundColor: '#F9DDDE',
    alignItems: 'center',
    marginStart: 5,
    alignSelf: 'flex-start',
    paddingHorizontal: 15,
    flexWrap: 'wrap',
    flexDirection: 'row',
    borderRadius: 15,
    justifyContent: 'space-evenly',
    backgroundColor: '#DEF3E2',
  },
  progressViewStyle: {
    width: '85%',
    marginStart: 18,
    alignItems: 'center',
    alignSelf: 'flex-start',
    marginTop: 15,
    justifyContent: 'center',
    marginBottom: 7,
  },
  errorDotStyle: {
    height: 20,
    width: 20,
    borderRadius: 50,
    marginRight: 5,
    backgroundColor: '#FF0000',
  },
});
