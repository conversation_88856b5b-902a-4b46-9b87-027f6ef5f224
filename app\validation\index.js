import * as Yup from 'yup';

const regularExpression = /^(?=.*\d)(?=.*[!@#$%^&*])(?=.*[a-z])(?=.*[A-Z]).{8,}$/;
const regexsmallet = /[a-z]/;
const regexcaps = /[A-Z]/;
const regexdigi = /[0-9]/;
const regexspeChar = /[!@#\$%\^&\*_]/;
export const validationSchema = Yup.object().shape({
  email: Yup.string().email('Enter valid email-id').required('Enter valid email-id'),
  password: Yup.string('')
    .required('Please enter password')
    .min(5, 'Please enter a valid password'),
});
export const passwordValidationSchema = Yup.object().shape({
  newpassword: Yup.string('').required('Please enter new password'),
  confirmpassword: Yup.string('')
    .required('Please enter confirm password')
    .oneOf([Yup.ref('newpassword')], 'Password not match'),
});
export const prolfileValidationSchema = Yup.object().shape({
  firstName: Yup.string('').trim().required('Please enter First name'),
  lastName: Yup.string('').trim().required('Please enter Last name'),
});
