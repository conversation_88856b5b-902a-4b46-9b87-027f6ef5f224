import { enablePromise, openDatabase, SQLiteDatabase } from 'react-native-sqlite-storage';
import { APP_ID, DATABASE_CONFIG, NO_DATA_FOUND, tables } from '_utils';
import { getData, isEmpty, isJson, storeData } from '_helpers';

enablePromise(true);

const tableName = 'todoData';
var createQueries = {
  [tables.PROJECTS_TABLE]: `CREATE TABLE IF NOT EXISTS ${tables.PROJECTS_TABLE}(id INTEGER PRIMARY KEY AUTOINCREMENT, _id TEXT, name TEXT, logo TEXT, customerID TEXT, customer_name TEXT, rating boolean, autoGenerateStudyNames boolean, allowStudyNameEdit boolean, studyExport boolean, projectStats boolean, rerateElement boolean, promptTriggerTime TEXT,assignedlocations TEXT,  canCreateElements INT, canCreateRoles INT, canCreateAreas INT)`,
  [tables.LOCATIONS_TABLE]: `CREATE TABLE IF NOT EXISTS ${tables.LOCATIONS_TABLE}(id INTEGER PRIMARY KEY AUTOINCREMENT, _id TEXT NOT NULL, projectID TEXT,  customerID TEXT , locationname TEXT, address TEXT ,contactname TEXT , telephone TEXT, postcode TEXT)`,
  [tables.AREAS_TABLE]: `CREATE TABLE IF NOT EXISTS ${tables.AREAS_TABLE}(id INTEGER PRIMARY KEY AUTOINCREMENT, name TEXT, _id TEXT, projectID TEXT,  status TEXT)`,
  [tables.TASKS_TABLE]: `CREATE TABLE IF NOT EXISTS ${tables.TASKS_TABLE}(id INTEGER PRIMARY KEY AUTOINCREMENT, name TEXT, _id TEXT, projectID TEXT, date TEXT, elements, custom_group TEXT, controllingElementID TEXT, status TEXT)`,
  [tables.ELEMENTS_TABLE]: `CREATE TABLE IF NOT EXISTS ${tables.ELEMENTS_TABLE}(id INTEGER PRIMARY KEY AUTOINCREMENT, name TEXT, _id TEXT,rating INT, projectID TEXT, category TEXT, type INT, taskID TEXT , count boolean, addPosition TEXT, unitOfMeasure TEXT,contingencyAllowance TEXT, relaxationAllowance TEXT)`,
  [tables.STUDY_TABLE]: `CREATE TABLE IF NOT EXISTS ${tables.STUDY_TABLE}(id INTEGER PRIMARY KEY AUTOINCREMENT,_id TEXT,name TEXT, projectID TEXT, projectRating INT , studyStartTime BIGINT, studyEndTime BIGINT, customerID TEXT, locationID TEXT, userID TEXT, role TEXT, synced boolean, cancelled boolean,sendStudyToEmail boolean,sendProjectStatsReport boolean,locationName TEXT, customerName TEXT)`,
  [tables.STUDY_DATA_TABLE]: `CREATE TABLE IF NOT EXISTS ${tables.STUDY_DATA_TABLE}(id INTEGER PRIMARY KEY AUTOINCREMENT, area TEXT , areaName TEXT, task TEXT, taskName TEXT,element TEXT, elementName TEXT, rating INT,frequency INT, notes TEXT, photo TEXT, photoLocal TEXT, startTime, endTime , duration, studyID INTEGER, continuesObservation INTEGER, surveyDetails TEXT,localID INTEGER,isElementCreated boolean,isAreaCreated boolean)`,
  [tables.RECOVERY_DATA_TABLE]: `CREATE TABLE IF NOT EXISTS ${tables.RECOVERY_DATA_TABLE}(id INTEGER PRIMARY KEY AUTOINCREMENT, area TEXT ,areaName TEXT, task TEXT, taskName TEXT,element TEXT, elementName TEXT, rating INT,frequency INT, notes TEXT, photo TEXT, photoLocal TEXT, startTime, endTime , duration, studyID INTEGER, continuesObservation INTEGER,localID INTEGER,isElementCreated boolean,isAreaCreated boolean)`,
  [tables.CREATE_AREA_TABLE]: `CREATE TABLE IF NOT EXISTS ${tables.CREATE_AREA_TABLE}(id INTEGER PRIMARY KEY AUTOINCREMENT, _id TEXT, name TEXT, projectID TEXT, customerID TEXT, status TEXT, syncFromHome boolean)`,
  [tables.CREATE_ELEMENT_TABLE]: `CREATE TABLE IF NOT EXISTS ${tables.CREATE_ELEMENT_TABLE}(id INTEGER PRIMARY KEY AUTOINCREMENT, _id TEXT, name TEXT, type TEXT, rating INT, count INT, category TEXT, projectID TEXT, addedby TEXT, id_of_addedby TEXT, status TEXT, date TEXT, userAdded boolean, taskID TEXT, addPosition TEXT,contingencyAllowance TEXT, relaxationAllowance TEXT, syncFromHome boolean)`,
  [tables.CATEGORIES_TABLE]: `CREATE TABLE IF NOT EXISTS ${tables.CATEGORIES_TABLE}(id INTEGER PRIMARY KEY AUTOINCREMENT, _id TEXT, name TEXT , studyType  INT , projectID TEXT)`,
  [tables.ROLES_TABLE]: `CREATE TABLE IF NOT EXISTS ${tables.ROLES_TABLE}(id INTEGER PRIMARY KEY AUTOINCREMENT, _id TEXT, name TEXT, position TEXT, projectID TEXT, status TEXT)`,
  [tables.CREATE_ROLE_TABLE]: `CREATE TABLE IF NOT EXISTS ${tables.CREATE_ROLE_TABLE}(id INTEGER PRIMARY KEY AUTOINCREMENT, _id TEXT, name TEXT, position TEXT, projectID TEXT, customerID TEXT, syncFromHome boolean)`,
  [tables.GROUPS_TABLE]: `CREATE TABLE IF NOT EXISTS ${tables.GROUPS_TABLE}(id INTEGER PRIMARY KEY AUTOINCREMENT, _id TEXT, name TEXT, projectID TEXT, status TEXT)`,
  [tables.REMINDERS_TABLE]: `CREATE TABLE IF NOT EXISTS ${tables.REMINDERS_TABLE}(id INTEGER PRIMARY KEY AUTOINCREMENT, _id TEXT, text TEXT, projectID TEXT, triggerType TEXT, triggerTime TEXT, repeat boolean, photo boolean, taskID TEXT, elementID TEXT, showOnDays TEXT,status boolean)`,
  [tables.QUESTIONS_TABLE]: `CREATE TABLE IF NOT EXISTS ${tables.QUESTIONS_TABLE}(id INTEGER PRIMARY KEY AUTOINCREMENT, _id TEXT, text TEXT, projectID TEXT, answerRequired boolean, photo boolean, taskID TEXT, elementID TEXT, triggerType TEXT, triggerTime TEXT, showOnDays TEXT,questionType TEXT,questionOptions TEXT,status boolean,freeText boolean ,numericPads TEXT )`,
  [tables.ANSWERS_TABLE]: `CREATE TABLE IF NOT EXISTS ${tables.ANSWERS_TABLE}(id INTEGER PRIMARY KEY AUTOINCREMENT, question TEXT, answer TEXT, studyID INTEGER,time TEXT,questionID INTEGER,triggerType TEXT)`,
  [tables.ERROR_TABLE]: `CREATE TABLE IF NOT EXISTS ${tables.ERROR_TABLE}(id INTEGER PRIMARY KEY AUTOINCREMENT, errorMsg TEXT, errorFrom TEXT,time TEXT)`,
};

var insertQueries = {
  [tables.PROJECTS_TABLE]: `INSERT INTO ${tables.PROJECTS_TABLE}(_id, name, logo, customerID, customer_name, rating, autoGenerateStudyNames, allowStudyNameEdit, studyExport, projectStats, rerateElement, promptTriggerTime,assignedlocations,  canCreateElements, canCreateRoles, canCreateAreas) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
  [tables.LOCATIONS_TABLE]: `INSERT INTO ${tables.LOCATIONS_TABLE}(_id , projectID ,customerID , locationname, address, contactname, telephone, postcode) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
  [tables.AREAS_TABLE]: `INSERT INTO ${tables.AREAS_TABLE}(name, _id, projectID, status) VALUES (?, ?, ?, ?)`,
  [tables.TASKS_TABLE]: `INSERT INTO ${tables.TASKS_TABLE}(name, _id, projectID, custom_group, controllingElementID,elements) VALUES ( ?, ?, ?, ?, ?, ?)`,
  [tables.ELEMENTS_TABLE]: `INSERT INTO ${tables.ELEMENTS_TABLE}(name, _id, rating, projectID, category ,type, taskID , count, unitOfMeasure) VALUES (?, ? , ?, ?, ?, ?, ?, ?, ?)`,
  [tables.STUDY_TABLE]: `INSERT INTO ${tables.STUDY_TABLE}(name , projectID , projectRating , studyStartTime , studyEndTime, customerID, locationID, userID, locationName, customerName) VALUES (?, ?, ? , ? , ? , ? , ?, ?, ?, ?)`,
  [tables.STUDY_DATA_TABLE]: `INSERT INTO ${tables.STUDY_DATA_TABLE}(area ,areaName ,task ,taskName, element , elementName  ,rating, frequency, notes, photo,photoLocal, startTime, endTime , duration, studyID, continuesObservation,localID,isElementCreated, isAreaCreated) VALUES (? , ? ,? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ?, ?, ?, ?, ?, ?, ?)`,
  [tables.RECOVERY_DATA_TABLE]: `INSERT INTO ${tables.RECOVERY_DATA_TABLE}(area ,areaName ,task ,taskName, element , elementName  ,rating, frequency, notes, photo,photoLocal, startTime, endTime , duration, studyID, continuesObservation,localID,isElementCreated, isAreaCreated) VALUES (? ,? , ? ,? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ?, ?, ?, ?, ?, ?)`,
  [tables.CREATE_AREA_TABLE]: `INSERT INTO ${tables.CREATE_AREA_TABLE}(_id ,name , projectID , customerID, syncFromHome) VALUES ( ?, ?, ?, ?, ?)`,
  [tables.CREATE_ELEMENT_TABLE]: `INSERT INTO ${tables.CREATE_ELEMENT_TABLE}(_id , name, type, rating, count, category, projectID, taskID, addPosition,contingencyAllowance, relaxationAllowance, syncFromHome)  VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
  [tables.CATEGORIES_TABLE]: `INSERT INTO ${tables.CATEGORIES_TABLE}(_id , name, studyType, projectID) VALUES (? , ? , ? , ?)`,
  [tables.ROLES_TABLE]: `INSERT INTO ${tables.ROLES_TABLE}(_id, name, position, projectID,  status) VALUES (?, ?, ?, ?, ?)`,
  [tables.CREATE_ROLE_TABLE]: `INSERT INTO ${tables.CREATE_ROLE_TABLE}(_id ,name , position, projectID , customerID , syncFromHome) VALUES (? , ? , ? , ? , ?, ?)`,
  [tables.GROUPS_TABLE]: `INSERT INTO ${tables.GROUPS_TABLE}(_id , name, projectID, status) VALUES (?, ?, ?, ?)`,
  [tables.REMINDERS_TABLE]: `INSERT INTO ${tables.REMINDERS_TABLE}(_id , text, projectID, triggerType, triggerTime, repeat, photo, taskID, elementID, showOnDays,status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?)`,
  [tables.QUESTIONS_TABLE]: `INSERT INTO ${tables.QUESTIONS_TABLE}(_id , text, projectID, answerRequired, photo, taskID, elementID, triggerType, triggerTime, showOnDays,questionType,questionOptions,status,freeText,numericPads) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?,?,?,?, ?)`,
  [tables.ANSWERS_TABLE]: `INSERT INTO ${tables.ANSWERS_TABLE}(question, answer, studyID,time,questionID,triggerType) VALUES (?, ?, ?,?, ?, ?)`,
  [tables.ERROR_TABLE]: `INSERT INTO ${tables.ERROR_TABLE}(errorMsg, errorFrom, time) VALUES (?, ?, ?)`,
};
export const getDBConnection = async () => {
  return openDatabase(DATABASE_CONFIG).then(async (db) => {
    await storeData('DATABASE_CONFIG', JSON.stringify(DATABASE_CONFIG));
    let databaseInstance = db;
    return databaseInstance;
  });
};

export const createTable = async (table, db) => {
  // create table if not exists
  const query = createQueries[table];
  await db
    .executeSql(query)
    .then(() => {
      return true;
    })
    .catch((error) => {
      return error;
    });
};
export const deleteTable = async (db) => {
  const query = `drop table ${tableName}`;

  await db.executeSql(query);
};
export const dropTable = async (table, db) => {
  let query = 'DROP TABLE IF EXISTS ' + table;
  return new Promise(async (resolve, reject) => {
    await db.executeSql(query, []).then(
      () => {
        resolve(table + ' data removed successfully.');
      },
      (err) => {
        reject(err);
      }
    );
  });
};
export const dropAllTables = async (db) => {
  return Promise.all([
    dropTable(tables.PROJECTS_TABLE, db),
    // dropTable(tables.ROLES_TABLE, db),
    dropTable(tables.ELEMENTS_TABLE, db),
    dropTable(tables.AREAS_TABLE, db),
    dropTable(tables.LOCATIONS_TABLE, db),
    dropTable(tables.CATEGORIES_TABLE, db),
    dropTable(tables.TASKS_TABLE, db),
    dropTable(tables.GROUPS_TABLE, db),
    dropTable(tables.REMINDERS_TABLE, db),
    dropTable(tables.QUESTIONS_TABLE, db),
  ]).then(() => {
    return;
  });
};
export const deleteById = async (table, id, db) => {
  let query = 'DELETE FROM ' + `${table}` + ' WHERE _id=?';
  let queryData = [id];
  return db
    .transaction((tx) => {
      tx.executeSql(query, queryData);
    })
    .then(() => {
      return;
    })
    .catch((error) => {
      console.error('error', error);
      return error;
    });
};

export const getAllStudyData = async (table, sid, db) => {
  // console.log(sid, ' getquerydataata');
  let query = 'select * FROM ' + `${table}` + ' WHERE studyID=?';
  let queryData = [sid];
  let data = [];
  return db
    .transaction((tx) => {
      tx.executeSql(query, queryData, (tx, result) => {
        console.log(result, 'ddd');
        if (!isEmpty(result.rows)) {
          data = parseRows(table, result.rows);
          return;
        }
      });
    })
    .then(() => {
      return data;
    })
    .catch((error) => {
      console.error('error', error);
      return error;
    });
};

export const getStudyData = async (table, id, sid, db) => {
  // console.log(id, sid, ' getquerydataata');
  let query = 'select * FROM ' + `${table}` + ' WHERE localID=? and studyID=?';
  let queryData = [id, sid];
  let data = [];
  return db
    .transaction((tx) => {
      tx.executeSql(query, queryData, (tx, result) => {
        console.log(result, 'ddd');
        if (!isEmpty(result.rows)) {
          data = parseRows(table, result.rows);
          return;
        }
      });
    })
    .then(() => {
      return data;
    })
    .catch((error) => {
      console.error('error', error);
      return error;
    });
};

export const getStudyTable = async (table, db) => {
  // console.log(id, sid, ' getquerydataata');
  let query = 'select * FROM ' + `${table}` + ' where syncFromHome is null';
  let queryData = [];
  let data = [];
  return db
    .transaction((tx) => {
      tx.executeSql(query, queryData, (tx, result) => {
        if (!isEmpty(result.rows)) {
          data = parseRows(table, result.rows);
          return;
        }
      });
    })
    .then(() => {
      return data;
    })
    .catch((error) => {
      console.error('error', error);
      return error;
    });
};

export const updateCreationTable = async (table, name, id, db) => {
  // console.log(id, sid, ' getquerydataata');
  let query = 'update ' + `${table}` + ' SET syncFromHome=true WHERE name=? and projectID=?';
  let queryData = [name, id];
  let data = [];
  return db
    .transaction((tx) => {
      tx.executeSql(query, queryData, (tx, result) => {
        if (!isEmpty(result.rows)) {
          data = parseRows(table, result.rows);
          return;
        }
      });
    })
    .then(() => {
      return data;
    })
    .catch((error) => {
      console.error('error', error);
      return error;
    });
};

export const updateStudyData = async (table, photo, photoLocal, id, sid, db) => {
  // console.log(id, sid, ' getquerydataata');
  let query = 'update ' + `${table}` + ' SET photo=?, photoLocal=? WHERE localID=? and studyID=?';
  let queryData = [photo, photoLocal, id, sid];
  let data = [];
  return db
    .transaction((tx) => {
      tx.executeSql(query, queryData, (tx, result) => {
        if (!isEmpty(result.rows)) {
          data = parseRows(table, result.rows);
          return;
        }
      });
    })
    .then(() => {
      return data;
    })
    .catch((error) => {
      console.error('error', error);
      return error;
    });
};

export const insertIntoTable = async (table, data, db) => {
  return new Promise((resolve, reject) => {
    if (isEmpty(data) || !data.length) {
      resolve(NO_DATA_FOUND);
      return;
    }
    db.transaction((tx) => {
      for (let i = 0; i < data.length; i++) {
        const rowData = prepareDataRow(table, data[i]);
        tx.executeSql(insertQueries[table], rowData);
        resolve(true);
        (err) => {
          console.error('Error AT TABLE: ' + table + ' ' + err);
          reject('');
          return [];
        };
      }
    });
  }).catch((err) => {
    console.error('Error AT TABLE: ' + table + ' ' + err);
    return [];
  });
};
/* GETTING ALL RECORDS FROM TABLE */
export const getAllData = async (table, db) => {
  let query = 'SELECT * FROM ' + `${table}`;
  if (table == tables.PROJECTS_TABLE) {
    query += ` ORDER BY CASE
        WHEN substr(name, 1, 1) GLOB '[0-9]' THEN substr(name, instr(name, ' ') + 1)
        ELSE name  END COLLATE NOCASE`;
  }
  let data = [];
  return db
    .transaction((tx) => {
      tx.executeSql(query, [], (tx, result) => {
        if (!isEmpty(result.rows)) {
          data = parseRows(table, result.rows);
          return;
        }
      });
    })
    .then(() => {
      return data;
    })
    .catch((err) => {
      return [];
    });
};
export const parseRows = (table, rows) => {
  let data = [];

  for (let i = 0; i < rows.length; i++) {
    let item = rows.item(i);
    if (table === tables.PROJECTS_TABLE) {
    }
    if (table === tables.STUDY_DATA_TABLE) {
      item.photo = isJson(item.photo) ? JSON.parse(item.photo) : item.photo;
    }
    if (table === tables.STUDY_DATA_TABLE) {
      item.photoLocal = isJson(item.photoLocal) ? JSON.parse(item.photoLocal) : item.photoLocal;
    }
    if (table === tables.RECOVERY_DATA_TABLE) {
      item.photo = isJson(item.photo) ? JSON.parse(item.photo) : item.photo;
    }
    if (table === tables.RECOVERY_DATA_TABLE) {
      item.photoLocal = isJson(item.photoLocal) ? JSON.parse(item.photoLocal) : item.photoLocal;
    }
    if (table === tables.TASKS_TABLE) {
      // item.elements = JSON.parse(item.elements);
      item.group = item.custom_group;
    }
    if (table === tables.ELEMENTS_TABLE) {
      item.grouped_data = isJson(item.grouped_data)
        ? JSON.parse(item.grouped_data)
        : item.grouped_data;
      item.elementsPosition = isJson(item.elementsPosition)
        ? JSON.parse(item.elementsPosition)
        : item.elementsPosition;
    }

    if (table === tables.REMINDERS_TABLE || table === tables.QUESTIONS_TABLE) {
      if (item.triggerTime) {
        item.triggerTime = isJson(item.triggerTime)
          ? JSON.parse(item.triggerTime)
          : item.triggerTime;
      }
      if (item.questionOptions) {
        item.questionOptions = isJson(item.questionOptions)
          ? JSON.parse(item.questionOptions)
          : item.questionOptions;
      }
      if (item.showOnDays) {
        item.showOnDays = isJson(item.showOnDays) ? JSON.parse(item.showOnDays) : item.showOnDays;
      }
      if (item.taskID) {
        item.taskID = isJson(item.taskID) ? JSON.parse(item.taskID) : item.taskID;
      }
      if (item.elementID) {
        item.elementID = isJson(item.elementID) ? JSON.parse(item.elementID) : item.elementID;
      }
    }
    data.push(item);
  }
  return data;
};

export const prepareDataRow = (table, data) => {
  let _data = [];

  if (table === tables.PROJECTS_TABLE) {
    _data = [
      data._id,
      data.name,
      data.logo || '',
      data.customerID,
      data.customerName,
      data.rating,
      data.autoGenerateStudyNames,
      data.allowStudyNameEdit,
      data.studyExport,
      data.projectStats,
      data.rerateElement,
      JSON.stringify(data.promptTriggerTime),
      data.assignedLocations.length, // assigned locations to current user
      data.fieldUser.elementCreationDOTS ? 1 : 0,
      false,
      data.fieldUser.areaCreationDOTS ? 1 : 0,
    ];
  } else if (table === tables.LOCATIONS_TABLE) {
    _data = [
      data._id,
      data.projectID,
      data.customerID,
      data.name,
      data.address,

      data.contactName,
      data.telephone,
      data.postcode,
    ];
  } else if (table === tables.AREAS_TABLE) {
    _data = [data.name, data._id, data.projectID, data.status];
  } else if (table === tables.ELEMENTS_TABLE) {
    _data = [
      data.name,
      data._id,
      data.rating,
      data.projectID,
      data.categoryID,
      data.type,
      data.taskID ?? '',
      data.count ?? false,
      data.unitOfMeasure ?? '',
    ];
  } else if (table === tables.TASKS_TABLE) {
    _data = [
      data.name,
      data._id,
      data.projectID,
      data.groupID ?? '',
      data.controllingElementID,
      JSON.stringify(data.elements),
    ];
  } else if (table === tables.CREATE_AREA_TABLE) {
    _data = [data._id, data.name, data.projectID, data.customerID, data.syncFromHome];
  } else if (table === tables.CREATE_ELEMENT_TABLE) {
    data.count = data.count ? 1 : 0;
    _data = [
      data._id,
      data.name,
      data.type,
      data.rating,
      data.count,
      data.category,
      data.projectID,
      data.taskID,
      data.addPosition,
      data.contingencyAllowance,
      data.relaxationAllowance,
      data.syncFromHome,
    ];
  } else if (table === 'Create_Task') throw new Error("Shouldn't exist!");
  else if (table === tables.STUDY_TABLE) {
    //   let data = parser.getStudyData();
    _data = [
      data.name,
      data.projectID,
      data.projectRating,
      data.studyStartTime,
      data.StudyEndTime,
      data.customerID,
      data.locationID,
      data.userId,
      data.locationName,
      data.customerName,
      // getData("userID")
    ];

    if (APP_ID == 2 && !isEmpty(data.role)) {
      data.role._id;
      data.role.name;
    }
  } else if (table === tables.STUDY_DATA_TABLE)
    _data = [
      data.area,
      data.areaName,
      data.task,
      data.taskName,
      data.element,
      data.elementName,
      data.rating,
      data.frequency,
      data.notes,
      !isEmpty(data.photo) && typeof data.photo === 'string'
        ? data.photo
        : !isEmpty(data.photo) && typeof data.photo !== 'string'
          ? JSON.stringify(data.photo)
          : '',
      !isEmpty(data.photoLocal) && typeof data.photoLocal === 'string'
        ? data.photoLocal
        : !isEmpty(data.photoLocal) && typeof data.photoLocal !== 'string'
          ? JSON.stringify(data.photoLocal)
          : '',
      data.startTime,
      data.endTime,
      data.duration,
      data.studyID,
      data.continuesObservation,
      data.localID,
      data.isElementCreated,
      data.isAreaCreated,
    ];
  else if (table === tables.RECOVERY_DATA_TABLE)
    _data = [
      data.area,
      data.areaName,
      data.task,
      data.taskName,
      data.element,
      data.elementName,
      data.rating,
      data.frequency,
      data.notes,
      !isEmpty(data.photo) && typeof data.photo === 'string'
        ? data.photo
        : !isEmpty(data.photo) && typeof data.photo !== 'string'
          ? JSON.stringify(data.photo)
          : '',
      !isEmpty(data.photoLocal) && typeof data.photoLocal === 'string'
        ? data.photoLocal
        : !isEmpty(data.photoLocal) && typeof data.photoLocal !== 'string'
          ? JSON.stringify(data.photoLocal)
          : '',
      data.startTime,
      data.endTime,
      data.duration,
      data.studyID,
      data.continuesObservation,
      data.localID,
      data.isElementCreated,
      data.isAreaCreated,
    ];
  else if (table === tables.CATEGORIES_TABLE) {
    _data = [data._id, data.name, data.studyType, data.projectID];
  } else if (table === tables.ROLES_TABLE) {
    _data = [data._id, data.name, data.position, data.projectID, data.status == 'active'];
  } else if (table === tables.CREATE_ROLE_TABLE) {
    _data = [
      data._id,
      data.name,
      data.position,
      data.projectID,
      data.customerID,
      data.syncFromHome,
    ];
  } else if (table === tables.GROUPS_TABLE) {
    _data = [data._id, data.name, data.projectID, data.status];
  } else if (table === tables.REMINDERS_TABLE) {
    _data = [
      data._id,
      data.name,
      data.projectID,
      data.triggerType,
      JSON.stringify(data.triggerTime),
      data.repeat,
      data.takePhoto,
      JSON.stringify(data.taskID),
      JSON.stringify(data.elements) ?? '',
      JSON.stringify(data.showOnDays),
      data.status == 'active',
    ];
  } else if (table === tables.QUESTIONS_TABLE) {
    _data = [
      data._id,
      data.name,
      data.projectID,
      data.answerRequired,
      data.takePhoto,
      JSON.stringify(data.taskID) ?? '',
      JSON.stringify(data.elements) ?? '',
      data.triggerType,
      JSON.stringify(data.triggerTime),
      JSON.stringify(data.showOnDays),
      data.questionType,
      JSON.stringify(data.questionOptions),
      data.status == 'active',
      data.isFreeTextBox,
      data.numericPads,
    ];
  } else if (table === tables.ANSWERS_TABLE) {
    _data = [
      data.question,
      data.answer,
      data.studyID,
      data.time,
      data.questionID,
      data.triggerType,
    ];
  } else if (table === tables.ERROR_TABLE) {
    _data = [data.errorMsg, data.errorFrom, data.time];
  }

  return _data;
};

export const getDataByTableAndID = (table, id, db) => {
  let query = '';

  const defaultQuery = `SELECT * FROM ${table} WHERE projectID=?`;
  const limitQuery = `SELECT * FROM ${table} WHERE _id=? LIMIT 1`;

  switch (table) {
    case tables.ELEMENTS_TABLE:
    case tables.CREATE_AREA_TABLE:
    case tables.CREATE_ELEMENT_TABLE:
    case tables.CREATE_ROLE_TABLE:
      query = defaultQuery;
      break;

    case tables.STUDY_TABLE:
      query = `SELECT * FROM ${table} WHERE id=?`;
      break;

    case tables.STUDY_DATA_TABLE:
    case tables.RECOVERY_DATA_TABLE:
      query = `
        SELECT id, areaName, taskName, elementName, rating, frequency, notes, photo,photoLocal, 
        startTime, endTime, duration, continuesObservation, localID, isElementCreated, 
        isAreaCreated, studyID AS studyNo, element AS elementID, area AS areaID, task AS taskID 
        FROM ${table} WHERE studyID=?
      `;
      break;

    case 'OfflineElement':
      table = id.includes('element') ? tables.CREATE_ELEMENT_TABLE : tables.ELEMENTS_TABLE;
      query = `SELECT * FROM ${table} WHERE _id=? LIMIT 1`;
      break;

    case 'OfflineArea':
      table = id.includes('area') ? tables.CREATE_AREA_TABLE : tables.AREAS_TABLE;
      query = `SELECT * FROM ${table} WHERE _id=? LIMIT 1`;
      break;

    case 'OfflineRole':
      table = id.includes('role') ? tables.CREATE_ROLE_TABLE : tables.ROLES_TABLE;
      query = `SELECT * FROM ${table} WHERE _id=? LIMIT 1`;
      break;

    case tables.PROJECTS_TABLE:
      query = `SELECT * FROM ${table} WHERE _id=?`;
      break;

    case tables.GROUPS_TABLE:
      query = defaultQuery;
      break;

    case tables.ANSWERS_TABLE:
      query = `SELECT *, studyID AS studyNo FROM ${table} WHERE studyID=?`;
      break;

    case tables.ROLES_TABLE:
    case tables.AREAS_TABLE:
    case tables.CATEGORIES_TABLE:
      query = `${defaultQuery} ORDER BY name ASC`;
      break;

    default:
      query = defaultQuery;
      break;
  }
  let data = [];

  return new Promise((resolve, reject) => {
    db.transaction(
      (tx) => {
        tx.executeSql(
          query,
          [id],
          (tx, result) => {
            data = parseRows(table, result.rows); // Parse rows from result
          },
          (tx, error) => {
            console.error('SQL error:', error);
            reject(error); // Reject promise on error
          }
        );
      },
      (transactionError) => {
        console.error('Transaction error:', transactionError);
        reject(transactionError); // Reject promise if transaction fails
      },
      () => {
        resolve(data); // Resolve promise with data if successful
      }
    );
  }).catch((err) => {
    console.error('Final catch error:', err);
    return []; // Return an empty array if anything fails
  });
};

export const getDataByTableAndName = (table, name, projectID, db) => {
  let query = '';
  let queryParams = [];
  if (
    table === tables.ELEMENTS_TABLE ||
    table === tables.AREAS_TABLE ||
    table === tables.ROLES_TABLE
  ) {
    query = `SELECT * FROM ${table} WHERE name = ? AND projectID = ?`;
    queryParams = [name, projectID]; // Use query parameters to prevent SQL injection
  } else {
    query = `SELECT * FROM ${table} WHERE projectID = ?`;
    queryParams = [projectID];
  }
  let data = [];
  return new Promise((resolve, reject) => {
    db.transaction(
      (tx) => {
        tx.executeSql(
          query,
          queryParams,
          (tx, result) => {
            data = parseRows(table, result.rows); // Parse rows from result
          },
          (tx, error) => {
            console.error('SQL error:', error);
            reject(error); // Reject promise on error
          }
        );
      },
      (transactionError) => {
        console.error('Transaction error:', transactionError);
        reject(transactionError); // Reject promise if transaction fails
      },
      () => {
        resolve(data); // Resolve promise with data if successful
      }
    );
  }).catch((err) => {
    console.error('Final catch error:', err);
    return []; // Return an empty array if anything fails
  });
};

export const getStudies = async (db, userID, nonSyncedOnly = true, showAll = false) => {
  let query = `SELECT * FROM ${tables.STUDY_TABLE} WHERE cancelled IS NULL`;

  // show only synced, only unsynced, or no limit (reveal all)
  if (!showAll) {
    query += ' AND synced' + (nonSyncedOnly ? ' IS NULL' : '=1');
  }
  query += ` AND userID='${userID}'`;
  let data = [];
  return db
    .transaction((tx) => {
      tx.executeSql(query, [], (tx, result) => {
        if (!isEmpty(result.rows)) {
          data = parseRows(tables.STUDY_TABLE, result.rows);
        }
        return;
      });
    })
    .then(() => {
      return data;
    })
    .catch((error) => {
      return;
    });
};
export const getRecoverStudies = async (db, userID) => {
  let query = `SELECT * FROM ${tables.RECOVERY_DATA_TABLE} `;
  let data = [];
  return db
    .transaction((tx) => {
      tx.executeSql(query, [], (tx, result) => {
        if (!isEmpty(result.rows)) {
          data = parseRows(tables.RECOVERY_DATA_TABLE, result.rows);
        }
        return;
      });
    })
    .then(() => {
      return data;
    })
    .catch((error) => {
      return;
    });
};
export const getStudiesBYProjectID = async (key, projectID, db) => {
  let query;
  if (key == 'role') {
    query = `select id AS studyNo,role,roleName,isRoleCreated,projectID from ${tables.STUDY_TABLE} where  projectID = ? AND synced = 1 AND isRoleCreated = 1 `;
  } else if (key == 'area') {
    query = `select area,areaName,studyID AS studyNo , isAreaCreated, projectID from ${tables.STUDY_DATA_TABLE}  inner join ${tables.STUDY_TABLE} on ${tables.STUDY_TABLE}.id = ${tables.STUDY_DATA_TABLE}.studyID  where  projectID = ? AND synced = 1  AND  isAreaCreated = 1`;
  } else if (key == 'element') {
    query = `select element,elementName,studyID AS studyNo, isElementCreated, projectID from ${tables.STUDY_DATA_TABLE}  inner join ${tables.STUDY_TABLE} on ${tables.STUDY_TABLE}.id = ${tables.STUDY_DATA_TABLE}.studyID  where projectID = ? AND synced = 1 AND isElementCreated = 1 `;
  }

  let data = [];
  return db
    .transaction((tx) => {
      tx.executeSql(query, [projectID], (tx, result) => {
        data = parseRows(tables.STUDY_DATA_TABLE, result.rows);
        return;
      });
    })
    .then(() => {
      return data;
    })
    .catch((error) => {
      return;
    });
};
export const getStudiesBYSyncID = async (db) => {
  let query = `SELECT * FROM ${tables.STUDY_TABLE} WHERE cancelled IS NULL AND synced IS NULL AND  _id IS NOT NULL`;

  let data = [];
  return db
    .transaction((tx) => {
      tx.executeSql(query, [], (tx, result) => {
        if (!isEmpty(result.rows)) {
          data = parseRows(tables.STUDY_TABLE, result.rows);
        }
        return;
      });
    })
    .then(() => {
      return data;
    })
    .catch((error) => {
      return;
    });
};

export const markStudyAsSynced = (studyID, database) => {
  let queryData = [studyID];
  return database
    .transaction((tx) => {
      tx.executeSql(`UPDATE ${tables.STUDY_TABLE} SET synced=1  WHERE _id=?`, queryData);
    })
    .then((result) => {
      return result;
    })
    .catch((error) => {
      return error;
    });
};
export const markStudyAsCancelled = async (studyID, database) => {
  let queryData = [studyID];
  return database
    .transaction((tx) => {
      tx.executeSql(`UPDATE ${tables.STUDY_TABLE} SET cancelled=1  WHERE id=?`, queryData).then(
        (result) => {
          return result;
        }
      );
    })
    .catch((error) => {
      return error;
    });
};

export const runSqlQuery = async (db, query, queryData) => {
  if (!query) return;

  return db.transaction((tx) => {
    tx.executeSql(query, queryData || [])
      .then((result) => {
        return result;
      })
      .catch((error) => {
        return [];
      });
  });
};

export const getElements = async (taskID, projectID, db) => {
  let query = `SELECT * FROM ${tables.ELEMENTS_TABLE} WHERE taskID=? AND projectID=?`;
  let data = [];

  return new Promise((resolve, reject) => {
    let data = [];
    db.transaction((tx) => {
      tx.executeSql(query, [taskID, projectID], (tx, result) => {
        data = parseRows(tables.ELEMENTS_TABLE, result.rows);
        resolve(data);
      });
      (err) => {
        reject(err);
      };
    }).then(
      (data) => {
        return data;
      },
      (err) => {
        return [];
      }
    );
  });
};

export const searchElementByName = async (elementName, projectID, db) => {
  let query = `SELECT * FROM ${tables.ELEMENTS_TABLE} WHERE projectID='${projectID}' AND name LIKE '${elementName}'`;
  return new Promise((resolve, reject) => {
    let data = [];
    db.transaction((tx) => {
      tx.executeSql(query, [], (tx, result) => {
        data = parseRows(tables.ELEMENTS_TABLE, result.rows);
        resolve(data);
      });
      (err) => {
        reject(err);
      };
    }).then(
      (data) => {
        return data;
      },
      (err) => {
        return [];
      }
    );
  });
};

export const getElementsGroupByTask = (projectID, db, taskID) => {
  const query = `
    SELECT elementsPosition, taskID, '[' || GROUP_CONCAT(json_data) || ']' AS grouped_data
    FROM (
      SELECT 
        t.projectID AS projectID,  
        t._id AS taskID, 
        t.elements AS elementsPosition,
        '{
          "_id":"' || e._id || '",
          "projectID":"' || e.projectID || '",
          "taskID":"' || t._id || '",
          "unitOfMeasure":"' || e.unitOfMeasure || '",
          "category":"' || e.category || '",
          "count":' || e.count || ',
          "id":' || e.id || ',
          "name":"' || e.name || '",
          "rating":' || e.rating || ',
          "type":' || e.type || ',
          "addPosition":"' || COALESCE(e.addPosition, '') || '"
        }' AS json_data  
      FROM ${tables.ELEMENTS_TABLE} e
      JOIN ${tables.TASKS_TABLE} t
        ON t.elements LIKE '%"' || e._id || '"%' 
      WHERE t.projectID = ? AND e.projectID = ?
    ) 
    GROUP BY taskID;
  `;

  return new Promise((resolve, reject) => {
    db.transaction((tx) => {
      tx.executeSql(
        query,
        [projectID, projectID],
        (tx, result) => {
          const data = parseRows(tables.ELEMENTS_TABLE, result.rows);
          resolve(data);
        },
        (tx, error) => {
          console.error('Error executing SQL query:', error);
          reject(error);
        }
      );
    });
  });
};

export const getTasks = async (taskID, projectID, db) => {
  let query = `SELECT * FROM ${tables.TASKS_TABLE} WHERE _id=? AND projectID=?`;
  let data = [];

  return new Promise((resolve, reject) => {
    let data = [];
    db.transaction((tx) => {
      tx.executeSql(query, [taskID, projectID], (tx, result) => {
        data = parseRows(tables.TASKS_TABLE, result.rows);
        resolve(data);
      });
      (err) => {
        reject(err);
      };
    });
  }).then(
    (data) => {
      return data;
    },
    (err) => {
      return [];
    }
  );
};
export const getTaskByID = async (taskID, projectID = null, db) => {
  let query;
  let params = [taskID];
  let data = [];

  if (projectID) {
    query = `SELECT * FROM ${tables.TASKS_TABLE} WHERE _id=? and projectID=?`;
    params.push(projectID);
  } else {
    query = `SELECT * FROM ${tables.TASKS_TABLE} WHERE _id=? limit 1`;
  }

  return db
    .transaction((tx) => {
      tx.executeSql(query, params, (tx, result) => {
        data = parseRows(tables.TASKS_TABLE, result.rows);
        return;
      });
    })
    .then(() => {
      return data;
    })
    .catch((error) => {
      return [];
    });
};
export const getLocationByID = async (locationId, projectID, db) => {
  let query;
  let params = [locationId, projectID];
  query = `SELECT * FROM ${tables.LOCATIONS_TABLE} WHERE _id=? and projectID=?`;
  let data = [];

  return db
    .transaction((tx) => {
      tx.executeSql(query, params, (tx, result) => {
        data = parseRows(tables.LOCATIONS_TABLE, result.rows);
        return;
      });
    })
    .then(() => {
      return data;
    })
    .catch((error) => {
      return [];
    });
};
export const updateTable = async (table, column, data, db) => {
  let query = null;
  let query_data = null;
  if (table === tables.ELEMENTS_TABLE) {
    if (data.offline) {
      query = 'UPDATE ' + `${table}` + ' SET _id=? , name=?  WHERE _id=?';
      query_data = [data._id, data.name, data.offline];
    }
  } else if (
    table === tables.AREAS_TABLE ||
    table === tables.ROLES_TABLE ||
    table === tables.TASKS_TABLE
  ) {
    if (data.offline) {
      query = 'UPDATE ' + `${table}` + ' SET _id=? , name=? WHERE _id=?';
      query_data = [data._id, data.name, data.offline];
    }
  } else if (table === tables.STUDY_TABLE) {
    if (data.role) {
      query = 'UPDATE Study SET role=? WHERE id=?';
      query_data = [data.role, data.studyID];
    } else if (data.studyEndTime) {
      query = 'UPDATE Study SET studyEndTime=? WHERE id=?';
      query_data = [data.studyEndTime, data.studyID];
    } else if (data.studyName) {
      query = 'UPDATE ' + `${table}` + ' SET name=?  WHERE id=?';
      query_data = [data.studyName, data.studyID];
    } else if (data.sendProjectStatsReport || data.sendStudyToEmail) {
      query =
        'UPDATE ' + `${table}` + ' SET sendProjectStatsReport=?, sendStudyToEmail=? WHERE id=?';
      query_data = [data.sendProjectStatsReport, data.sendStudyToEmail, data.studyID];
    } else if (data._id) {
      query = 'UPDATE ' + `${table}` + ' SET _id = ? WHERE id=?';
      query_data = [data._id, data.studyID];
    }
  } else if (table === tables.STUDY_DATA_TABLE) {
    /* UPDATING OFFLINE DATA FROM OBSERVATION SUMMARY PAGE */
    if (data.area && data.task && data.element) {
      query =
        'UPDATE ' +
        `${table}` +
        ` SET area=? ,areaName=?, task=? ,taskName=?, element=? ,elementName=?, rating=?, frequency=?,notes=?,photo=?,photoLocal=? WHERE id IN  (${data.id})`;

      query_data = [
        data.area,
        data.areaName,
        data.task,
        data.taskName,
        data.element,
        data.elementName,
        data.rating,
        data.frequency,
        data.notes,
        data.photo,
        data.photoLocal,
      ];
    } else if (data.photoUpdate && data.id) {
      query = 'UPDATE ' + `${table}` + ' SET photo=?  WHERE id=?';
      query_data = [data.photoUpdate, data.id];
    } else if (data.photoUpdateLocal && data.id) {
      query = 'UPDATE ' + `${table}` + ' SET photoLocal=?  WHERE id=?';
      query_data = [data.photoUpdateLocal, data.id];
    } else {
      /* UPDATING OFFLINE DATA ON SYNCING */
      if (data.photo) query_data = [data.photo, data.path];
      else query_data = [data.offline, data._id];
      query = 'UPDATE ' + `${table}` + ' SET ' + `${column}` + '= replace(' + `${column}, ?,  ?)`;
    }
  } else if (table === tables.RECOVERY_DATA_TABLE) {
    /* UPDATING OFFLINE DATA FROM OBSERVATION SUMMARY PAGE */
    if (data.area && data.task && data.element) {
      query =
        'UPDATE ' +
        `${table}` +
        ` SET area=? ,areaName=?, task=? ,taskName=?, element=? ,elementName=?, rating=?, frequency=?,notes=?,photo=?,photoLocal=? WHERE id IN  (${data.id})`;

      query_data = [
        data.area,
        data.areaName,
        data.task,
        data.taskName,
        data.element,
        data.elementName,
        data.rating,
        data.frequency,
        data.notes,
        data.photo,
        data.photoLocal,
      ];
    } else if (data.photoUpdate && data.id) {
      query = 'UPDATE ' + `${table}` + ' SET photo=?  WHERE id=?';
      query_data = [data.photoUpdate, data.id];
    } else if (data.photoUpdateLocal && data.id) {
      query = 'UPDATE ' + `${table}` + ' SET photoLocal=?  WHERE id=?';
      query_data = [data.photoUpdateLocal, data.id];
    } else {
      /* UPDATING OFFLINE DATA ON SYNCING */
      if (data.photo) query_data = [data.photo, data.path];
      else query_data = [data.offline, data._id];
      query = 'UPDATE ' + `${table}` + ' SET ' + `${column}` + '= replace(' + `${column}, ?,  ?)`;
    }
  }
  return db.transaction((tx) => {
    tx.executeSql(query, query_data)
      .then((result) => {
        return;
      })
      .catch((error) => {
        console.error('markStudyAsSynced', error);
        return;
      });
  });
};

export const deleteRecord = (table, data, db) => {
  let query = '';
  let queryData = null;

  if (table === tables.STUDY_DATA_TABLE) {
    /* IF REMOVING USING ID KEY */
    if (data.id) {
      query = `DELETE FROM ${table} WHERE id IN (${data.id})`;
      queryData = '';
    } else if (data.studyID) {
      /* IF REMOVING USING studyID KEY */
      query = 'DELETE FROM ' + `${table}` + ' WHERE studyID=?';
      queryData = [data.studyID];
    }
  } else if (table === tables.STUDY_TABLE) {
    query = `DELETE FROM ${table} WHERE id IN (${data.id})`;
    queryData = '';
  } else if (table === tables.RECOVERY_DATA_TABLE) {
    /* IF REMOVING USING ID KEY */
    if (data.id) {
      query = `DELETE FROM ${table} WHERE id IN (${data.id})`;
      queryData = '';
    } else if (data.studyID) {
      /* IF REMOVING USING studyID KEY */
      query = `DELETE FROM ${table} WHERE studyID IN (${data.studyID})`;
      // queryData = data.studyID;
    }
  } else if (
    table === tables.AREAS_TABLE ||
    table === tables.TASKS_TABLE ||
    table === tables.ROLES_TABLE ||
    table === tables.LOCATIONS_TABLE ||
    table === tables.GROUPS_TABLE ||
    table === tables.ELEMENTS_TABLE
  ) {
    query = 'DELETE FROM ' + `${table}` + ' WHERE projectID=?';
    queryData = [data.projectID];
  } else if (table === tables.ERROR_TABLE) {
    query = 'DELETE FROM ' + `${table}` + ' ';
    queryData = '';
  }
  return db
    .transaction((tx) => {
      tx.executeSql(query, queryData);
    })
    .then(() => {
      return;
    })
    .catch((error) => {
      console.error('error', error);
      return error;
    });
};

export const updateTaskElementsByID = async (id, elementData, db) => {
  let query = 'UPDATE ' + `${tables.TASKS_TABLE}` + ' SET elements=?  WHERE _id=?';
  let queryData = [JSON.stringify(elementData), id];
  let data = [];
  return db
    .transaction((tx) => {
      tx.executeSql(query, queryData);
    })
    .then(() => {
      return;
    })
    .catch((error) => {
      return;
    });
};

export const updateOfflineElementInTasks = (oldElementID, newElementID, taskID, db) => {
  return new Promise((resolve, reject) => {
    getTaskByID(taskID, '', db).then(([task]) => {
      if (!task) {
        return console.error("Couldn't find task", taskID);
      }

      task.elements.forEach((elementID, index) => {
        if (elementID == oldElementID) {
          task.elements[index] = newElementID;
        }
      });

      updateTaskElementsByID(taskID, task.elements, db)
        .then((res) => resolve(res))
        .catch((error) => reject(error));
    });
  });
};

export const updateOfflineRoleInStudies = (oldRoleID, newRoleID, db) => {
  let query = `UPDATE ${tables.STUDY_TABLE} SET role=?  WHERE role=?`;
  return db
    .transaction((tx) => {
      tx.executeSql(query, [newRoleID, oldRoleID], (tx, result) => {
        return;
      });
    })
    .then(() => {
      return;
    })
    .catch((error) => {
      return;
    });
};

export const getPhotos = (db) => {
  let query = `select photo, name,customerName,customerID,projectID from ${tables.STUDY_DATA_TABLE}
    inner join ${tables.STUDY_TABLE} on ${tables.STUDY_TABLE}.id = ${tables.STUDY_DATA_TABLE}.studyID
    where photo LIKE '%file://%' `;
  // let query = `SELECT * FROM ${tables.STUDY_DATA_TABLE} WHERE photo LIKE '%file://%'`;
  let data = [];
  return db
    .transaction((tx) => {
      tx.executeSql(query, [], (tx, result) => {
        data = parseRows(tables.STUDY_DATA_TABLE, result.rows);
        return;
      });
    })
    .then(() => {
      return data;
    })
    .catch((error) => {
      return [];
    });
};

export const getReoverDataByTableAndID = (table, id, db) => {
  let query = '';
  query = 'SELECT * FROM ' + `${table}`;
  let data = [];
  return db
    .transaction((tx) => {
      return tx.executeSql(query, '', (tx, result) => {
        data = parseRows(table, result.rows);
        return;
      });
    })
    .then(() => {
      return data;
    })
    .catch((err) => {
      console.log(err, 'errrr');
      return [];
    });
};

//fetching column name from table
export const mergeColumnName = async (db) => {
  // Iterate through createQueries and alter existing tables
  for (const tableName in createQueries) {
    if (createQueries.hasOwnProperty(tableName)) {
      const sqlStatement = createQueries[tableName];
      const columnNamesInQuery = extractColumnNames(sqlStatement);

      db.transaction((tx) => {
        // Fetch existing columns and their types
        tx.executeSql(
          `PRAGMA table_info(${tableName});`,
          [],
          (tx, results) => {
            if (results.rows.length === 0) {
              console.error(`Table ${tableName} does not exist.`);
              return; // Skip processing if the table doesn't exist
            }
            const existingColumns = {};
            for (let i = 0; i < results.rows.length; i++) {
              const row = results.rows.item(i);
              existingColumns[row.name] = row.type; // Store column name and type
            }

            // Identify new columns to add or columns to modify (if type needs to change)
            const columnsToAddOrModify = columnNamesInQuery.filter((columnName) => {
              const columnTypeInQuery = getColumnDataType(sqlStatement, columnName);
              return (
                !existingColumns.hasOwnProperty(columnName) || // If column doesn't exist
                existingColumns[columnName] !== columnTypeInQuery // If column exists but type is different
              );
            });

            if (columnsToAddOrModify.length > 0) {
              columnsToAddOrModify.forEach((columnName) => {
                const columnTypeInQuery = getColumnDataType(sqlStatement, columnName);

                if (existingColumns.hasOwnProperty(columnName)) {
                  // Column exists but has a different type, so we recreate the table
                  recreateTableWithModifiedColumn(db, tableName, columnName, columnTypeInQuery);
                } else {
                  // Add new column if it doesn't exist
                  tx.executeSql(
                    `ALTER TABLE ${tableName} ADD COLUMN ${columnName} ${columnTypeInQuery};`,
                    [],
                    () => {
                      console.log(`Added ${columnName} column to ${tableName}`);
                    },
                    (error) => {
                      console.error(`Error adding ${columnName} column to ${tableName}:`, error);
                    }
                  );
                }
              });
            }
          },
          (error) => {
            console.error(`Error retrieving columns for ${tableName}:`, error);
          }
        );
      });
    }
  }
};

// Helper function to get column data type from SQL statement
export const getColumnDataType = (sqlStatement, columnName) => {
  const columnDefinitionRegex = new RegExp(`${columnName}\\s+([A-Z]+)`, 'i');
  const match = sqlStatement.match(columnDefinitionRegex);
  return match ? match[1] : null;
};

// Helper function to extract column names from SQL statement
export const extractColumnNames = (sqlStatement) => {
  const startIndex = sqlStatement.indexOf('(');
  const endIndex = sqlStatement.lastIndexOf(')');

  if (startIndex !== -1 && endIndex !== -1) {
    const columnList = sqlStatement.substring(startIndex + 1, endIndex);
    const columnNames = columnList.split(',').map((column) => column.trim().split(' ')[0]);
    return columnNames;
  } else {
    console.error('Invalid SQL statement:', sqlStatement);
    return [];
  }
};

// Function to recreate a table with a modified column
const recreateTableWithModifiedColumn = (db, tableName, columnName, newColumnType) => {
  db.transaction((tx) => {
    const tempTableName = `${tableName}_backup`;

    // Step 1: Rename the old table
    tx.executeSql(`ALTER TABLE ${tableName} RENAME TO ${tempTableName};`, [], () => {
      console.log(`Renamed ${tableName} to ${tempTableName}`);

      // Step 2: Recreate the table with the new column type
      const createTableQuery = createQueries[tableName].replace(
        new RegExp(`${columnName} .*?[,]`, 'g'),
        `${columnName} ${newColumnType},`
      );
      tx.executeSql(createTableQuery, [], () => {
        console.log(`Created new table ${tableName} with updated column ${columnName}`);

        // Step 3: Copy data from the old table to the new one
        tx.executeSql(
          `INSERT INTO ${tableName} SELECT * FROM ${tempTableName};`,
          [],
          () => {
            console.log(`Copied data from ${tempTableName} to ${tableName}`);

            // Step 4: Drop the old table
            tx.executeSql(`DROP TABLE ${tempTableName};`, [], () => {
              console.log(`Dropped table ${tempTableName}`);
            });
          },
          (error) => {
            console.error(`Error copying data from ${tempTableName} to ${tableName}:`, error);
          }
        );
      });
    });
  });
};
