{"name": "ActivityStudy", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint ."}, "dependencies": {"@react-native-async-storage/async-storage": "^1.17.10", "@react-native-community/cli-platform-ios": "^9.3.0", "@react-native-community/netinfo": "^11.4.0", "@react-native-firebase/app": "^18.9.0", "@react-native-firebase/crashlytics": "^18.9.0", "@react-native-masked-view/masked-view": "^0.2.8", "@react-native-picker/picker": "^2.4.8", "@react-navigation/drawer": "^6.5.0", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.2", "@sayem314/react-native-keep-awake": "^1.2.0", "babel-plugin-module-resolver": "^5.0.0", "eslint-plugin-import": "^2.27.5", "formik": "^2.2.9", "jwt-decode": "^3.1.2", "moment": "^2.29.4", "prop-types": "^15.8.1", "react": "18.2.0", "react-native": "^0.72.17", "react-native-animatable": "^1.3.3", "react-native-biometrics": "^3.0.1", "react-native-camera-kit": "^13.0.0", "react-native-check-version": "^1.1.1", "react-native-compressor": "^1.8.24", "react-native-device-info": "^10.3.0", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "^2.8.0", "react-native-image-picker": "^5.7.0", "react-native-image-viewing": "^0.2.2", "react-native-linear-gradient": "^2.6.2", "react-native-orientation-locker": "^1.6.0", "react-native-paper": "^4.12.5", "react-native-reanimated": "^3.15.0", "react-native-safe-area-context": "^4.5.3", "react-native-screens": "^3.27.0", "react-native-sqlite-storage": "^6.0.1", "react-native-vector-icons": "^9.2.0", "react-redux": "^7.2.9", "redux": "^4.1.2", "redux-thunk": "^2.4.2", "yup": "^0.32.11"}, "devDependencies": {"@babel/core": "^7.12.9", "@babel/runtime": "^7.12.5", "@react-native/gradle-plugin": "^0.73.4", "@react-native/metro-config": "^0.72.12", "babel-jest": "^24.1.0", "eslint": "^8.54.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^9.0.0", "eslint-import-resolver-babel-module": "^5.3.2", "eslint-plugin-import": "^2.29.0", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-native": "^4.1.0", "jest": "^25.0.0", "metro-react-native-babel-preset": "0.73.9", "prettier": "^3.1.0", "react-test-renderer": "18.1.0"}, "engines": {"node": ">=16"}}