import {
  ActivityIndicator,
  Alert,
  Image,
  Platform,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import React, { useEffect, useState } from 'react';
import { Card, useTheme } from 'react-native-paper';

import {
  AButton,
  AText,
  AuthLoading,
  IconButton,
  MainLayout,
  NoDataFound,
} from '_theme_components';
import { APP_ID, FontStyle, INTERNET_ERROR, VERSION } from '_utils';
import { useDispatch, useSelector } from 'react-redux';
import { recoverAction } from '../../store/actions/loginAction';
import { getData, isEmpty } from '_helpers';
import { ALERT_ERROR, ALERT_SUCCESS } from '../../store/reducers/alert';
import Icon from 'react-native-vector-icons/Ionicons';
import RNFS from 'react-native-fs';
import { styles as stylesSync } from '../Components/styles';
import moment from 'moment';

const RecoverScreen = ({ navigation, route }) => {
  // const { payload } = route.params;
  const dispatch = useDispatch();
  const { colors, dark } = useTheme();
  const [payload, setpayload] = useState({});
  const [loader, setLoader] = useState({});
  const [selectedStudy, setSelectedStudy] = useState([]);
  const [selectedStudyData, setSelectedStudyData] = useState([]);
  const [disableButton, setDisableButton] = useState(true);
  const { netConnection } = useSelector((state) => state.netInfo);
  const { loading } = useSelector((state) => state.projectReducer);

  // <EMAIL>
  useEffect(() => {
    getDataPayload();
  }, []);
  const getDataPayload = async () => {
    try {
      setLoader(true);
      let data = await getData('payload');
      data = JSON.parse(data);
      data.answerData = data.answerData.map((item) => {
        const { studyID, ...rests } = item; // Extract studyID and rest of the properties
        return { ...rests, studyNo: studyID }; // Add studyNo with the value of studyID
      });
      data.studyData = data.studyData.map((item) => {
        const { studyID, area, element, task, ...rest } = item; // Extract studyID and rest of the properties
        return { ...rest, studyNo: studyID, areaID: area, elementID: element, taskID: task }; // Add studyNo with the value of studyID
      });
      data.creatElement = data.creatElement.map((item) => {
        const { category, ...rest } = item; // Extract studyID and rest of the properties
        return { ...rest, categoryID: category }; // Add studyNo with the value of studyID
      });
      data.study = data.study.map((item) => {
        const { sendProjectStatsReport, sendStudyToEmail, ...rest } = item; // Extract studyID and rest of the properties
        return {
          ...rest,
          sendProjectStatsReport: sendProjectStatsReport === 1 ? true : false,
          sendStudyToEmail: sendStudyToEmail === 1 ? true : false,
        }; // Add studyNo with the value of studyID
      });
      // console.log(data, ' datatatatat');
      setpayload(data);
      setLoader(false);
    } catch (error) {
      setLoader(false);
    }
  };

  const submitData = async (download = false) => {
    if (isEmpty(selectedStudy)) {
      dispatch({
        type: ALERT_ERROR,
        payload: 'Please select studies..',
      });
      return;
    }
    setDisableButton(true);
    try {
      if (netConnection) {
        setLoader(true);
        const os = Platform.OS == 'ios' ? 'IOS' : 'Android';
        const payloads = {
          ...payload,
          study: selectedStudy,
          studyData: payload.studyData,
          createElement: payload.creatElement,
          createRole: payload.creatRole,
          createArea: payload.creatArea,
          answerData: payload.answerData,
          osinfo: os,
          version: VERSION,
          timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
          studyType: APP_ID,
          isRecovery: true,
        };
        // console.log(payloads, ' payloadsss');
        if (download) {
          try {
            var path =
              Platform.OS === 'ios'
                ? `${RNFS.DocumentDirectoryPath}`
                : `/storage/emulated/0/Android/media/com.crm.reTime.dots/Download`;
            if (Platform.OS === 'android') {
              RNFS.mkdir(path);
            }
            path += `/studyrecovery${Date.now()}.txt`;
            const a = await RNFS.writeFile(path, JSON.stringify(payloads), 'utf8')
              .then((success) => {
                console.log('Succeaass', success);
                dispatch({
                  type: ALERT_SUCCESS,
                  payload:
                    Platform.OS === 'ios'
                      ? 'Successfully downloaded'
                      : 'Successfully download to Android/Media',
                });
              })
              .catch((err) => {
                console.log(err.message, 'err');
              });
            // console.log(a,syndataTobe.length - 1 == index,' after download')
          } catch (error) {
            Alert.alert('Error', 'Failed to save file');
            console.error(error);
          }
        } else {
          dispatch(recoverAction(payloads, navigation));
        }
        setDisableButton(false);
        setLoader(false);
      } else {
        setDisableButton(false);
        dispatch({
          type: ALERT_ERROR,
          payload: INTERNET_ERROR,
        });
      }
    } catch (e) {
      setDisableButton(false);

      setLoader(false);
    }
  };
  const handleselectstudy = (id) => {
    let data = payload.study;
    if (selectedStudy.findIndex((n) => n.id === id) !== -1) {
      setSelectedStudy((products) => products.filter((val) => val.id !== id));
    } else {
      let item = data.find((n) => n.id === id);
      // if (item.synced == 1) {
      //   dispatch({
      //     type: ALERT_ERROR,
      //     payload: 'Study already synced',
      //   });
      //   return;
      // }
      setSelectedStudy((oldArray) => [...oldArray, item]);
    }
  };
  useEffect(() => {
    setDisableButton(!(selectedStudy.length > 0));
  }, [selectedStudy]);
  const handleselectstudyData = (id) => {
    if (selectedStudyData.findIndex((n) => n === id) !== -1) {
      setSelectedStudyData((products) =>
        products.filter((val) => {
          val !== id;
        })
      );
    } else {
      setSelectedStudyData((oldArray) => [...oldArray, id]);
    }
  };
  const fieldsArray = [
    { id: 1, name: 'taskName', showExtra: 'startTime', styles: stylesSync.taskViewStyle },
    { id: 1, name: 'elementName', showExtra: 'EC', styles: stylesSync.taskViewStyle },
    { id: 1, name: 'elementID', showExtra: 'EC', styles: stylesSync.taskViewStyle },
    { id: 1, name: 'isElementCreated', showExtra: 'EC', styles: stylesSync.taskViewStyle },
    { id: 1, name: 'isAreaCreated', showExtra: 'EC', styles: stylesSync.taskViewStyle },
    { id: 1, name: 'areaID', showExtra: '', styles: stylesSync.taskViewStyle },
    { id: 1, name: 'rating', showExtra: '', styles: stylesSync.taskViewStyle },
    { id: 1, name: 'frequency', showExtra: '', styles: stylesSync.taskViewStyle },
    { id: 1, name: 'areaName', showExtra: '', styles: stylesSync.taskViewStyle },
    { id: 1, name: 'notes', showExtra: '', styles: stylesSync.taskViewStyle },
    { id: 1, name: 'startTime', showExtra: '', styles: stylesSync.taskViewStyle },
    { id: 1, name: 'endTime', showExtra: '', styles: stylesSync.taskViewStyle },
    { id: 1, name: 'duration', showExtra: '', styles: stylesSync.taskViewStyle },
    { id: 1, name: 'photoLocal', showExtra: '', styles: stylesSync.taskViewStyle },
  ];

  const TextStyle = ({ value, showGray = false, fontBold = false, small }) => {
    return (
      <AText
        fontSize={small ? 'xxtrasmall' : 'small'}
        lightGray={showGray}
        fontWeight={fontBold ? FontStyle.fontBold : FontStyle.fontMedium}
      >
        {value ?? ''}
      </AText>
    );
  };
  return (
    <>
      {loader && loading ? <AuthLoading /> : null}
      <MainLayout
        showRecover
        submitData={submitData}
        disableButton={disableButton}
        back
        navigation={navigation}
        headerShow
      >
        <View style={styles.contentContainer}>
          <AText
            fontSize={'large'}
            styleText={{ marginBottom: 10 }}
            fontWeight={FontStyle.fontBold}
          >
            Local Data
          </AText>
          {!isEmpty(payload) &&
          (!isEmpty(payload.recoveryData) ||
            !isEmpty(payload.study) ||
            !isEmpty(payload.studyData)) ? (
            <>
              {payload.study.map((data) => (
                <View key={Math.random()} style={styles.container}>
                  <Card
                    style={{
                      ...styles.syncCardstyle,
                      paddingBottom: 5,
                      backgroundColor:
                        data?.cancelled === 1 ? '#ffdddd' : dark ? '#242124' : '#fff',
                    }}
                  >
                    <Card.Content style={stylesSync.syncCardContainer}>
                      <View style={{ width: '75%' }}>
                        <View style={{ ...stylesSync.syncCardName, alignItems: 'center' }}>
                          <IconButton
                            onPress={() => {
                              handleselectstudy(data.id);
                            }}
                            btnStyle={{ ...stylesSync.pressableStyle, width: 50 }}
                            icon={
                              <Icon
                                style={stylesSync.syncCardiconStyle}
                                name={
                                  !isEmpty(selectedStudy) &&
                                  selectedStudy.findIndex((n) => n.id === data.id) !== -1
                                    ? 'checkbox'
                                    : 'square-outline'
                                }
                                color={
                                  !isEmpty(selectedStudy) &&
                                  selectedStudy.findIndex((n) => n.id === data.id) !== -1
                                    ? colors.primary
                                    : '#878787'
                                }
                                size={27}
                              />
                            }
                          />
                          <AText fontSize={'medium'} fontWeight={FontStyle.fontMedium}>
                            {data?.name}
                          </AText>
                        </View>
                        <View style={stylesSync.syncCardRow}>
                          {!isEmpty(data?.customerName) && (
                            <View style={stylesSync.syncCardElement}>
                              <Icon
                                name="folder-open-outline"
                                style={stylesSync.syncCardiconStyle}
                                color={'#878787'}
                                size={20}
                              />
                              <TextStyle
                                showGray
                                fontBold
                                value={
                                  data?.customerName?.length > 20
                                    ? data?.customerName.substring(0, 20) + '...'
                                    : data?.customerName
                                }
                              />
                            </View>
                          )}
                          {/* <View style={stylesSync.syncCardElement}>
                            <Icon
                              name="layers-outline"
                              style={stylesSync.syncCardiconStyle}
                              color={'#878787'}
                              size={22}
                            />
                            <TextStyle showGray fontBold value={data?.project?.name} />
                          </View> */}
                          <View style={stylesSync.syncCardElement}>
                            <Icon
                              name="location-outline"
                              style={stylesSync.syncCardiconStyle}
                              color={'#878787'}
                              size={20}
                            />
                            <TextStyle showGray fontBold value={data?.locationName} />
                          </View>
                          <View
                            style={{
                              ...stylesSync.syncCardElement,
                              flexWrap: 'wrap',
                              justifyContent: 'flex-start',
                              alignItems: 'center',
                            }}
                          >
                            <Icon
                              name="timer"
                              style={stylesSync.syncCardiconStyle}
                              color={'#878787'}
                              size={20}
                            />

                            <TextStyle
                              showGray
                              fontBold
                              small
                              value={'Start Time: ' + moment(data?.studyStartTime).format('lll')}
                            />
                            <TextStyle
                              showGray
                              fontBold
                              small
                              value={'    End Time:  ' + moment(data?.studyEndTime).format('lll')}
                            />
                          </View>
                          {/* <View style={stylesSync.syncCardElement}>
                              <Icon
                                name="timer"
                                style={stylesSync.syncCardiconStyle}
                                color={'#878787'}
                                size={20}
                              />
                              <TextStyle
                                showGray
                                fontBold
                                value={moment(data?.studyEndTime).format('lll')}
                              />
                            </View> */}
                        </View>
                      </View>
                      <AButton
                        onPress={() => {
                          handleselectstudyData(data.id);
                        }}
                        btnStyle={{ alignSelf: 'flex-start' }}
                        mode="text"
                        title={
                          !isEmpty(selectedStudyData) &&
                          selectedStudyData.findIndex((n) => n === data.id) !== -1
                            ? 'HIDE DetailS'
                            : 'SEE DetailS'
                        }
                        fontSize={'medium'}
                        styleText={{ color: colors.primary }}
                      />
                      <View
                        style={{
                          ...stylesSync.syncCardElement,
                          position: 'absolute',
                          right: 20,
                          top: 50,
                        }}
                      >
                        {data?.cancelled === 1 ? (
                          <AText
                            fontSize={'small'}
                            lightGray={false}
                            fontWeight={FontStyle.fontBold}
                            styleText={{ color: 'red', marginLeft: 10 }}
                          >
                            Cancelled Study
                          </AText>
                        ) : null}
                      </View>
                      {/* )} */}
                    </Card.Content>
                    {!isEmpty(payload.studyData) &&
                    !isEmpty(selectedStudyData) &&
                    selectedStudyData.findIndex((n) => n === data.id) !== -1 ? (
                      // || showDataAlways ?
                      <View style={{ ...stylesSync.cardTableContent, marginTop: 0 }}>
                        {!isEmpty(payload.studyData) &&
                          payload.studyData.map((item) => (
                            <>
                              {item.studyNo == data.id ? (
                                <View
                                  key={Math.random()}
                                  style={{
                                    width: '100%',
                                    borderBottomWidth: 1,
                                    borderColor: 'lightgray',
                                    marginTop: 10,
                                    paddingBottom: 10,
                                  }}
                                >
                                  {fieldsArray.map(({ styles, showExtra, name }) => (
                                    <>
                                      {showExtra === 'startTime' ||
                                      (showExtra === 'EC' &&
                                        !isEmpty(item.continuesObservation)) ? (
                                        <TextStyle
                                          showGray
                                          value={
                                            showExtra === 'startTime'
                                              ? new Date(item[showExtra])
                                                  .toLocaleTimeString()
                                                  .replace(/:[^:]*$/, '')
                                              : !isEmpty(item.continuesObservation) &&
                                                  name === 'elementName'
                                                ? 'EC'
                                                : null
                                          }
                                        />
                                      ) : null}
                                      <View
                                        style={{
                                          flexDirection: 'row',
                                          // borderBottomWidth: 1,
                                          // borderColor: 'lightgray',
                                          width: '100%',
                                          // justifyContent: 'space-between',
                                        }}
                                      >
                                        {/* <TextStyle value={name} /> */}
                                        <AText
                                          fontSize={'small'}
                                          fontWeight={FontStyle.fontBold}
                                          styleText={{
                                            width: '40%',
                                            color: dark ? '#fff' : 'black',
                                            // backgroundColor: 'orange'
                                          }}
                                        >
                                          {name}
                                        </AText>

                                        <View
                                          style={[
                                            styles,
                                            { width: '60%' },
                                            name === 'elementName'
                                              ? {
                                                  paddingTop: isEmpty(item.continuesObservation)
                                                    ? 0
                                                    : 0,
                                                }
                                              : {},
                                          ]}
                                        >
                                          {name == 'photoLocal' && (
                                            <View
                                              style={{ flexDirection: 'row', flexWrap: 'wrap' }}
                                            >
                                              {item[name].split(',').map((fitem) => (
                                                <TouchableOpacity
                                                // onPress={() => setMediaUrl(item)}
                                                >
                                                  <Image
                                                    source={{
                                                      uri: `${fitem}`,
                                                    }}
                                                    style={{
                                                      width: fitem ? 100 : 0,
                                                      height: fitem ? 100 : 0,
                                                      marginTop: 10,
                                                      marginRight: 10,
                                                    }}
                                                  />
                                                </TouchableOpacity>
                                              ))}
                                            </View>
                                          )}
                                          {name !== 'photoLocal' ? (
                                            <TextStyle
                                              showGray
                                              value={
                                                name === 'endTime' || name === 'startTime'
                                                  ? moment(item[name]).format('lll')
                                                  : item[name]
                                              }
                                            />
                                          ) : null}
                                        </View>
                                      </View>
                                    </>
                                  ))}
                                </View>
                              ) : null}
                            </>
                          ))}
                      </View>
                    ) : null}
                  </Card>
                </View>
              ))}
              {/* {payload.study.map((item, index) => (
                <View
                  style={{
                    flexDirection: 'row',
                    flexWrap: 'wrap',
                    marginTop: index > 0 ? 35 : 0,
                    width: '100%',
                  }}
                >
                  <IconButton
                    onPress={() => {
                      handleselectstudy(item.id);
                    }}
                    mode="text"
                    btnStyle={{ alignSelf: 'flex-start', paddingVertical: 0, width: 40 }}
                    icon={
                      <Icon
                        name={
                          !isEmpty(selectedStudy) &&
                          selectedStudy.findIndex((n) => n.id === item.id) !== -1
                            ? 'checkbox'
                            : 'square-outline'
                        }
                        color={
                          !isEmpty(selectedStudy) &&
                          selectedStudy.findIndex((n) => n.id === item.id) !== -1
                            ? colors.primary
                            : '#878787'
                        }
                        size={27}
                      />
                    }
                  />
                  <View style={{ flexDirection: 'column' }}>
                    {Object.keys(item).map((key) => (
                      <View key={key} style={{ flexDirection: 'column' }}>
                        <AText>
                          {key}: {item[key]}
                        </AText>
                      </View>
                    ))}
                    <AButton
                      mode="text"
                      onPress={() => {
                        handleselectstudyData(item.id);
                      }}
                      btnStyle={{
                        alignItems: 'center',
                        flexDirection: 'row-reverse',
                        alignSelf: 'flex-start',
                        paddingVertical: 0,
                        padding: 0,
                      }}
                      fontWeight={FontStyle.fontBold}
                      fontSize={'small'}
                      title={'StudyDetails'}
                      styleText={{ color: dark ? '#fff' : '#000' }}
                      icon={
                        <Icon
                          name={
                            !isEmpty(selectedStudyData) &&
                            selectedStudyData.findIndex((n) => n === item.id) !== -1
                              ? 'chevron-down'
                              : 'chevron-up'
                          }
                          color={'#000'}
                          style={styles.iconStyle}
                          size={27}
                        />
                      }
                    />
                    {!isEmpty(selectedStudyData) &&
                    selectedStudyData.findIndex((n) => n === item.id) !== -1 &&
                    payload.studyData.findIndex((n) => n.studyNo === item.id) == -1 ? (
                      <AText fontWeight={FontStyle.fontBold} fontSize={'small'}>
                        No Study Data
                      </AText>
                    ) : (
                      <>
                        {!isEmpty(payload.studyData) &&
                        !isEmpty(selectedStudyData) &&
                        selectedStudyData.findIndex((n) => n === item.id) !== -1
                          ? payload.studyData.map(
                              (itemData) =>
                                itemData.studyNo == item.id &&
                                Object.keys(itemData).map((keyData) => (
                                  <View key={keyData}>
                                    <AText>
                                      {keyData}: {itemData[keyData]}
                                    </AText>
                                  </View>
                                ))
                            )
                          : null}
                      </>
                    )}
                  </View>
                </View>
              ))} */}
            </>
          ) : (
            <NoDataFound
              imageStyle={[styles.imageStyle, { tintColor: colors.primary }]}
              source={require('../../assets/images/no-search-data.png')}
              text={`There is no data.`}
            />
          )}
        </View>
      </MainLayout>
    </>
  );
};

export default RecoverScreen;

const styles = StyleSheet.create({
  container: {
    // alignItems: 'center',
    width: '100%',
    marginVertical: 8,
  },
  contentContainer: {
    width: '95%',
    // marginTop: 20,
    alignSelf: 'center',
  },
  textstyle: {
    marginTop: 19,
    fontFamily: FontStyle.fontRegular,
  },
  linkStyle: {
    textDecorationLine: 'underline',
  },
  imageStyle: {
    resizeMode: 'contain',
    width: 370,
    height: 350,
    marginVertical: 15,
  },
});
