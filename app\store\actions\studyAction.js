import { executeFetch, insertIntoTable, isEmpty, updateTable } from '_helpers';
import { ALERT_ERROR, ALERT_HIDE, ALERT_SUCCESS } from '../reducers/alert';
import { APP_ID, ERROR, tables } from '_utils';
import moment from 'moment';

export const submitStudyAction = (payload, dbDetail, studyId) => (dispatch) => {
  // dispatch({
  //   type: STUDY_LOADING,
  // });
  executeFetch(`sync/study`, 'POST', payload)
    .then(async (response) => {
      if (response.status === 200 && response.data.data.success) {
        updateTable(
          tables.STUDY_TABLE,
          null,
          { _id: response.data.data.syncedStudyID, studyID: studyId },
          dbDetail
        );
        dispatch({
          type: STUDY_SUBMITTED,
          payload: {
            studyId: studyId ?? 0,
            syncedStudyID: {
              syncedStudyID: response.data.data.syncedStudyID,
              studyName: payload.name,
              studyId: studyId,
            },
          },
        });
      } else {
        dispatch({
          type: ALERT_ERROR,
          payload: response?.data?.message ?? 'Something went wrong. Please try again later.',
        });
        dispatch({
          type: STUDY_SUBMITTED_FAILED,
          payload: studyId ?? 0,
        });
        const err = [
          {
            errorMsg: response?.data?.message,
            errorFrom: 'submitStudyAction',
            time: moment().format('lll'),
          },
        ];
        insertIntoTable(tables.ERROR_TABLE, err, dbDetail);
      }
    })
    .catch((error) => {
      dispatch({
        type: STUDY_SUBMITTED_FAILED,
        payload: studyId ?? 0,
      });
      dispatch({
        type: ALERT_ERROR,
        payload: 'Something went wrong. Please try again later.',
      });
      const err = [
        {
          errorMsg: JSON.stringify(error),
          errorFrom: 'submitStudyAction catch',
          time: moment().format('lll'),
        },
      ];
      insertIntoTable(tables.ERROR_TABLE, err, dbDetail);
    });
};

export const syncingStatusCheck =
  (payload, returnResponse = false) =>
  async (dispatch) => {
    if (isEmpty(payload) && isEmpty(payload.syncedStudyIDs)) {
      return;
    }
    dispatch({
      type: 'SYNCDATA_LOADING',
    });
    try {
      const response = await executeFetch(`sync/study/progress`, 'POSTWithoutTimeout', payload);
      if (response.status === 200) {
        dispatch({
          type: 'SYNCDATA_LOADING_FALSE',
        });
        if (returnResponse) {
          return response.data.data;
        }
        let data = response.data;
        dispatch({
          type: 'SYNCING_STUDIES',
          payload: data.data,
        });
      } else {
        dispatch({
          type: 'SYNCDATA_LOADING_FALSE',
        });
        dispatch({
          type: STUDY_SUBMITTED_FAILED,
          payload: payload.syncedStudyIDs ?? [],
        });
        if (returnResponse) {
          return;
        }
        dispatch({
          type: ALERT_ERROR,
          payload: response?.data?.message ?? 'Something went wrong. Please try again later.',
        });
      }
    } catch (error) {
      dispatch({
        type: 'SYNCDATA_LOADING_FALSE',
      });
      dispatch({
        type: ALERT_ERROR,
        payload: 'Something went wrong. Please try again later.',
      });
      const err = [
        {
          errorMsg: JSON.stringify(error),
          errorFrom: 'syncingStatusCheck catch',
          time: moment().format('lll'),
        },
      ];
      insertIntoTable(tables.ERROR_TABLE, err, dbDetail);
      if (returnResponse) {
        return;
      }
    }
  };
export const STUDY_LOADING_FALSE = 'STUDY_LOADING_FALSE';
export const STUDY_LOADING = 'STUDY_LOADING';
export const STUDY_SUBMITTED = 'STUDY_SUBMITTED';
export const STUDY_SUBMITTED_FAILED = 'STUDY_SUBMITTED_FAILED';
export const STUDY_LIST = 'STUDY_LIST';
