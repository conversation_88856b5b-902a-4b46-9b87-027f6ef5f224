import React, { useState } from 'react';
import { Pressable, StyleSheet, View } from 'react-native';
import { useTheme } from 'react-native-paper';
import { FontStyle, LINEAR_GRADIENT_RATING_BUTTON, windowHeight, windowWidth } from '_utils';
import { AText, RatingButton } from './';
import { isEmpty } from '_helpers';
import LinearGradient from 'react-native-linear-gradient';
import FontAwesome from 'react-native-vector-icons/FontAwesome5';

const FrequencyPanel = ({ disableBtn, setFreqInput, freqInput, styleText }) => {
  const n = 14;
  const { colors, dark } = useTheme();
  const freq = 13;
  const [freqKeyPressed, setFreqKeyPressed] = useState();

  return (
    <View style={[styles.freqViewcontainer]}>
      {[...Array(freq)].map((e, i) => {
        let val = i == 9 ? 0 : i + 1;
        return (
          <Pressable
            activeOpacity={0.9}
            onPress={() => {
              let frequency = isEmpty(freqInput) ? 0 : parseInt(freqInput);
              let value =
                i == 11 && freqInput > 0 ? frequency - 1 : i == 12 ? frequency + 1 : frequency;
              setFreqKeyPressed(val);
              if (i === 11 || i === 12) {
                setFreqInput(value.toString());
              } else if (i == 10 && !isEmpty(freqInput)) {
                setFreqInput(freqInput.substring(0, freqInput.length - 1));
              } else if (i < 10) {
                setFreqInput(freqInput + val);
              }
            }}
            key={i * n}
            disabled={
              disableBtn
                ? true
                : i == 10 || i == 11 || i == 12
                  ? false
                  : freqInput.length > 4
                    ? true
                    : false
            }
            style={[
              styles.freqcontainer,
              {
                height: windowWidth > windowHeight ? windowWidth * 0.04 : windowWidth * 0.1,
                width:
                  // i == 10 && windowWidth > windowHeight
                  //   ? '32%'
                  // :
                  i == 10
                    ? '60%'
                    : // : windowWidth > windowHeight
                      //   ? '15%'
                      Platform.OS == 'ios'
                      ? '27%'
                      : '28%',
                opacity: !isEmpty(freqInput) && freqKeyPressed == val ? 0.8 : 1,
                backgroundColor: dark ? colors.onSurface : '#fff',
                borderWidth: !isEmpty(freqInput) && freqKeyPressed == val ? 3 : 1,
                borderColor: !isEmpty(freqInput) && freqKeyPressed == val ? '#00C0F3' : '#fff',
              },
            ]}
          >
            <LinearGradient
              colors={
                !isEmpty(freqInput) && freqKeyPressed == val
                  ? LINEAR_GRADIENT_RATING_BUTTON
                  : dark
                    ? [colors.onSurface, colors.onSurface]
                    : ['#fff', '#fff']
              }
              key={i * n}
              start={{ x: 0.1, y: 0.3 }}
              end={{ x: 0.5, y: 0.7 }}
              style={styles.selectedfreqcontainer}
            >
              {i == 10 ? (
                <FontAwesome
                  name="backspace"
                  style={styles.iconStyle}
                  size={25}
                  color={dark ? '#fff' : '#000'}
                />
              ) : (
                <AText
                  fontWeight={FontStyle.fontMedium}
                  styleText={{ color: dark ? '#fff' : '#000', textAlign: 'center' }}
                  fontSize={'large'}
                >
                  {i == 9 ? 0 : i == 11 ? '-' : i == 12 ? '+' : i + 1}
                </AText>
              )}
            </LinearGradient>
          </Pressable>
        );
      })}
    </View>
  );
};

export default FrequencyPanel;
const styles = StyleSheet.create({
  freqViewcontainer: {
    width: '90%',
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'center',
    flexWrap: 'wrap',
    // marginTop: 15,
    marginBottom: 5,
    justifyContent: 'center',
  },
  selectedfreqcontainer: {
    justifyContent: 'center',
    width: '100%',
    height: '100%',
    alignSelf: 'center',
    alignItems: 'center',
    borderRadius: 12,
  },
  freqcontainer: {
    justifyContent: 'center',
    marginHorizontal: 7,
    marginVertical: 10,
    alignItems: 'center',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#d8d8d8',
  },

  rateContainStyle: {
    width: '99%',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    flexWrap: 'wrap',
    paddingBottom: 55,
  },
});
