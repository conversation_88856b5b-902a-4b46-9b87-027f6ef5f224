import { isEmpty, isJson } from '_helpers';

export const canContinueElement =
  (elementID, areaId, taskId, completedObservations) => (dispatch) => {
    return completedObservations.some((observation) => {
      return (
        observation.element == elementID && observation.area == areaId && observation.task == taskId
      );
    });
  };
export const checkNotesEC = (elementID, areaId, taskId, completedObservations) => {
  let completedObservation = completedObservations.slice();
  completedObservation = completedObservation.reverse();
  const usedElementEntry = completedObservation.find((observation) => {
    return (
      observation.element == elementID && observation.area == areaId && observation.task == taskId
    );
  });
  if (!isEmpty(usedElementEntry)) {
    return usedElementEntry.notes;
  }
  return '';
};
export const ContinueSelectedElement = (elementID, areaId, taskId, completedObservations) => {
  let completedObservation = completedObservations.slice();
  completedObservation = completedObservation.reverse();
  const usedElementEntry = completedObservation.find((observation) => {
    return (
      observation.element == elementID && observation.area == areaId && observation.task == taskId
    );
  });
  if (usedElementEntry) {
    if (usedElementEntry.continuesObservation) {
      return findLastEC(completedObservations, usedElementEntry.continuesObservation);
    } else {
      return usedElementEntry.localID;
    }
    // let usedElementIndex = (completedObservation.length - 1) - usedElementEntry
  }
  return false;
};

export const isYesOrNo = (name) => {
  if (['yes', 'no'].indexOf(name.toLowerCase()) != -1) {
    return true;
  }
  return false;
};

export const checkECNotesINObservation = (id, note, data) => {
  let studies = data.findIndex((item) => item.id === id); // 3 === 4
  if (studies >= -1) {
    if (isEmpty(data[studies].continuesObservation)) {
      data[studies].notes = note;
      return;
    } else {
      data[studies].notes = '';
      checkECNotesINObservation(data[studies].continuesObservation, note, data); // 4
    }
  }
};

export const findLastEC = (elements, lastId) => {
  const element = elements.find((e) => e.localID === lastId);
  if (element) {
    if (element.continuesObservation) {
      return findLastUsedId(elements, element.continuesObservation);
    } else {
      return element.localID;
    }
  }

  return false; // Last ID not found in the array
};

export const loadElementDataFetch = (allElementList, taskID) => async (dispatch) => {
  let elementdata = [];
  let elementPosition = [];

  // Check if allElementList has elements and find matching task by taskID
  if (allElementList.length > 0) {
    const elements = allElementList.find((item) => item.taskID == taskID);

    // Use optional chaining and default values for robustness
    elementPosition = elements?.elementsPosition || [];

    // Parse grouped_data only if it's a valid JSON
    if (!isEmpty(elements?.grouped_data)) {
      elementdata = isJson(elements.grouped_data)
        ? JSON.parse(elements.grouped_data)
        : elements.grouped_data;
    }
  }

  // Call elementOrdering to process and sort elements
  const { orderElement, showYESElement, showNOElement } = await elementOrdering(
    elementdata,
    elementPosition
  );

  // Dispatch the ordered elements and flags to the store
  dispatch({
    type: 'STORE_ELEMENT_LIST',
    payload: { elementList: orderElement, showYESElement, showNOElement },
  });
};

// Function to order elements based on existing positions and addPosition instructions
export const elementOrdering = (elementdata, elementPosition) => {
  const existingPositions = new Set(elementPosition); // Fast lookups for positions
  const positionMap = new Map(); // Store elements by their _id for quick access
  const newPositions = []; // Store elements that need to be inserted later

  // Build the positionMap and newPositions in a single loop
  elementdata.forEach((item) => {
    const { _id, addPosition } = item;
    positionMap.set(_id, item); // Map element by its _id

    // If element _id doesn't exist in current positions, mark for insertion
    if (!existingPositions.has(_id)) {
      newPositions.push({ _id, addPosition });
    }
  });

  // Insert missing elements based on their addPosition
  newPositions.forEach(({ _id, addPosition }) => {
    switch (addPosition) {
      case 'addFirst':
        elementPosition.unshift(_id); // Insert at the beginning
        break;
      case 'addLast':
        elementPosition.push(_id); // Insert at the end
        break;
      default:
        const afterIndex = elementPosition.indexOf(addPosition);
        if (afterIndex !== -1) {
          // Insert _id after the specified element if found
          elementPosition.splice(afterIndex + 1, 0, _id);
        } else {
          // Default to adding at the end if addPosition is invalid
          elementPosition.push(_id);
        }
        break;
    }
    existingPositions.add(_id); // Mark as processed
  });

  // Map the final ordered elements from the elementPosition array
  const orderedElements = elementPosition
    .map((positionId) => positionMap.get(positionId)) // Fetch elements in the new order
    .filter(Boolean); // Ensure no undefined values if some _ids don't exist in elementdata

  // Separate "yes" and "no" elements from the rest
  const yesNoElements = [];
  const orderElement = [];

  orderedElements.forEach((item) => {
    const lowerCaseName = item.name?.toLowerCase();
    if (lowerCaseName === 'yes' || lowerCaseName === 'no') {
      yesNoElements.push(item);
    } else {
      orderElement.push(item);
    }
  });

  // Set flags if "YES" or "NO" elements are found
  const showYESElement = yesNoElements.find((item) => item.name.toLowerCase() === 'yes');
  const showNOElement = yesNoElements.find((item) => item.name.toLowerCase() === 'no');

  // Return the ordered elements and the flags for "YES" and "NO"
  return { orderElement, showYESElement, showNOElement };
};
