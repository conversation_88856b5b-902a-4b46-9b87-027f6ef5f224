import { FlatList, Platform, StyleSheet, Pressable, View } from 'react-native';
import React, { useState } from 'react';
import { useTheme } from 'react-native-paper';
import LinearGradient from 'react-native-linear-gradient';
import { useDispatch, useSelector } from 'react-redux';

import { AText, StudyHeader, StudyNextCard } from '_theme_components';
import { FontStyle, LINEAR_GRADIENT_RATING_BUTTON, windowHeight, windowWidth } from '_utils';
import { isEmpty } from '_helpers';

const RateScreen = ({
  navigation,
  nextElementData,
  navigateScreen,
  ratingSlect,
  setRatingsAndFreq,
}) => {
  const { colors, dark } = useTheme();
  const dispatch = useDispatch();

  const { reRate, projectSelect, taskSelect, elementSelect } = useSelector(
    (state) => state.createStudy
  );
  const { controllingElementID } = taskSelect;
  const [reRatings, setReRatings] = useState([]);
  // const isCount = !elementSelect.count;
  const isCount = elementSelect.count === 1 ? true : false;

  const rate = 60;
  const n = 14;

  const RatePanel = React.memo(() => {
    return (
      <>
        {[...Array(n)].map((e, i) => {
          let val = rate + i * 5;
          return (
            <Pressable
              activeOpacity={0.9}
              onPress={() => {
                selectRating(val);
              }}
              key={i * n}
              style={[
                styles.ratecontainer,
                {
                  height:
                    Platform.OS == 'ios' && windowWidth < windowHeight
                      ? windowWidth * 0.12
                      : Platform.OS == 'ios' && windowWidth > windowHeight
                        ? windowWidth * 0.07
                        : Platform.OS == 'android' && windowWidth > windowHeight
                          ? windowWidth * 0.07
                          : Platform.OS == 'android' && windowWidth < windowHeight
                            ? windowWidth * 0.17
                            : windowWidth * 0.12,

                  width:
                    Platform.OS == 'ios' && val === 125
                      ? '65%'
                      : Platform.OS == 'ios'
                        ? '20%'
                        : val === 125
                          ? '59%'
                          : '19%',
                  backgroundColor:
                    ratingSlect == val
                      ? 'rgba(208, 180, 235, 0.9)'
                      : dark
                        ? colors.onSurface
                        : '#fff',
                  borderColor: ratingSlect == val ? '#00C0F3' : '#fff',
                  borderWidth: ratingSlect == val ? 3 : 1,
                },
              ]}
            >
              <LinearGradient
                colors={
                  ratingSlect == val
                    ? LINEAR_GRADIENT_RATING_BUTTON
                    : dark
                      ? [colors.onSurface, colors.onSurface]
                      : ['#fff', '#fff']
                }
                key={i * n}
                locations={[0, 0.9]}
                start={{ x: 0.0, y: 0.6 }}
                end={{ x: 0.6, y: 0.9 }}
                style={[
                  styles.slectedRatecontainer,
                  {
                    opacity: 0.8,
                  },
                ]}
              >
                <AText
                  fontWeight={FontStyle.fontMedium}
                  styleText={{
                    textAlign: 'center',
                    color: ratingSlect == val || dark ? '#fff' : '#000',
                  }}
                  fontSize={'large'}
                >
                  {val === 125 ? 'NOT RATED' : val}
                </AText>
              </LinearGradient>
            </Pressable>
          );
        })}
      </>
    );
  }, []);

  const goToScreen = async (screen, elementContinue = false, element) => {
    setReRatings([]);
    navigateScreen(screen, elementContinue, element);
  };

  const selectRating = (rating) => {
    if (isCount && projectSelect.rerateElement) {
      saveReRate(rating);
    } else {
      saveRating(rating);
    }
  };

  const saveReRate = (rating) => {
    var ratings = rating === 125 ? 0 : rating;
    let reRateElement = reRate;
    reRateElement.push(ratings);
    dispatch({
      type: 'SAVE_RERATE',
      payload: reRateElement,
    });

    let totalRatings = reRate.reduce((total, element) => total + element, 0);
    var averageRating = reRate.length === 0 ? 0 : Math.round(totalRatings / reRate.length);
    averageRating = reRate.length == 1 && averageRating == 0 ? 'Not Rated' : averageRating;
    ratings = ratings == 0 ? 'Not Rated' : ratings;
    const ratingVal = averageRating > 0 && projectSelect.rerateElement ? averageRating : ratings;
    setRatingsAndFreq(rating, ratingVal, true);
  };

  /* PARSING ROUND DATA TO NEXT PAGE */
  const saveRating = (rating) => {
    let ratings = rating === 125 ? 0 : rating;
    let ratingVal = ratings === 0 ? 'Not Rated' : ratings;
    var reRatearr = [];
    if (projectSelect.rerateElement && reRatings.length > 0) {
      reRatearr = [...reRatings, ratings];
      const totalRatings = reRatearr.reduce((sum, reRate) => {
        return sum + reRate;
      }, 0);
      const averageRating = Math.round(totalRatings / reRatearr.length);

      ratingVal = averageRating > 0 ? averageRating : ratingVal;
    }
    setRatingsAndFreq(rating, ratingVal, isCount);
    if (projectSelect.rerateElement) {
      setReRatings((reRatings) => [...reRatings, ratings]);
    }
  };

  const ItemView = ({ item, index }) => {
    return (
      <View
        style={{ flex: 1, width: Platform.OS == 'ios' ? '90%' : '95%', alignSelf: 'center' }}
        key={item.id + index}
      >
        <StudyNextCard
          controllingElementID={
            !isEmpty(controllingElementID) && controllingElementID == item._id ? true : false
          }
          data={item}
          ShowEC={true}
          icon={
            !isEmpty(controllingElementID) && controllingElementID == item._id
              ? 'target'
              : 'arrow-right'
          }
          navigateNext={(val) => {
            goToScreen('nextElement', val, item);
          }}
        />
      </View>
    );
  };

  return (
    <>
      <StudyHeader navigation={navigation} Title={'Rate'} SubTitle={'Select rating'} rate />
      <View
        style={[
          styles.container,
          {
            width:
              Platform.OS == 'ios' && windowWidth < windowHeight
                ? '90%'
                : Platform.OS == 'ios' && windowWidth > windowHeight
                  ? '70%'
                  : Platform.OS == 'android' && windowWidth > windowHeight
                    ? '80%'
                    : '100%',
          },
        ]}
      >
        <View style={styles.rateViewcontainer}>
          <RatePanel />
        </View>

        <View style={styles.nextElemtcontainer}>
          {!isEmpty(nextElementData) && !isCount && (
            <>
              <View style={styles.nextElemtHeadercontainer}>
                <AText
                  fontWeight={FontStyle.fontBold}
                  styleText={{ color: '#c7c7c7' }}
                  fontSize={'title'}
                >
                  Next elements
                </AText>
              </View>
              {nextElementData.map((item, index) => (
                <ItemView key={index.toString()} item={item} />
              ))}
            </>
          )}
        </View>
      </View>
    </>
  );
};

export default RateScreen;

const styles = StyleSheet.create({
  container: {
    width: '92%',
    marginTop: 15,
    paddingBottom: 45,
    flexDirection: 'column',
  },
  rateViewcontainer: {
    width: Platform.OS == 'ios' ? '90%' : '100%',
    alignSelf: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    flexWrap: 'wrap',
  },
  nextElemtcontainer: {
    paddingBottom: 18,
    paddingHorizontal: Platform.OS == 'ios' ? 25 : 5,
  },
  nextElemtHeadercontainer: {
    justifyContent: 'flex-start',
    alignSelf: 'center',
    width: Platform.OS == 'ios' ? '77%' : '80%',
    marginStart: 0,
    marginTop: 20,
  },
  ratecontainer: {
    alignSelf: 'center',
    alignContent: 'center',
    justifyContent: 'center',
    marginHorizontal: 7,
    marginVertical: 8,
    alignItems: 'center',
    borderRadius: 26,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
    elevation: Platform.OS == 'ios' ? 0 : 2.5,
  },
  slectedRatecontainer: {
    justifyContent: 'center',
    width: '100%',
    height: '100%',
    alignSelf: 'center',
    alignItems: 'center',
    borderRadius: 24,
  },
});
