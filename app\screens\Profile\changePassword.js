import React from 'react';
import { View } from 'react-native';
import { Card } from 'react-native-paper';
import { useDispatch, useSelector } from 'react-redux';
import { changePasswordfieldsArray } from './constants';
import { styles } from './styles';

import { AText, AuthLoading, MainLayout } from '_theme_components';
import { FontStyle, windowWidth } from '_utils';
import { Formiks } from '_helpers';
import { passwordValidationSchema } from '_validation';
import { changePasswordAction } from '_action';

const ChangePasswordScreen = ({ navigation }) => {
  const dispatch = useDispatch();
  const { userDetails, loading, dbDetail } = useSelector((state) => state.user);
  const submitChange = (val) => {
    const payload = {
      newPassword: val.newpassword,
      confirmPassword: val.confirmpassword,
    };
    dispatch(changePasswordAction(payload, dbDetail, userDetails._id));
  };

  return (
    <>
      {loading ? <AuthLoading /> : null}
      <MainLayout back navigation={navigation} headerShow>
        <View style={styles.HeaderContainer}>
          <View style={{ alignSelf: 'flex-start' }}>
            <AText fontWeight={FontStyle.fontBold} fontSize={'title'}>
              Change Password
            </AText>
          </View>

          <Card style={styles.projectCardstyle}>
            <Card.Content>
              <Formiks
                validation={passwordValidationSchema}
                fieldArray={changePasswordfieldsArray}
                loginPage={false}
                btntitle={'Change password'}
                btnContentStyle={styles.btnContentStyle}
                btnStyle={styles.saveChangeBtnStyle}
                viewStyle={[styles.saveChangeButton, { width: windowWidth * 0.35 }]}
                submitChange={(values) => submitChange(values)}
              />
            </Card.Content>
          </Card>
        </View>
      </MainLayout>
    </>
  );
};

export default ChangePasswordScreen;
