import { FlatList, Platform, StyleSheet, View, ScrollView, Alert } from 'react-native';
import React, { useEffect, useState } from 'react';
import { Card, useTheme } from 'react-native-paper';
import { useDispatch, useSelector } from 'react-redux';
import { useIsFocused } from '@react-navigation/native';

import {
  getDataByTableAndID,
  getStudies,
  getLocationByID,
  isEmpty,
  getTaskByID,
  getData,
} from '_helpers';
import { AButton, FabButton, NoDataFound } from '_theme_components';
import {
  APP_ID,
  FontStyle,
  tables,
  INTERNET_ERROR,
  resync_confirmation,
  sync_studies,
  localimage,
  VERSION,
} from '_utils';
import { ALERT_ERROR, ALERT_HIDE, ALERT_SUCCESS } from '../../store/reducers/alert';
import { submitStudyAction } from '_action';
import { getSyncExclamation, syncOfflinePhotos } from '_provider';
import { BottomModal, SyncCard } from '../Components';
import RNFS from 'react-native-fs';

const SyncScreen = ({ navigation, loadExclamtion }) => {
  const { colors, dark } = useTheme();
  const dispatch = useDispatch();
  const isFocused = useIsFocused();

  const [showingSynced, setShowingSynced] = useState(false);
  const { dbDetail } = useSelector((state) => state.user);
  const { projectList } = useSelector((state) => state.projectReducer);
  const { userDetails } = useSelector((state) => state.user);
  const { netConnection, showDownload } = useSelector((state) => state.netInfo);
  const { noOfStudySynced, dataSyncCompleted } = useSelector((state) => state.study);

  const [unSyncObsData, setUnsyncObsData] = useState([]);
  const [syncObsData, setSyncObsData] = useState([]);
  const [recoverObsData, setRecoverObsData] = useState([]);
  const [selectedStudy, setSelectedStudy] = useState([]);
  const [resyncConfirmModal, setResyncConfirmModal] = useState(false);
  const [showDetail, setShowDetail] = useState(false);
  const [showActivityIndicator, setShowActivityIndicator] = useState(false);
  const [showObservationId, setShowObservationId] = useState();

  const [loader, setLoader] = useState(false);
  const [headerValue, setHeaderValue] = useState('unsync');
  const headerData = [
    { id: 3, label: 'UNSYNCED', value: 'unsync' },
    { id: 4, label: 'SYNCED', value: 'sync' },
    { id: 4, label: 'RECOVERED', value: 'recovered' },
  ];

  useEffect(() => {
    if (isFocused) {
      loadExclamtion();
      dispatch({
        type: 'CLEAR_PROJECT',
      });
    } else {
      setLoader(false);
    }
  }, [isFocused]);

  useEffect(() => {
    if (!loader) {
      getStudy();
    }
  }, [headerValue, isFocused, netConnection]);

  const checkExcalmation = async () => {
    let userDetail = await getData('currentUser');
    userDetail = JSON.parse(userDetail);
    dispatch(getSyncExclamation(dbDetail, userDetail._id));
  };

  /* GETTING OFFLINE DATA AND SYNCING IT TO THE SERVER */
  const getStudy = async () => {
    try {
      setLoader(true);
      dispatch({ type: 'SYNCDATA_LOADING' });

      await dispatch(syncOfflinePhotos(dbDetail, netConnection));

      let showingSync = headerValue === 'sync';
      const result = await getStudies(dbDetail, userDetails._id, !showingSync);

      if (result && result.length > 0) {
        await getStudyData(result);
      } else {
        dispatch({ type: 'SYNCDATA_LOADING_FALSE' });
        setLoader(false);
      }
    } catch (error) {
      setLoader(false);
      dispatch({ type: 'SYNCDATA_LOADING_FALSE' });
    }
  };

  /* GETTING ALL THE STUDY DATA */
  const getStudyData = async (data) => {
    try {
      setShowDetail(false);
      setShowActivityIndicator(false);
      setShowObservationId();
      const studyData = await Promise.all(
        data.map(async (element) => {
          var fetchdata = [];
          const [location, project, study, answers] = await Promise.all([
            await getLocationByID(element.locationID, element.projectID, dbDetail),
            getProject(element.projectID),
            fetchdata,
            await getDataByTableAndID(tables.ANSWERS_TABLE, element.id, dbDetail),
          ]);
          var data = {
            ...element,
            answerData: answers,
            locationName: !isEmpty(element.locationName)
              ? element.locationName
              : !isEmpty(location)
                ? location[0].locationname
                : ' ',
            data: [],
            customerName: element.customerName ?? project.customer_name,
            project,
          };
          return data;
        })
      );
      const filteredStudyData = studyData.filter((item) => item !== null);
      if (headerValue === 'unsync') {
        setUnsyncObsData(filteredStudyData);
      } else if (headerValue === 'sync') {
        setSyncObsData(filteredStudyData);
      } else if (headerValue === 'recovered') {
        setRecoverObsData(filteredStudyData);
      }

      dispatch({ type: 'SYNCDATA_LOADING_FALSE' });
      setLoader(false);
    } catch (error) {
      setLoader(false);
      dispatch({ type: 'SYNCDATA_LOADING_FALSE' });
    }
  };

  const getObservation = async (studyDetail, index, fromSubmitStudy) => {
    if (!fromSubmitStudy) {
      setShowObservationId(studyDetail.id);
      setShowActivityIndicator(true);
    }
    try {
      var fetchdata =
        headerValue == 'recovered'
          ? getDataByTableAndID(tables.RECOVERY_DATA_TABLE, studyDetail.id, dbDetail)
          : getDataByTableAndID(tables.STUDY_DATA_TABLE, studyDetail.id, dbDetail);
      const [study] = await Promise.all([fetchdata]);
      const studies = await getFormattedData(study, studyDetail);
      if (headerValue === 'unsync') {
        unSyncObsData[index].data = studies;
        setUnsyncObsData([...unSyncObsData]);
      } else if (headerValue === 'sync') {
        if (fromSubmitStudy) {
          selectedStudy[index].data = studies;
          setSelectedStudy([...selectedStudy]);
        } else {
          syncObsData[index].data = studies;
          setSyncObsData([...syncObsData]);
        }
      } else if (headerValue === 'recovered') {
        recoverObsData[index].data = studies;
        setRecoverObsData([...recoverObsData]);
      }
      if (!fromSubmitStudy) {
        setShowDetail(true);
        setShowActivityIndicator(false);
      }
      // dispatch({ type: 'SYNCDATA_LOADING_FALSE' });
      // setLoader(false);
    } catch (error) {
      setLoader(false);
      dispatch({ type: 'SYNCDATA_LOADING_FALSE' });
    }
  };

  const getFormattedData = async (studydatas, studyDetail) => {
    let studydata = studydatas.slice();
    let updatedStudyData = await Promise.all(
      studydata.map(async (observation) => {
        let { areaID, areaName, taskID, taskName, elementID, elementName } = observation;
        if (isEmpty(areaName)) {
          let areas = await getDataByTableAndID('OfflineArea', areaID, dbDetail);
          observation.areaName = !isEmpty(areas) ? areas[0].name : '';
        }
        if (areaID.indexOf('area') > -1) {
          observation.areaData = {
            name: areaName ?? observation.areaName,
            studyTypes: [1, 2, 3],
            projectID: studyDetail.projectID,
            customerID: studyDetail.customerID,
          };
        }

        if (isEmpty(taskName)) {
          let tasks = await getTaskByID(taskID, '', dbDetail);
          observation.taskName = !isEmpty(tasks) ? tasks[0].name : '';
        }

        const ele = !isEmpty(elementID)
          ? (await getDataByTableAndID('OfflineElement', elementID, dbDetail))[0]
          : {};

        let elementData = !isEmpty(ele)
          ? {
              name: ele.name,
              studyTypes: [2, 3],
              type: ele.type,
              rating: ele.rating,
              categoryID: ele.category,
              addedBy: {
                _id: userDetails._id,
                name: `${userDetails.name} ${userDetails.lastname}`,
                date: new Date(),
              },
              projectID: studyDetail.projectID,
              status: 'active',
              userAdded: true,
              taskID: ele.taskID ?? observation.taskID,
              count: ele.count === 1 ? true : false,
              addPosition: ele.addPosition,
              contingencyAllowance: ele.contingencyAllowance,
              relaxationAllowance: ele.relaxationAllowance,
              customerID: studyDetail.customerID,
            }
          : { name: elementName, rating: '', count: '' };

        observation.elementData = elementData;

        return observation;
      })
    );
    return updatedStudyData;
  };

  const getProject = (projectID) => {
    const project = projectList.find((p) => p._id === projectID);
    return project || '';
  };

  const handleSelectStudy = (id) => {
    const itemIndex = selectedStudy.findIndex((n) => n.id === id);
    setSelectedStudy((products) =>
      itemIndex !== -1
        ? products.filter((val) => val.id !== id)
        : [...products, ...syncObsData.filter((n) => n.id === id)]
    );
  };

  const submitSyncStudy = async (exported = false) => {
    setResyncConfirmModal(false);
    if (!netConnection) {
      dispatch({ type: ALERT_ERROR, payload: INTERNET_ERROR });
      return;
    }
    if (headerValue == 'sync' && isEmpty(selectedStudy)) {
      dispatch({ type: ALERT_ERROR, payload: 'Please Select Studies' });
      return;
    }
    setLoader(true);
    dispatch({ type: 'SYNCDATA_LOADING' });
    let syndataTobe =
      headerValue == 'sync'
        ? selectedStudy
        : headerValue == 'recovered'
          ? recoverObsData
          : unSyncObsData;

    const len = syndataTobe.length;
    dispatch({ type: 'ALERT_SHOW', payload: exported ? 'Downloading Data...' : 'Syncing Data...' });
    dispatch({ type: 'STUDY_COUNT', payload: len });
    await Promise.all(
      syndataTobe.map(async (study, index) => {
        if (isEmpty(study.data)) {
          await getObservation(study, index, true);
        }
      })
    );

    dispatch({ type: 'SYNCDATA_LOADING' });
    const os = Platform.OS == 'ios' ? 'IOS' : 'Android';
    const addedBy = {
      _id: userDetails._id,
      name: `${userDetails.name} ${userDetails.lastname}`,
      date: new Date(),
    };

    if (netConnection) {
      syndataTobe.forEach(async (study, index) => {
        const {
          data,
          name,
          studyStartTime,
          studyEndTime,
          customerID,
          projectID,
          locationID,
          answerData,
          id,
          sendStudyToEmail,
          sendProjectStatsReport,
        } = study;
        var payload = {
          name,
          studyStartTime,
          studyEndTime: !isEmpty(studyEndTime) ? studyEndTime : data[data.length - 1].endTime,
          customerID,
          projectID,
          locationID,
          userID: userDetails._id,
          data,
          addedBy,
          roundDuration: studyEndTime - studyStartTime,
          version: VERSION,
          studyType: APP_ID,
          osInfo: os,
          answers: answerData,
          timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
          showLastNote: true,
          sendStudyToEmail: sendStudyToEmail ?? false,
          sendProjectStatsReport: sendProjectStatsReport ?? false,
        };
        // console.log(JSON.stringify(payload), ' payloaddd');
        // return;
        if (exported) {
          try {
            var path =
              Platform.OS === 'ios'
                ? `${RNFS.DocumentDirectoryPath}`
                : `/storage/emulated/0/Android/media/com.crm.reTime.dots/Download`;
            if (Platform.OS === 'android') {
              RNFS.mkdir(path);
            }
            path += `/study${payload.name}.txt`;
            const a = await RNFS.writeFile(path, JSON.stringify(payload), 'utf8')
              .then((success) => {
                console.log('Succeaass', success);
                dispatch({
                  type: ALERT_SUCCESS,
                  payload:
                    Platform.OS === 'ios'
                      ? 'Successfully downloaded'
                      : 'Successfully download to Android/Media',
                });
              })
              .catch((err) => {
                console.log(err.message, 'err');
              });
            // console.log(a,syndataTobe.length - 1 == index,' after download')
            if (syndataTobe.length - 1 == index) {
              console.log('ladoing false');
              setLoader(false);
              dispatch({ type: 'SYNCDATA_LOADING_FALSE' });
            }
          } catch (error) {
            Alert.alert('Error', 'Failed to save file');
            console.error(error);
          }
        } else {
          dispatch(submitStudyAction(payload, dbDetail, id));
          if (syndataTobe.length - 1 == index) {
            dispatch({ type: 'SYNCDATA_LOADING_FALSE' });
            setLoader(false);
            checkExcalmation();
          }
        }
      });
    } else {
      setLoader(false);
      dispatch({ type: 'SYNCDATA_LOADING_FALSE' });
      dispatch({ type: ALERT_ERROR, payload: INTERNET_ERROR });
    }
  };

  useEffect(() => {
    if (dataSyncCompleted) {
      syncDataCompleted();
    }
  }, [dataSyncCompleted]);

  const syncDataCompleted = async () => {
    dispatch({ type: 'SYNCDATA_LOADING' });
    if (headerValue !== 'sync' && !isEmpty(noOfStudySynced)) {
      getStudy();
      setRecoverObsData([]);
      setUnsyncObsData([]);
      loadExclamtion();
    }
    setSelectedStudy([]);
    setLoader(false);
    dispatch({ type: 'RESET_SUBMITTED' });
    dispatch({ type: ALERT_HIDE });
    dispatch({ type: 'SYNCDATA_LOADING_FALSE' });

    dispatch({ type: ALERT_SUCCESS, payload: 'All the data synced successfully' });
  };

  const handleResubmit = () => {
    if (isEmpty(selectedStudy)) {
      dispatch({
        type: ALERT_ERROR,
        payload: 'Please select studies..',
      });
      return;
    } else {
      setResyncConfirmModal(true);
    }
  };
  const ItemView = ({ item, index }) => {
    return (
      <View key={Math.random()} style={styles.container}>
        <SyncCard
          navigation={navigation}
          syncCheckBox={headerValue == 'sync'}
          ISselected={
            !isEmpty(selectedStudy) && selectedStudy.findIndex((n) => n.id === item.id) !== -1
              ? true
              : false
          }
          selectstudy={() => {
            handleSelectStudy(item.id);
          }}
          showEditButton={headerValue == 'unsync'}
          data={item}
          showObservations={(id) => {
            showDetail && (setShowDetail(false), setShowObservationId(''));
            !isEmpty(item.data) && showObservationId !== id
              ? (setShowDetail(true), setShowObservationId(id))
              : isEmpty(item.data) && showObservationId !== id
                ? getObservation(item, index)
                : null;
          }}
          showDetail={showDetail}
          showObservationId={showObservationId}
          showActivityIndicator={showActivityIndicator}
        />
      </View>
    );
  };

  const renderFlatListOrNoData = () => {
    const dataToRender =
      headerValue === 'unsync'
        ? unSyncObsData
        : headerValue === 'sync'
          ? syncObsData
          : recoverObsData;
    const noDataMessage =
      headerValue === 'recovered'
        ? `There are no ${headerValue} studies.`
        : `There are no ${headerValue}ed studies. You can see your ${
            headerValue === 'sync' ? 'unsynced' : 'synced'
          } studies in the other tab.`;

    return !loader && dataToRender && dataToRender.length > 0 ? (
      <FlatList
        style={styles.flatListContainer}
        contentContainerStyle={{ paddingBottom: 55 }}
        data={dataToRender}
        showsVerticalScrollIndicator={false}
        showsHorizontalScrollIndicator={false}
        keyExtractor={(key, index) => index.toString()}
        renderItem={ItemView}
      />
    ) : (
      <NoDataFound
        imageStyle={[styles.imageStyle, { tintColor: colors.primary }]}
        source={localimage.nounsync}
        text={noDataMessage}
      />
    );
  };

  const renderFabButton = (type) => {
    const showFabButton =
      (headerValue === 'unsync' && !isEmpty(unSyncObsData)) ||
      (headerValue === 'sync' && !isEmpty(syncObsData)) ||
      (headerValue === 'recovered' && !isEmpty(recoverObsData) && netConnection && !loader);

    return showFabButton ? (
      <FabButton
        icons={type === 'download' ? 'cloud-download-outline' : 'cloud-upload-outline'}
        onClick={() => {
          headerValue == 'sync' && type !== 'download'
            ? handleResubmit()
            : submitSyncStudy(type === 'download' ? true : false);
        }}
        style={type === 'download' ? styles.fab2 : styles.fab}
      />
    ) : null;
  };
  return (
    <>
      <Card style={[styles.projectCardstyle, { shadowColor: colors.primary }]} contentContainer>
        <View style={[styles.HeaderContainer]}>
          {!isEmpty(headerData) &&
            headerData.map(({ label, value }) => (
              <AButton
                key={Math.random()}
                onPress={() => {
                  setShowingSynced(!showingSynced), setHeaderValue(value);
                }}
                btnStyle={{
                  ...styles.headerButtonStyle,
                  backgroundColor: headerValue == value ? colors.primary : 'transparent',
                }}
                fontWeight={FontStyle.fontBold}
                styleText={{
                  ...styles.lineHeight,
                  textAlign: 'center',
                  color: headerValue == value ? '#fff' : colors.primary,
                }}
                fontSize={'medium'}
                title={`${label}\nSTUDIES`}
              />
            ))}
        </View>
      </Card>
      <ScrollView
        showsVerticalScrollIndicator={false}
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.contentContainerStyle}
        style={styles.Viewcontainer}
      >
        {renderFlatListOrNoData()}
      </ScrollView>
      <BottomModal
        modalShow={resyncConfirmModal}
        confirmPress={() => submitSyncStudy()}
        cancelShow={() => setResyncConfirmModal(false)}
        title={sync_studies}
        body={resync_confirmation}
        confirm={'yes'}
        reject={' Cancel '}
      />
      {renderFabButton()}
      {showDownload ? renderFabButton('download') : null}
    </>
  );
};

export default SyncScreen;

const styles = StyleSheet.create({
  contentContainerStyle: {
    padding: 15,
    width: '100%',
    flexGrow: 1,
    alignSelf: 'center',
  },
  projectCardstyle: {
    width: '90%',
    alignSelf: 'center',
    justifyContent: 'center',
    shadowColor: 'rgba(0,0,0,0.5)',
    shadowOffset: {
      width: 4,
      height: 5,
    },
    borderRadius: 75,
    shadowOpacity: 0.5,
    shadowRadius: 7.5,
    elevation: Platform.OS == 'ios' ? 2 : 15,
    borderWidth: 1,
    borderColor: '#fff',
    marginTop: 20,
    borderRadius: 75,
    paddingHorizontal: 7,
    paddingVertical: 7,
  },
  HeaderContainer: {
    flexDirection: 'row',
    alignSelf: 'center',
    justifyContent: 'space-evenly',
    alignItems: 'center',
  },
  headerButtonStyle: {
    paddingVertical: 7,
    width: '34%',
    paddingHorizontal: 19,
    justifyContent: 'center',
    alignContent: 'center',
    alignItems: 'center',
    borderRadius: 35,
  },
  contentContainer: {
    width: '100%',
    marginTop: 25,
    alignItems: 'center',
    flex: 1,
    alignSelf: 'center',
    justifyContent: 'center',
  },
  fab: {
    position: 'absolute',
    marginHorizontal: 5,
    right: 0,
    bottom: 45,
    padding: 5,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 200,
  },
  fab2: {
    position: 'absolute',
    marginHorizontal: 5,
    right: 75,
    bottom: 45,
    padding: 5,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 200,
  },
  container: {
    alignItems: 'center',
    width: '100%',
  },
  flatListContainer: {
    // alignItems: 'center',
    width: '100%',
    alignSelf: 'center',
    flex: 1,
    alignSelf: 'center',
    paddingBottom: 85,
  },
  imageStyle: {
    resizeMode: 'contain',
    width: 350,
    height: 350,
    marginVertical: 15,
  },
  Viewcontainer: {
    width: '100%',
    marginTop: 10,
    flex: 1,
    marginBottom: 10,
  },
  lineHeight: {
    lineHeight: 26,
  },
});
