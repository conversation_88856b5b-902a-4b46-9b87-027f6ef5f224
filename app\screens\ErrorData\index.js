import { deleteRecord, getAllData, insertIntoTable } from '_helpers';
import { APP_PRIMARY_COLOR, APP_SECONDARY_COLOR, tables } from '_utils';
import React, { useEffect, useState } from 'react';
import { Platform, ScrollView, TouchableOpacity } from 'react-native';
import { View, Text, StyleSheet } from 'react-native';
import { useSelector } from 'react-redux';
import { Appbar, useTheme } from 'react-native-paper';

const ErrorData = ({ navigation }) => {
  const [erros, setErrors] = useState([]);
  const { colors, dark } = useTheme();
  const { dbDetail } = useSelector((state) => state.user);
  const handleDataClear = () => {
    deleteRecord(tables.ERROR_TABLE, [], dbDetail);
    setErrors([]);
  };

  useEffect(() => {
    (async () => {
      try {
        const a = await getAllData(tables.ERROR_TABLE, dbDetail);
        setErrors(a);
      } catch (e) {}
    })();
  }, []);

  return (
    <ScrollView style={{ ...styles.container, backgroundColor: !dark ? '#fff' : colors.onSurface }}>
      <TouchableOpacity
        onPress={() => handleDataClear()}
        style={{
          width: 150,
          position: 'absolute',
          backgroundColor: 'rgba(12, 159, 239,1)',
          padding: 10,
          zIndex: 2,
          right: 60,
          top: 20,
          borderRadius: 30,
          marginHorizontal: 30,
        }}
      >
        <Text style={{ textAlign: 'center', color: '#fff' }}>Clear Error Log</Text>
      </TouchableOpacity>
      <Appbar.BackAction
        size={Platform.OS == 'ios' ? 20 : 30}
        color={colors.primary}
        style={[
          styles.backIconStyle,
          { backgroundColor: dark ? 'rgba(0, 0, 0, 0.5)' : 'rgba(255, 255, 255, 0.5)' },
        ]}
        onPress={() => navigation.goBack()}
      />
      <Text
        style={{
          ...styles.text,
          color: !dark ? '#000' : '#fff',
          marginBottom: 15,
          paddingLeft: 40,
        }}
      >
        Errors Log
      </Text>
      <View
        style={{
          width: '85%',
          backgroundColor: 'white',
          borderWidth: 0.5,
          borderColor: 'lightgray',
          alignSelf: 'center',
          paddingVertical: 10,
        }}
      >
        <View style={{ flexDirection: 'row', marginHorizontal: 30 }}>
          <Text style={{ width: '40%', fontWeight: 'bold', color: 'gray' }}>Error Message</Text>
          <Text style={{ width: '30%', fontWeight: 'bold', color: 'gray' }}>Error From</Text>
          <Text style={{ width: '20%', fontWeight: 'bold', color: 'gray' }}>Time</Text>
        </View>
        {erros.map((item) => (
          <View style={{ flexDirection: 'row', marginHorizontal: 30 }}>
            <Text style={{ width: '40%', color: 'gray' }}>{item.errorMsg ?? '-'}</Text>
            <Text style={{ width: '30%', color: 'gray' }}>{item.errorFrom}</Text>
            <Text style={{ width: '20%', color: 'gray' }}>{item.time}</Text>
          </View>
        ))}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
  },
  text: {
    marginHorizontal: 30,
    fontSize: 24,
    fontWeight: 'bold',
  },
  backIconStyle: {
    marginHorizontal: 30,
    height: Platform.OS == 'ios' ? 50 : 60,
    width: Platform.OS == 'ios' ? 50 : 60,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default ErrorData;
