import React, { createContext, useState } from 'react';
import { Provider as PaperProvider } from 'react-native-paper';
import { reTimeDarkTheme, reTimeTheme } from '_utils';

const AppThemeContext = createContext(null);

const AppThemeProvider = ({ children }) => {
    const [theme, setTheme] = useState(reTimeTheme);
    const [themeLoader, setThemeloader] = useState(false);

    const themeChange = (val) => {
        setThemeloader(true);
        setTheme(val);
        setTimeout(() => {
            setThemeloader(false);
        }, 1000);
    };
    return (
        <AppThemeContext.Provider
            value={{
                theme,
                setTheme,
                themeChange,
                themeLoader,
            }}
        >
            <PaperProvider theme={theme}>{children}</PaperProvider>
        </AppThemeContext.Provider>
    );
};
export { AppThemeContext, AppThemeProvider };
