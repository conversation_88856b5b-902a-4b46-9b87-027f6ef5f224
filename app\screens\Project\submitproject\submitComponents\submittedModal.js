import { Image, StyleSheet, Text, View } from 'react-native';
import React from 'react';
import { AButton, AText, BottomModalUI, LinearGradientButton } from '_theme_components';
import { useTheme } from 'react-native-paper';
import { FontStyle, localimage, windowHeight, windowWidth } from '_utils';
import Icon from 'react-native-vector-icons/Ionicons';

const SubmittedModal = ({
  submitedModalShow,
  netConnection,
  submitHeader,
  submitContent,
  navigateNext,
}) => {
  const { colors, dark } = useTheme();

  return (
    <BottomModalUI
      width={'100%'}
      backgroundColor={dark ? colors.onSurface : '#fff'}
      ModalClose={false}
      height={windowHeight}
      modalShow={submitedModalShow}
    >
      <View style={[styles.modalCointainer]}>
        <Image
          source={netConnection ? localimage.submitteddata : localimage.submittingdataFailed}
          style={[
            styles.submitStyle,
            {
              height: windowWidth > windowHeight ? windowHeight * 0.55 : windowWidth * 0.55,
              width: windowWidth > windowHeight ? windowHeight * 0.65 : windowWidth * 0.55,
            },
          ]}
        />
        <View
          style={[
            styles.SubmitedView,
            {
              width: windowWidth > windowHeight ? windowWidth * 0.5 : windowWidth * 0.67,
            },
          ]}
        >
          <AText fontSize={'title'} fontWeight={FontStyle.fontBold}>
            {submitHeader}
          </AText>
          <AText
            fontSize={'medium'}
            styleText={{
              paddingBottom: windowWidth > windowHeight ? 5 : 15,
              paddingTop: 10,
            }}
            fontWeight={FontStyle.fontMedium}
          >
            {submitContent}
          </AText>
          <LinearGradientButton
            onPress={() => {
              navigateNext('project');
            }}
            title="Return to Projects"
            btnStyle={{ width: windowWidth * 0.4 }}
            contentStyles={{ paddingVertical: 13 }}
          />

          <AButton
            // bgColor={'#fff'}
            mode={'text'}
            onPress={() => {
              navigateNext('location');
            }}
            icon={
              <Icon name="arrow-back" style={styles.iconStyle} color={colors.primary} size={22} />
            }
            btnStyle={{
              ...styles.locationBtnStyle,
              marginTop: windowHeight > 1000 ? 30 : 10,
            }}
            title={'RETURN TO Locations'}
            fontSize={'medium'}
            styleText={{ color: colors.primary }}
          />
        </View>
      </View>
    </BottomModalUI>
  );
};

export default SubmittedModal;

const styles = StyleSheet.create({
  modalCointainer: {
    flex: 1,
    justifyContent: 'center',
  },
  submitStyle: {
    resizeMode: 'contain',
    alignSelf: 'center',
  },
  SubmittingView: {
    // marginVertical: 20,
    alignSelf: 'center',
    paddingTop: 25,
  },
  SubmitedView: {
    // marginVertical: 20,
    alignSelf: 'center',
  },
  selectionBotttomBar: {
    alignSelf: 'center',
    position: 'absolute',
    bottom: 25,
    backgroundColor: 'rgba(60, 69, 86,0.5)',
    // backgroundColor: 'rgba(80, 88, 101,1)',
    borderRadius: 40,
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  locationBtnStyle: {
    alignItems: 'center',
  },
  iconStyle: {
    alignSelf: 'center',
    marginRight: 0,
  },
  iconBtntyle: {
    marginRight: 10,
    justifyContent: 'center',
    borderRadius: 40,
    height: 60,
    width: 60,
  },
});
