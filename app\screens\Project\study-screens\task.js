import { StyleSheet, View } from 'react-native';
import React, { useCallback, useEffect, useState } from 'react';
import { useTheme } from 'react-native-paper';
import { useDispatch, useSelector } from 'react-redux';
import { SortingModal } from './components';
import { AText, NoDataFound, StudyHeader, StudyNextCard } from '_theme_components';
import { FontStyle, INTERNET_ERROR, localimage, tables, windowHeight, windowWidth } from '_utils';
import { dynamicSort, isEmpty } from '_helpers';
import { fetchGroup, pullServerData } from '_action';

import { ALERT_ERROR } from '../../../store/reducers/alert';
import { getTaskDataFetch } from '_provider';

const TaskScreen = ({
  navigation,
  navigateTo,
  onRefreshPage,
  setLoader,
  loader,
  setRefreshFalse,
}) => {
  const { colors } = useTheme();
  const dispatch = useDispatch();

  const project = useSelector((state) => state.createStudy.projectSelect);
  const { dbDetail } = useSelector((state) => state.user);
  const { netConnection } = useSelector((state) => state.netInfo);
  const { taskList } = useSelector((state) => state.serverReducer);

  const [showSearch, setShowSearch] = useState(true);
  const [sortModalShow, setSortModalShow] = useState(false);
  const [showGroup, setShowGroup] = useState(true);

  const [showTask, setShowTask] = useState([]);
  const [tasks, setTasks] = useState({
    taskData: [],
    unGroupTask: [],
  });
  const [orderTask, setOrderTask] = useState('');
  const [taskSearchInput, setTaskSearchInput] = useState('');

  useEffect(() => {
    if (taskList) {
      let taskListData = [];
      taskList.map((val) => {
        taskListData.push(...val.entries);
      });
      setTasks({
        taskData: taskList,
        unGroupTask: taskListData,
      });
    }
  }, [taskList]);

  const getTaskData = async (refresh) => {
    if (isEmpty(refresh)) {
      setLoader(true);
    }
    await dispatch(pullServerData(tables.TASKS_TABLE, project._id, dbDetail));
    await dispatch(getTaskDataFetch(project._id, dbDetail));
    setLoader(false);
  };
  const navigateNext = (item) => {
    dispatch({
      type: 'STORE_TASK',
      payload: item,
    });
    navigateTo('element');
  };
  const handleShowGrpTask = (name) => {
    if (showTask.includes(name)) {
      setShowTask((products) => products.filter((val) => val !== name));
    } else {
      setShowTask([...showTask, name]);
    }
  };

  const onChangeSearch = async (value) => {
    var taskEmtries = [];
    let items = taskList;
    if (value !== '') {
      items.map((item) => {
        item.entries.filter((val) => {
          let insertEntries = [];
          if (val.name.toLowerCase().includes(value.toLowerCase())) {
            insertEntries.push(val);
          }
          if (!isEmpty(insertEntries)) {
            taskEmtries.push({
              entries: insertEntries,
              name: item.name,
              ungrouped: item.ungrouped,
            });
          }
        });
      });
      setTasks({
        ...tasks,
        taskData: taskEmtries,
      });
      setTaskSearchInput(value);
    } else {
      setTasks({
        ...tasks,
        taskData: taskList,
      });
      setTaskSearchInput(value);
    }
  };
  useEffect(() => {
    if (onRefreshPage) {
      onRefresh();
    }
  }, [onRefreshPage]);

  const onRefresh = React.useCallback(async () => {
    if (!netConnection) {
      dispatch({
        type: ALERT_ERROR,
        payload: INTERNET_ERROR,
      });
    } else {
      Promise.all([dispatch(fetchGroup(project._id, dbDetail))]).then(() => {
        getTaskData(true);
      });
      setTimeout(() => {
        setShowSearch(false);
      }, 3000);
    }
    setRefreshFalse();
  }, []);
  const toggleOrdering = useCallback(
    (type) => {
      setOrderTask(type);
      const taskListData = taskList.reduce((acc, val) => {
        if (val.ungrouped || !showGroup) return [...acc, ...val.entries];
        return acc;
      }, []);

      const sortedObservations = taskListData.sort(dynamicSort(type === 'asec' ? 'name' : '-name'));

      if (showGroup) {
        setTasks((prevTasks) => ({
          ...prevTasks,
          taskData: prevTasks.taskData.map((val) =>
            val.ungrouped ? { ...val, entries: sortedObservations } : val
          ),
        }));
      } else {
        setTasks((prevTasks) => ({ ...prevTasks, unGroupTask: sortedObservations }));
      }
    },
    [taskList, showGroup]
  );

  const GroupToggle = useCallback(
    (type) => {
      if (orderTask) toggleOrdering(orderTask);
      setShowGroup(type);
      setSortModalShow(false);
    },
    [orderTask, toggleOrdering]
  );

  return (
    <>
      <StudyHeader
        ShowMagnify={true}
        OnPressIcon={() => setSortModalShow(true)}
        navigation={navigation}
        Title={'Tasks'}
        bachkHandler={() => {
          navigateTo('area');
        }}
        alignself={'flex-start'}
        SubTitle={'Select tasks'}
        showBack={true}
        OnChangeSearch={(val) => onChangeSearch(val)}
        searchPress={() => {
          setShowSearch(!showSearch), onChangeSearch('');
        }}
        showSearch={showSearch}
        showSorting={!showSearch}
      />
      {showGroup && (
        <View onStartShouldSetResponder={() => true} style={styles.container}>
          {!isEmpty(tasks.taskData) &&
            tasks.taskData.map(
              (item) =>
                !item.ungrouped && (
                  <View key={Math.random()}>
                    <StudyNextCard
                      data={item}
                      taskGroup
                      ShowTasks={showTask.includes(item.name)}
                      icon={'chevron-down'}
                      navigateNext={() => handleShowGrpTask(item.name)}
                    />
                    {!isEmpty(item.entries) &&
                      showTask.includes(item.name) &&
                      item.entries.map((entry, index) => {
                        return (
                          <View key={Math.random()}>
                            <StudyNextCard
                              data={entry}
                              lasttask={item.entries.length - 1 == index ? true : false}
                              taskGroupElement
                              navigateNext={() => navigateNext(entry)}
                            />
                          </View>
                        );
                      })}
                  </View>
                )
            )}
        </View>
      )}
      <View onStartShouldSetResponder={() => true} style={styles.container}>
        {showGroup && !isEmpty(tasks.taskData) ? (
          <>
            <View style={styles.taskHeaderStyle}>
              <AText
                fontWeight={FontStyle.fontBold}
                styleText={{ color: '#c7c7c7' }}
                fontSize={'title'}
              >
                Tasks
              </AText>
            </View>
            {tasks.taskData.map(
              (item) =>
                item.ungrouped && (
                  <>
                    {!isEmpty(item.entries) &&
                      item.entries.map((entry) => (
                        <View key={Math.random()}>
                          <StudyNextCard data={entry} navigateNext={() => navigateNext(entry)} />
                        </View>
                      ))}
                  </>
                )
            )}
          </>
        ) : !showGroup && !isEmpty(tasks.taskData) ? (
          tasks.unGroupTask.map((item) => (
            <View key={Math.random()}>
              <StudyNextCard data={item} navigateNext={() => navigateNext(item)} />
            </View>
          ))
        ) : !isEmpty(taskList) &&
          isEmpty(tasks.taskData) &&
          !loader &&
          !isEmpty(taskSearchInput) ? (
          <NoDataFound
            source={localimage.emptydata}
            text={'Sorry, there is no task with that name'}
            imageStyle={{
              height: windowHeight * 0.22,
              width: windowWidth * 0.6,
              marginTop: 55,
              tintColor: colors.primary,
            }}
          />
        ) : isEmpty(taskList) && isEmpty(tasks.taskData) && !loader ? (
          <View style={{ justifyContent: 'center', alignSelf: 'center', marginTop: 15 }}>
            <AText
              fontWeight={FontStyle.fontBold}
              styleText={{ color: '#c7c7c7' }}
              fontSize={'title'}
            >
              No Task Found
            </AText>
          </View>
        ) : null}
      </View>
      {/* ----------------------------Sort element modal------------------------------------------- */}
      <SortingModal
        closeModal={() => setSortModalShow(false)}
        sortModalShow={sortModalShow}
        title={'tasks'}
        showSwitch={true}
        showGroup={showGroup}
        orderTask={orderTask}
        GroupToggle={() => GroupToggle(!showGroup)}
        toggleOrdering={(val) => {
          toggleOrdering(val);
        }}
      />
      {/* ----------------------------sort element modal end------------------------------------------- */}
    </>
  );
};

export default TaskScreen;

const styles = StyleSheet.create({
  container: {
    width: '87%',
    marginTop: 15,
    alignSelf: 'center',
  },
  taskHeaderStyle: {
    justifyContent: 'flex-start',
    alignSelf: 'center',
    width: '82%',
    marginTop: 15,
  },
});
