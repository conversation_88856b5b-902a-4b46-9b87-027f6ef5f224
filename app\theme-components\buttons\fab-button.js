import { StyleSheet, Text, View } from 'react-native';
import React from 'react';
import { FAB } from 'react-native-paper';
import { windowHeight } from '_utils';
import Icon from 'react-native-vector-icons/Ionicons';
import PropTypes from 'prop-types';

const FabButton = ({ onClick, style, icons, showIcon, ICONS }) => {
  FabButton.propTypes = {
    onClick: PropTypes.func,
    icons: PropTypes.string,
    style: PropTypes.object,
    showIcon: PropTypes.bool,
    ICONS: PropTypes.object,
  };

  return (
    <FAB
      icon={() =>
        showIcon ? ICONS : <Icon name={icons} size={24} color={'#fff'} style={styles.iconStyle} />
      }
      mode="elevated"
      animated={false}
      size={'medium'}
      style={style ?? styles.fab}
      onPress={() => onClick()}
    />
  );
};

export default FabButton;

const styles = StyleSheet.create({
  fab: {
    position: 'absolute',
    right: 0,
    bottom: windowHeight * 0.015,
    padding: 5,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: windowHeight * 0.05,
  },
  iconStyle: {
    justifyContent: 'center',
    alignSelf: 'center',
    position: 'absolute',
    // bottom: 1,
    alignItems: 'center',
  },
});
