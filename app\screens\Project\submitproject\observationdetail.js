import { Image, StyleSheet, Text, Pressable, View, Dimensions, Platform } from 'react-native';
import React, { useEffect, useState } from 'react';
import Icon from 'react-native-vector-icons/Feather';
import FontAwesome from 'react-native-vector-icons/FontAwesome5';
import AntDesign from 'react-native-vector-icons/AntDesign';
import moment from 'moment';
import { useIsFocused } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';

import {
  AText,
  Textinputs,
  AuthLoading,
  BottomModalUI,
  CreateStudyLayout,
  IconButton,
  LinearGradientButton,
  StudyNextCard,
  SubmitHeader,
  RatingButton,
} from '_theme_components';

import {
  DELETE_MSG,
  DELETE_PHOTO_MSG,
  DELETE_TITLE,
  FontStyle,
  INTERNET_ERROR,
  LINEAR_GRADIENT_RATING_BUTTON,
  NO_DATA_FOUND,
  changeWindowHeightWidth,
  tables,
  windowHeight,
  windowWidth,
} from '_utils';
import {
  deleteRecord,
  getDataByTableAndID,
  getElements,
  getURL,
  isEmpty,
  isJson,
  updateTable,
} from '_helpers';
import { ALERT_ERROR } from '../../../store/reducers/alert';
import { useTheme } from 'react-native-paper';
import LinearGradient from 'react-native-linear-gradient';

import { BottomModal } from '_components';
import { getElementDataFetch } from '_provider';
import ImageView from 'react-native-image-viewing';
import { TouchableOpacity } from 'react-native';

const FieldTODisplay = [
  { id: 1, name: 'Area', value: 'areaName' },
  { id: 2, name: 'Task', value: 'taskName' },
  { id: 3, name: 'Element', value: 'elementName' },
  { id: 4, name: 'Rate', value: 'rating' },
  { id: 4, name: 'Frequency', value: 'frequency' },
  { id: 4, name: 'Notes', value: 'notes' },
  { id: 4, name: 'Photo', value: 'photo' },
];

const ObservationDetail = ({ navigation, route }) => {
  const { obsData, fromSyncPage } = route.params;
  const { colors, dark } = useTheme();
  const isFocused = useIsFocused();
  const dispatch = useDispatch();

  const { dbDetail } = useSelector((state) => state.user);
  const { netConnection } = useSelector((state) => state.netInfo);
  const { areaList, allElementList, taskAllList } = useSelector((state) => state.serverReducer);

  const [modalShow, setModalShow] = useState(false);
  const [mediaUrl, setMediaUrl] = useState('');
  const [loader, setLoader] = useState(false);
  const [deleteObsModal, setDeleteObsModal] = useState(false);
  const [deletePhotoModal, setDeletePhotosModal] = useState(false);

  const [modalHeader, setModalHeader] = useState('');
  const [selectedRating, setSelectedRating] = useState('100');
  const [freqKeyPressed, setFreqKeyPressed] = useState();
  const [editNotes, setEditNotes] = useState();

  const [observeData, setObservationData] = useState([]);
  const [taskData, setTaskData] = useState([]);
  const [areaData, setAreaData] = useState([]);
  const [elementData, setElementData] = useState([]);
  const [freqInput, setFreqInput] = useState([]);
  const [size, setSize] = useState([]);

  const rate = 60;
  const n = 14;
  const freq = 12;

  useEffect(() => {
    const updateWindowHeight = () => {
      const newWindowHeight = Dimensions.get('window').height;
      const newWindowWidth = Dimensions.get('window').width;
      changeWindowHeightWidth(newWindowHeight, newWindowWidth);
      setSize(newWindowWidth);
    };

    Dimensions.addEventListener('change', updateWindowHeight);

    return () => {
      // Dimension.remove('change', updateWindowHeight);
    };
  }, []);

  useEffect(() => {
    if (isFocused) {
      if (!isEmpty(obsData)) {
        setObservationData(obsData);
        setSelectedRating(obsData.rating);
        setEditNotes(obsData.notes);
      }
      if (isEmpty(areaList) || (isEmpty(taskAllList) && fromSyncPage)) {
        getData(obsData.elementData.projectID);
      } else {
        setAreaData(areaList);
        setTaskData(taskAllList);
      }
    }
  }, [isFocused]);

  const getData = async (projectID) => {
    setLoader(true);
    var areasdata = await getDataByTableAndID(tables.AREAS_TABLE, projectID, dbDetail);
    var taskdata = await getDataByTableAndID(tables.TASKS_TABLE, projectID, dbDetail);
    dispatch(getElementDataFetch(dbDetail, projectID));
    Promise.all([areasdata, taskdata])
      .then(async (data) => {
        if (!isEmpty(data)) {
          setAreaData(data[0]);
          setTaskData(data[1]);
        } else {
          dispatch({
            type: ALERT_ERROR,
            payload: NO_DATA_FOUND,
          });
        }

        setLoader(false);
      })
      .catch((error) => {
        setLoader(false);
        return;
      });
  };
  useEffect(() => {
    if (!isEmpty(observeData)) {
      setTimeout(() => {
        getElementData();
      }, 1200);
    }
  }, [allElementList, observeData]);

  const getElementData = async () => {
    let taskID = observeData.taskID ?? obsData.taskID;
    let elementdata = [];
    if (allElementList.length > 0) {
      let elements = allElementList.find((item) => item.taskID == taskID);
      if (!isEmpty(elements?.grouped_data)) {
        elementdata = isJson(elements?.grouped_data)
          ? JSON.parse(elements?.grouped_data)
          : elements?.grouped_data;
      }
    }
    setElementData(elementdata);
  };
  const editObservation = (item) => {
    setModalShow(false);
    if (modalHeader == 'Notes') {
      observeData.notes = item;
    }
    if (modalHeader == 'Area') {
      observeData.areaID = item._id;
      observeData.areaName = item.name;
    } else if (modalHeader == 'Task') {
      observeData.taskID = item._id;
      observeData.taskName = item.name;
      observeData.elementID = '';
      observeData.elementName = '';
      observeData.elementData = '';
    } else if (modalHeader == 'Element') {
      observeData.elementID = item._id;
      observeData.elementName = item.name;
      observeData.elementData = item;
      if (!item.count) {
        setFreqInput(1);
        observeData.frequency = 1;
      }
      if (item.rating === 2 || item.rating === 1) {
        observeData.rating = item.rating == 1 ? 'Not Rated' : 100;
        setSelectedRating(item.rating == 1 ? 'Not Rated' : 100);
      }
    } else if (modalHeader == 'Rate') {
      setSelectedRating(item === 125 ? 'Not Rated' : item);

      observeData.rating = item === 125 ? 'Not Rated' : item;
    } else if (modalHeader == 'Photo') {
      observeData.photo = '';
      setDeletePhotosModal(false);
    } else if (modalHeader == 'Frequency') {
      observeData.frequency = item;
    }
    setObservationData(observeData);
    if (modalHeader == 'Task') {
      getElementData();
    }
  };
  const deleteFromSQLite = async () => {
    setDeleteObsModal(false);
    await deleteRecord(tables.STUDY_DATA_TABLE, { id: observeData.id }, dbDetail)
      .then((result) => {
        navigation.goBack();
      })
      .catch((error) => {});
  };

  /* UPDATING STUDY DATA */
  const updateStudyData = () => {
    if (loader) {
      return;
    }
    if (isEmpty(observeData.elementID)) {
      dispatch({
        type: ALERT_ERROR,
        payload: 'Please select element',
      });
      return;
    }
    setLoader(true);
    let data = {
      area: null,
      element: null,
      task: null,
      rating: null,
      frequency: null,
      id: null,
      notes: '',
      photo: '',
    };
    /* CHEDKING AREA*/
    if (observeData.areaID) {
      data.area = observeData.areaID;
      data.areaName = observeData.areaName;
    }
    /* CHECKING TASk*/
    if (observeData.taskID) {
      data.task = observeData.taskID;
      data.taskName = observeData.taskName;
    }
    /* CHECKING ELEMENT */
    if (observeData.elementID) {
      data.element = observeData.elementID;
      data.elementName = observeData.elementName;
      if (!observeData.elementData.count) {
        observeData.frequency = 1;
      }
      if (observeData.elementData.rating === 1) {
        observeData.rating = 'Not Rated';
      } else if (observeData.elementData.rating === 2) {
        observeData.rating = 100;
      }
    }
    data.notes = observeData.notes;
    data.photo = JSON.stringify(observeData.photo);
    data.photoLocal = JSON.stringify(observeData.photoLocal);

    /* SETTING RATING*/
    data.rating = observeData.rating;

    /* SETTING FREQUENCY*/
    data.frequency = observeData.frequency;

    data.id = observeData.id;
    updateTable(tables.STUDY_DATA_TABLE, null, data, dbDetail)
      .then((result) => {
        setTimeout(() => {
          setLoader(false);
        }, 900);

        navigation.goBack();
      })
      .catch((error) => {
        setLoader(false);
      });
  };
  // deleteFromSQLite

  return (
    <>
      {loader ? <AuthLoading /> : null}
      <SubmitHeader
        showBack={true}
        ObservationScreen
        ShowTrash={true}
        deleteData={() => {
          setDeleteObsModal(true);
        }}
        savechanges={() => {
          updateStudyData();
        }}
        navigation={navigation}
        SubTitle={!isEmpty(obsData) ? obsData.elementName : ''}
        Title={'Observation'}
      >
        {!isEmpty(observeData) && (
          <View style={styles.syncCardstyle}>
            {!isEmpty(FieldTODisplay) &&
              FieldTODisplay.map((key) =>
                (key.value !== 'notes' && key.value !== 'photo') ||
                (key.value == 'notes' && !isEmpty(observeData.notes)) ||
                (key.value == 'photo' && !isEmpty(observeData.photo)) ? (
                  <View key={Math.random()} style={styles.divstyle}>
                    <View style={{ width: '80%' }}>
                      <AText
                        fontWeight={FontStyle.fontMedium}
                        styleText={{ color: '#828181' }}
                        fontSize={'medium'}
                      >
                        {key.name}
                      </AText>
                      <View style={{ flexDirection: 'row', flexWrap: 'wrap' }}>
                        {key.value == 'photo' &&
                          !isEmpty(observeData['photoLocal']) &&
                          observeData['photoLocal'].split(',').map((item) => (
                            <TouchableOpacity onPress={() => setMediaUrl(item)}>
                              <Image
                                source={{
                                  uri: `${item}`,
                                }}
                                style={{
                                  width: 100,
                                  height: 100,
                                  marginTop: 10,
                                  marginRight: 10,
                                }}
                              />
                            </TouchableOpacity>
                          ))}
                      </View>
                      <AText
                        styleText={{ paddingTop: 5 }}
                        fontWeight={FontStyle.fontBold}
                        fontSize={'medium'}
                      >
                        {key.value !== 'photo' ? observeData[key.value] : null}
                      </AText>
                      {key.value == 'frequency' && !observeData.elementData.count && (
                        <AText
                          styleText={{ color: '#828181', paddingTop: 5 }}
                          fontWeight={FontStyle.fontMedium}
                          fontSize={'small'}
                        >
                          Note: Frequency can't be edited for this Elements{' '}
                        </AText>
                      )}
                    </View>
                    {(key.value == 'frequency' && !observeData.elementData.count) ||
                    (key.value == 'rating' &&
                      (observeData.elementData.rating == 2 ||
                        observeData.elementData.rating == 1)) ? null : key.value === 'photo' ? (
                      <IconButton
                        onPress={() => {
                          setModalHeader(key.name), setDeletePhotosModal(true);
                        }}
                        icon={
                          <AntDesign
                            name="delete"
                            color={'red'}
                            style={styles.iconStyle}
                            size={20}
                          />
                        }
                        btnStyle={{ ...styles.editButtonStyle, borderColor: colors.primary }}
                        bgColor={colors.secondary}
                      />
                    ) : (
                      <IconButton
                        onPress={() => {
                          setModalHeader(key.name), setModalShow(true);
                        }}
                        icon={
                          <Icon
                            name="edit-3"
                            color={colors.primary}
                            style={styles.iconStyle}
                            size={20}
                          />
                        }
                        btnStyle={{ ...styles.editButtonStyle, borderColor: colors.primary }}
                        bgColor={colors.secondary}
                      />
                    )}
                  </View>
                ) : null
              )}

            <View style={styles.divstyle}>
              <View>
                <AText
                  fontWeight={FontStyle.fontMedium}
                  styleText={{ color: '#828181' }}
                  fontSize={'medium'}
                >
                  Study Time
                </AText>
                <AText fontWeight={FontStyle.fontBold} fontSize={'medium'}>
                  {moment(observeData.startTime).format('LTS')}{' '}
                  <AText styleText={{ color: '#c7c7c7', paddingTop: 17 }} fontSize={'large'}>
                    |
                  </AText>{' '}
                  {moment(observeData.startTime).format('LL')}
                </AText>
              </View>
            </View>
          </View>
        )}
        <BottomModalUI
          width={'100%'}
          closeModal={() => {
            setModalShow(false), setFreqInput(''), setFreqKeyPressed('');
          }}
          height={
            modalHeader == 'Notes' && windowWidth > windowHeight
              ? windowHeight * 0.45
              : modalHeader == 'Rate'
                ? windowHeight * 0.6
                : modalHeader == 'Frequency'
                  ? windowHeight * 0.55
                  : modalHeader === 'Notes'
                    ? windowHeight * 0.3
                    : windowHeight * 0.47
          }
          modalShow={modalShow}
          showScroll={true}
          closeShow
        >
          <View
            style={{
              paddingVertical: 15,
              width: '85%',
              alignSelf: 'center',
              alignItems: 'center',
              justifyContent: 'space-between',
              flexDirection: 'row',
            }}
          >
            <AText fontWeight={FontStyle.fontMedium} fontSize={'title'}>
              Edit {modalHeader}
            </AText>

            <View style={{ width: windowWidth * 0.27 }}>
              {modalHeader === 'Notes' && (
                <LinearGradientButton
                  disabled={false}
                  onPress={() => {
                    editObservation(editNotes);
                  }}
                  title={'Update'}
                  fontSize={'small'}
                  compact={true}
                  btnStyle={{ alignSelf: 'flex-start' }}
                />
              )}
            </View>
          </View>
          <View style={styles.container}>
            {modalHeader === 'Task' && !isEmpty(taskData) ? (
              taskData.map((item, index) => (
                <View style={{ width: '100%' }} key={item.id}>
                  <StudyNextCard
                    data={item}
                    showBorder={true}
                    icon={'arrow-right'}
                    navigateNext={() => {
                      editObservation(item);
                    }}
                  />
                </View>
              ))
            ) : modalHeader === 'Task' ? (
              <Text
                style={{
                  alignSelf: 'center',
                  fontSize: 25,
                  marginTop: 20,
                  color: dark ? '#fff' : '#000',
                }}
              >
                No Data Found
              </Text>
            ) : null}
            {modalHeader === 'Area' && !isEmpty(areaData) ? (
              areaData.map((item, index) => (
                <View style={{ width: '100%' }} key={item.id}>
                  <StudyNextCard
                    data={item}
                    showBorder={true}
                    icon={'arrow-right'}
                    navigateNext={() => {
                      editObservation(item);
                    }}
                  />
                </View>
              ))
            ) : modalHeader === 'Area' ? (
              <Text
                style={{
                  alignSelf: 'center',
                  fontSize: 25,
                  marginTop: 20,
                  color: dark ? '#fff' : '#000',
                }}
              >
                No Data Found
              </Text>
            ) : null}
            {modalHeader === 'Element' && !isEmpty(elementData) ? (
              elementData.map((item, index) => (
                <View style={{ width: '100%' }} key={item.id}>
                  <StudyNextCard
                    data={item}
                    showBorder={true}
                    icon={'arrow-right'}
                    navigateNext={() => {
                      editObservation(item);
                    }}
                  />
                </View>
              ))
            ) : modalHeader === 'Element' ? (
              <Text
                style={{
                  alignSelf: 'center',
                  fontSize: 25,
                  marginTop: 20,
                  color: dark ? '#fff' : '#000',
                }}
              >
                No Data Found
              </Text>
            ) : null}
          </View>
          {modalHeader === 'Rate' && (
            <View
              style={[
                styles.rateContainStyle,
                {
                  width: windowHeight > windowWidth ? '99%' : '90%',
                },
              ]}
            >
              {!isEmpty(rate) &&
                [...Array(n)].map((e, i) => {
                  let currentRating = rate + i * 5;
                  currentRating = currentRating == 125 ? 'Not Rated' : currentRating;

                  const isNotRated = currentRating === 125 || currentRating === 'Not Rated';
                  const isRatingSelected = selectedRating === currentRating;

                  return (
                    <RatingButton
                      onPress={() => editObservation(rate + i * 5)}
                      selected={isRatingSelected}
                      ratingContainerStyle={{
                        height:
                          windowWidth > windowHeight ? windowWidth * 0.05 : windowWidth * 0.12,
                        width: isNotRated ? '63%' : '20%',
                        opacity: selectedRating === rate + i * 5 ? 0.8 : 1,
                        backgroundColor:
                          selectedRating === rate + i * 5
                            ? 'rgba(226, 216, 235 ,0.9)'
                            : dark
                              ? colors.onSurface
                              : '#fff',
                        borderColor: isRatingSelected ? '#00C0F3' : '#fff',
                      }}
                      selectedRateContainerStyle={{
                        borderColor: isRatingSelected ? '#00C0F3' : '#fff',
                        borderWidth: isRatingSelected ? 3 : 1,
                      }}
                      item={isNotRated ? 'Not Rated' : currentRating}
                    />
                  );
                })}
            </View>
          )}
          {modalHeader === 'Frequency' && (
            <>
              <View
                activeOpacity={0.9}
                style={[
                  styles.freqDisp,
                  {
                    // backgroundColor: dark ? colors.onSurface : '#fff',
                  },
                ]}
              >
                <AText
                  fontWeight={FontStyle.fontMedium}
                  styleText={{ color: isEmpty(freqInput) ? '#c7c7c7' : dark ? '#fff' : '#000' }}
                  fontSize={'large'}
                >
                  {isEmpty(freqInput) ? 'Enter Frequency' : freqInput}
                </AText>
              </View>
              <View style={[styles.freqViewcontainer]}>
                {[...Array(freq)].map((e, i) => {
                  let val = i + 1 == 10 ? 0 : i + 1;
                  return (
                    <Pressable
                      activeOpacity={0.9}
                      onPress={() => {
                        setFreqKeyPressed(val),
                          i + 1 == 11 && !isEmpty(freqInput)
                            ? (setFreqKeyPressed(''), editObservation(freqInput))
                            : i + 1 == 11 && isEmpty(freqInput)
                              ? (setFreqKeyPressed(''), setModalShow(false))
                              : i + 1 == 12 && !isEmpty(freqInput)
                                ? setFreqInput(freqInput.substring(0, freqInput.length - 1))
                                : i + 1 < 12
                                  ? setFreqInput(freqInput + '' + val)
                                  : setFreqInput(freqInput);
                      }}
                      key={i * n}
                      disabled={
                        i + 1 == 11 || i + 1 == 12 ? false : freqInput.length > 4 ? true : false
                      }
                      style={[
                        styles.freqcontainer,
                        {
                          height: windowWidth > 900 ? windowWidth * 0.08 : windowWidth * 0.14,
                          width: windowWidth > 900 ? windowWidth * 0.09 : windowWidth * 0.15,
                          opacity:
                            !isEmpty(freqInput) && freqInput[freqInput.length - 1] == i + 1
                              ? 0.8
                              : 1,
                          backgroundColor: dark ? colors.onSurface : '#fff',
                        },
                      ]}
                    >
                      <LinearGradient
                        colors={
                          !isEmpty(freqInput) && freqKeyPressed == val
                            ? // (!isEmpty(freqInput) && (freqInput[freqInput.length - 1] == i + 1 || (freqInput[freqInput.length - 1] == 0 && i + 1 == 10)))
                              LINEAR_GRADIENT_RATING_BUTTON
                            : dark
                              ? [colors.onSurface, colors.onSurface]
                              : ['#fff', '#fff']
                        }
                        key={i * n}
                        start={{ x: 0.1, y: 0.3 }}
                        end={{ x: 0.5, y: 0.7 }}
                        style={[
                          styles.selectedfreqcontainer,
                          {
                            borderColor:
                              !isEmpty(freqInput) && freqInput[freqInput.length - 1] == i + 1
                                ? '#9a7fe0cc'
                                : '#fff',
                          },
                        ]}
                      >
                        {i + 1 == 12 ? (
                          <FontAwesome name="backspace" style={styles.iconStyle} size={45} />
                        ) : (
                          <AText
                            fontWeight={FontStyle.fontMedium}
                            styleText={{ color: dark ? '#fff' : '#000', textAlign: 'center' }}
                            fontSize={'large'}
                          >
                            {i + 1 == 10 ? 0 : i + 1 == 11 ? 'end' : i + 1}
                          </AText>
                        )}
                      </LinearGradient>
                    </Pressable>
                  );
                })}
              </View>
            </>
          )}
          {modalHeader === 'Notes' && (
            <View style={{ width: '87%', alignSelf: 'center' }}>
              <Textinputs
                heading={''}
                placeholder=""
                stylesTextInput={{ padding: 5 }}
                value={editNotes}
                onchange={(val) => {
                  setEditNotes(val);
                }}
              />
            </View>
          )}
        </BottomModalUI>

        <BottomModal
          height={windowHeight > windowWidth ? windowHeight * 0.25 : windowHeight * 0.35}
          modalShow={deletePhotoModal}
          confirmPress={() => {
            editObservation();
          }}
          cancelShow={() => setDeletePhotosModal(false)}
          title={'Delete Photos'}
          body={DELETE_PHOTO_MSG}
          confirm={'Yes'}
          reject={'No'}
        />

        <BottomModal
          height={windowHeight > windowWidth ? windowHeight * 0.25 : windowHeight * 0.35}
          modalShow={deleteObsModal}
          confirmPress={() => {
            deleteFromSQLite();
          }}
          cancelShow={() => setDeleteObsModal(false)}
          title={DELETE_TITLE}
          body={DELETE_MSG}
          confirm={'Yes'}
          reject={'No'}
        />
        <ImageView
          images={[{ uri: mediaUrl }]}
          imageIndex={0}
          visible={!isEmpty(mediaUrl)}
          onRequestClose={() => setMediaUrl('')}
        />
      </SubmitHeader>
    </>
  );
};
export default ObservationDetail;

const styles = StyleSheet.create({
  syncCardstyle: {
    width: '80%',
    justifyContent: 'center',
    flexDirection: 'column',
    alignItems: 'center',
  },

  divstyle: {
    width: '100%',
    justifyContent: 'space-between',
    flexDirection: 'row',
    borderBottomWidth: 0.9,
    borderColor: 'grey',
    paddingVertical: 30,
  },
  iconStyle: {
    alignSelf: 'center',
  },
  freqDisp: {
    justifyContent: 'flex-end',
    // alignItems: 'flex-end',
    marginHorizontal: '8%',
    paddingVertical: 10,
    // alignSelf: 'center',
    width: '83%',
    paddingHorizontal: 20,
    borderRadius: 8,
    borderWidth: 1.7,
    borderColor: '#D3D3D3',
    shadowColor: '#000',
  },
  freqcontainer: {
    justifyContent: 'center',
    marginHorizontal: 7,
    marginVertical: 10,
    alignItems: 'center',
    borderRadius: 26,
    borderWidth: 1,
    borderColor: '#d8d8d8',
  },
  selectedfreqcontainer: {
    justifyContent: 'center',
    width: '100%',
    height: '100%',
    alignSelf: 'center',
    alignItems: 'center',
    borderRadius: 26,
  },
  ratecontainer: {
    justifyContent: 'center',
    marginHorizontal: 7,
    marginVertical: 10,
    alignItems: 'center',
    borderRadius: 26,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
    elevation: 2,
  },
  slectedRatecontainer: {
    justifyContent: 'center',
    width: '100%',
    height: '100%',
    alignSelf: 'center',
    alignItems: 'center',
    borderRadius: 26,
  },
  container: {
    width: '100%',
    // flexDirection: "row",
    // alignItems: "center",
    alignSelf: 'center',
    // flexWrap: "wrap",
    marginTop: 15,
    marginBottom: 5,
  },
  freqViewcontainer: {
    width: '90%',
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'center',
    flexWrap: 'wrap',
    marginTop: 15,
    marginBottom: 30,
    justifyContent: 'center',
  },
  rateContainStyle: {
    width: '99%',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    flexWrap: 'wrap',
    paddingBottom: 55,
  },
  updateButtonView: {
    width: windowWidth * 0.27,
    // marginTop: 20,
  },
  editButtonStyle: {
    padding: 0,
    borderRadius: 40,
    width: 60,
    height: 60,
    marginRight: 10,
    marginRight: 10,
    justifyContent: 'center',
  },
});
