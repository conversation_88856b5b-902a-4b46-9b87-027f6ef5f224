require_relative '../node_modules/react-native/scripts/react_native_pods'
require_relative '../node_modules/@react-native-community/cli-platform-ios/native_modules'
source 'https://github.com/CocoaPods/Specs.git'
# platform :ios, '12.4'
platform :ios, min_ios_version_supported
prepare_react_native_project!
install! 'cocoapods', :deterministic_uuids => false


target 'ActivityStudy' do
  config = use_native_modules!

  pod 'Firebase', :modular_headers => true
  pod 'FirebaseCoreInternal', :modular_headers => true
  pod 'GoogleUtilities', :modular_headers => true
  pod 'FirebaseCoreExtension', :modular_headers => true
  pod 'FirebaseInstallations', :modular_headers => true
  pod 'GoogleDataTransport', :modular_headers => true
  pod 'nanopb', :modular_headers => true
  pod 'FirebaseCore', :modular_headers => true
# Add the pod for Firebase Crashlytics
pod 'Firebase/Crashlytics'
# Recommended: Add the Firebase pod for Google Analytics
pod 'Firebase/Analytics'
  $RNFirebaseAsStaticFramework = true

  
  use_react_native!(
    :path => config[:reactNativePath],
    # to enable hermes on iOS, change `false` to `true` and then install pods
    :hermes_enabled => false
  )

  target 'ActivityStudyTests' do
    inherit! :complete
    # Pods for testing
  end

  # Enables Flipper.
  #
  # Note that if you have use_frameworks! enabled, Flipper will not work and
  # you should disable the next line.
  use_flipper!()

  post_install do |installer|
    react_native_post_install(installer)
    __apply_Xcode_12_5_M1_post_install_workaround(installer)
  end
end
