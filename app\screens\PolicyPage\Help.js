import { Linking, StyleSheet, View } from 'react-native';
import React from 'react';
import { useTheme, Text } from 'react-native-paper';

import { AText, MainLayout } from '_theme_components';
import { FontStyle } from '_utils';

const HelpScreen = ({ navigation }) => {
  const { colors } = useTheme();
  return (
    <MainLayout back navigation={navigation} headerShow>
      <View style={styles.contentContainer}>
        <AText fontSize={'xtralarge'} fontWeight={FontStyle.fontBold}>
          Help
        </AText>
        <Text style={styles.textstyle}>
          If you require support please contact{' '}
          <Text
            onPress={() => Linking.openURL('mailto:<EMAIL>')}
            style={[styles.linkStyle, { color: colors.primary }]}
          >
            <EMAIL>
          </Text>
        </Text>
      </View>
    </MainLayout>
  );
};

export default HelpScreen;

const styles = StyleSheet.create({
  contentContainer: {
    width: '80%',
    marginTop: 20,
    alignSelf: 'center',
  },
  textstyle: {
    marginTop: 19,
    fontFamily: FontStyle.fontRegular,
  },
  linkStyle: {
    textDecorationLine: 'underline',
  },
});
