import { APP_ID, DEVELOPMENT_URL, PRODUCTION_URL, VERSION } from '_utils';
import { getToken, getURL, isEmpty, setURL } from '_helpers';

export const PostFetchWithoutToken = async (url, registerDetails) => {
  let URLMAIN = await getURL();
  if (isEmpty(URLMAIN)) {
    URLMAIN = PRODUCTION_URL;
    await setURL(PRODUCTION_URL);
  }
  let myHeaders = new Headers();
  myHeaders.append('Content-Type', 'application/json');
  myHeaders.append('studyType', APP_ID.toString());
  myHeaders.append('version', VERSION);
  var raw = JSON.stringify(registerDetails);
  var requestOptions = {
    method: 'POST',
    headers: myHeaders,
    body: raw,
    redirect: 'follow',
  };

  try {
    const response = fetch(`${URLMAIN}${url}`, requestOptions).then((res) => {
      return new Promise((resolve) => {
        const contentType = res.headers.get('content-type');
        if (contentType && contentType.indexOf('application/json') !== -1) {
          res.json().then((json) =>
            resolve({
              status: res.status,
              data: json,
            })
          );
        } else {
          throw 'Something went wrong';
        }
      });
    });

    return Promise.resolve(response);
  } catch (error) {
    return Promise.reject(error);
  }
};
export const PostFetch = async (url, updateDetails, timeout = 10000) => {
  let [token, baseUrl] = await Promise.all([getToken(), getURL() || DEVELOPMENT_URL]);

  if (!baseUrl) {
    await setURL(PRODUCTION_URL);
  }
  var myHeaders = new Headers();
  myHeaders.append('studyType', APP_ID.toString());
  myHeaders.append('version', VERSION);
  myHeaders.append('Authorization', `${token}`);
  myHeaders.append('Content-Type', 'application/json');

  // myHeaders.append('Authorization', `Bearer ${token}`);
  var raw = JSON.stringify(updateDetails);

  var requestOptions = {
    method: 'POST',
    headers: myHeaders,
    body: raw,
    redirect: 'follow',
  };

  try {
    const response = await Promise.race([
      fetch(`${URLMAIN}${url}`, requestOptions),
      new Promise((_, reject) => setTimeout(() => reject(new Error('Timeout')), timeout)),
    ]);
    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      const json = await response.json();
      return { status: response.status, data: json };
    } else {
      throw 'Something went wrong';
    }
  } catch (error) {
    return Promise.reject(error);
  }
};

export const PostFetchImagehData = async (url, formData) => {
  let token = await getToken();
  let URLMAIN = (await getURL()) || PRODUCTION_URL;
  if (isEmpty(URLMAIN)) {
    await setURL(PRODUCTION_URL);
  }
  var myHeaders = new Headers();
  myHeaders.append('studyType', APP_ID.toString());
  myHeaders.append('version', VERSION);
  myHeaders.append('Authorization', `${token}`);
  myHeaders.append('Content-Type', 'multipart/form-data');
  myHeaders.append('cache-control', 'no-cache');

  var requestOptions = {
    method: 'POST',
    headers: myHeaders,
    body: formData,
    redirect: 'follow',
  };
  try {
    const response = await fetch(`${URLMAIN}${url}`, requestOptions).then((res) => {
      return new Promise((resolve) => {
        const contentType = res.headers.get('content-type');
        if (contentType && contentType.indexOf('application/json') !== -1) {
          res.json().then((json) =>
            resolve({
              status: res.status,
              data: json,
            })
          );
        } else {
          throw 'Something went wrong';
        }
      });
    });
    return Promise.resolve(response);
  } catch (error) {
    return Promise.reject(error);
  }
};

export const Postmultiple = async (url, updateDetails) => {
  let token = await getToken();
  let URLMAIN = (await getURL()) || PRODUCTION_URL;
  if (isEmpty(URLMAIN)) {
    await setURL(PRODUCTION_URL);
  }

  var myHeaders = new Headers();
  myHeaders.append('studyType', APP_ID.toString());
  myHeaders.append('version', VERSION);
  myHeaders.append('Authorization', `${token}`);
  myHeaders.append('Content-Type', 'application/json');

  // myHeaders.append('Authorization', `Bearer ${token}`);
  var raw = JSON.stringify(updateDetails);

  var requestOptions = {
    method: 'POST',
    headers: myHeaders,
    body: raw,
    redirect: 'follow',
  };

  try {
    const response = await fetch(`${URLMAIN}${url}`, requestOptions);
    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      const json = await response.json();
      return json;
    } else {
      throw new Error('Something went wrong');
    }
  } catch (error) {
    return Promise.reject(error);
  }
};

export const executeFetch = async (url, type = 'GET', payload) => {
  // type=POSTcustomURL/postWithoutAuth/uploadImageFormData/Getmultiple/POST/GET/POSTWithoutTimeout

  try {
    let [token, baseUrl] = await Promise.all([getToken(), getURL() || PRODUCTION_URL]);
    if (!baseUrl) {
      await setURL(PRODUCTION_URL);
      baseUrl = PRODUCTION_URL;
    }

    var myHeaders = new Headers();
    myHeaders.append('studyType', APP_ID.toString());
    myHeaders.append('version', VERSION);
    myHeaders.append('cache-control', 'no-cache');
    if (type && type !== 'postWithoutAuth') {
      myHeaders.append('Authorization', `${token}`);
    }
    if (type === 'Getmultiple') {
      myHeaders.append('projectID', `${payload}`);
    }
    myHeaders.append(
      'Content-Type',
      type == 'uploadImageFormData' ? 'multipart/form-data' : 'application/json'
    );
    const fullUrl = `${baseUrl}api/v1/${type === 'postWithoutAuth' || type == 'uploadImageFormData' ? url : `app/${url}`}`;

    var requestOptions = {
      method: type.toLowerCase() === 'get' || type === 'Getmultiple' ? 'GET' : 'POST',
      headers: myHeaders,
      redirect: 'follow',
    };
    if (type.toLowerCase() !== 'get' && type !== 'Getmultiple') {
      requestOptions.body = type === 'uploadImageFormData' ? payload : JSON.stringify(payload);
    }

    const response = await fetchWithTimeout(fullUrl, requestOptions, type);
    const contentType = response.headers.get('content-type');

    if (contentType && contentType.includes('application/json')) {
      let returnData = {};
      const json = await response.json();
      if (type === 'Getmultiple') {
        returnData = json && json.data ? json.data : '';
      } else {
        returnData = { status: response.status, data: json };
      }
      return returnData;
    } else {
      throw 'Something went wrong';
    }
  } catch (error) {
    console.log(error, 'errors', url);
    return Promise.reject(error);
  }
};

const fetchWithTimeout = (url, options, type) => {
  const fetchPromise = fetch(url, options);
  if (type === 'POSTWithoutTimeout') {
    return fetchPromise;
  }

  const timeoutPromise = new Promise((_, reject) =>
    setTimeout(() => reject(new Error('Request timed out')), 10000)
  );

  return Promise.race([fetchPromise, timeoutPromise]);
};
