import React, { useEffect, useState } from 'react';
import { Image, StyleSheet, Text, Modal, TouchableOpacity, View, Linking } from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome';
import { checkVersion } from 'react-native-check-version';
import { localimage } from '_utils';
import { AText, IconButton, LinearGradientButton } from '_theme_components';
import { useTheme } from 'react-native-paper';

const UpdateVesionModal = () => {
  const { colors } = useTheme();
  const [viewUpdateModal, setViewUpdateModal] = useState(false);
  const [playStoreLink, setplayStoreLink] = useState('');

  useEffect(() => {
    checkVersions();
  }, []);
  const checkVersions = async () => {
    const version = await checkVersion();
    if (version.needsUpdate) {
      setplayStoreLink(version.url);
      // var nextPopup = await getData('UpdateModalShown')
      // var expires = moment(new Date()).toString();
      // if (isEmpty(nextPopup) || nextPopup < expires) {
      setTimeout(() => {
        // Store the expiration date of the current popup in localStorage.
        // expires = moment().hour(48)
        // storeData('UpdateModalShown', (expires).toString())
        setViewUpdateModal(true);
      }, 7500);
      // }
    }
  };

  return (
    <Modal
      transparent={true}
      visible={viewUpdateModal}
      animationIn="slideInLeft"
      animationOut="slideOutRight"
    >
      <View style={styles.containerStyle}>
        <View style={styles.updateContainerStyle}>
          <IconButton
            btnStyle={styles.viewImageClose}
            bgColor={'#fff'}
            icon={
              <Icon name="close" style={{ alignSelf: 'center' }} color={colors.primary} size={27} />
            }
            onPress={() => {
              setViewUpdateModal(false);
            }}
          />

          <Image style={styles.imgStyle} source={localimage.logo} />
          <View style={styles.textContainerStyle}>
            <AText error fontSize={'title'}>
              New version available
            </AText>
            <AText
              styleText={{ color: '#9B9B9B', padding: 10, textAlign: 'center' }}
              fontSize={'medium'}
            >
              Looks like you have an older version of the app.{'\n'}Please update to get latest
              feature and best experience.
            </AText>
          </View>
          <View style={styles.updateViewButton}>
            <LinearGradientButton
              title={'Update Now'}
              onPress={() => Linking.openURL(playStoreLink)}
            />
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default UpdateVesionModal;

const styles = StyleSheet.create({
  containerStyle: {
    backgroundColor: 'rgba(0,0,0,0.1)',
    alignItems: 'center',
    justifyContent: 'center',
    flexGrow: 1,
  },
  updateContainerStyle: {
    width: '75%',
    backgroundColor: 'white',
    paddingVertical: 40,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 14,
    borderColor: 'rgba(0, 0, 0, 0.1)',
  },
  imgStyle: {
    width: 90,
    height: 80,
    resizeMode: 'contain',
    marginBottom: 5,
  },
  viewImageClose: {
    position: 'absolute',
    top: 10,
    right: 25,
    zIndex: 25,
    padding: 4,
    borderRadius: 50,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    alignContent: 'center',
  },
  textContainerStyle: {
    margin: 15,
    paddingHorizontal: 35,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  updateViewButton: {
    marginTop: 15,
    marginStart: 7,
    width: 350,
  },
});
