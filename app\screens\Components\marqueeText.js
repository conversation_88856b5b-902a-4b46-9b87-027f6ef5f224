import React, { useEffect, useRef, useState } from 'react';
import { Animated, Text, TouchableOpacity } from 'react-native';

const MarqueeText = ({ text = '', duration = 1000, style = {}, onpress, show }) => {
  const [showAlert, setShowAlert] = useState(show);
  const animatedValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    setShowAlert(show);
  }, [show]);

  useEffect(() => {
    const runAnimation = () => {
      Animated.timing(animatedValue, {
        toValue: 1,
        duration,
        useNativeDriver: true,
      }).start(({ finished }) => {
        if (finished) {
          // Restart the animation from the off-screen position
          animatedValue.setValue(0);
          runAnimation(); // Start the animation again
        }
      });
    };

    runAnimation(); // Start the initial animation
  }, [animatedValue, duration]);

  return (
    <TouchableOpacity
      activeOpacity={1}
      //   onPress={() => onpress()}
      style={{ overflow: 'hidden', display: showAlert ? 'flex' : 'none', ...style }}
    >
      <Animated.Text
        style={{
          color: '#fff',
          fontWeight: 'bold',
          fontSize: 16,
          textAlign: 'center',
          //   transform: [
          //     {
          //       translateX: animatedValue.interpolate({
          //         inputRange: [0, 1],
          //         // Change the output range to make it loop continuously
          //         outputRange: [-text.length * 13, text.length * 13],
          //       }),
          //     },
          //   ],
        }}
      >
        {text}
      </Animated.Text>
      <TouchableOpacity onPress={() => setShowAlert(false)}>
        <Text
          style={{
            color: '#fff',
            fontWeight: 'bold',
            fontSize: 16,
            textDecorationLine: 'underline',
            marginLeft: 8,
          }}
        >
          Dismiss
        </Text>
      </TouchableOpacity>
    </TouchableOpacity>
  );
};

export default MarqueeText;
