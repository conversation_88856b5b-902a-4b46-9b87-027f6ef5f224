import { StyleSheet, Text, View } from 'react-native';
import React from 'react';
import { AButton } from '_theme_components';
import { useTheme } from 'react-native-paper';
import { windowWidth } from '_utils';

const RateScreenFooter = ({ navigateTo, modalOpen }) => {
  const { colors, dark } = useTheme();
  const ButtonArray = [
    { id: 1, name: 'Areas', navigateTo: () => navigateTo('area'), width: windowWidth * 0.17 },
    {
      id: 2,
      name: 'quick Change area',
      navigateTo: () => navigateTo('quick-area'),
      width: windowWidth * 0.31,
    },
    { id: 3, name: 'Tasks', navigateTo: () => navigateTo('task'), width: windowWidth * 0.17 },
    { id: 4, name: 'Elements', navigateTo: () => navigateTo('element'), width: windowWidth * 0.2 },
  ];
  return (
    <View style={[styles.container, { backgroundColor: dark ? '#484848' : 'rgba(255,255,255,1)' }]}>
      {ButtonArray.map(({ name, navigateTo, width }) => (
        <AButton
          disabled={modalOpen}
          mode="contained"
          styleText={{ color: colors.primary }}
          fontSize={'small'}
          btnStyle={{ ...styles.buttonStyle, width: width }}
          title={name}
          onPress={() => navigateTo()}
        />
      ))}
    </View>
  );
};

export default RateScreenFooter;

const styles = StyleSheet.create({
  container: {
    justifyContent: 'space-evenly',
    flexDirection: 'row',
    position: 'absolute',
    bottom: 0,
    width: '100%',
    paddingVertical: 10,
  },
  buttonStyle: {
    padding: 0,
    borderRadius: 60,
    backgroundColor: '#75D3F1',
    height: 55,
  },
});
