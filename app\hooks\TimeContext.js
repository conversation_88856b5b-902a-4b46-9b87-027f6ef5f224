import { isEmpty } from '_helpers';
import moment from 'moment';
import React, { createContext, useState, useEffect, useRef } from 'react';
import { AppState } from 'react-native';

const AppContext = createContext();

const AppProvider = ({ children }) => {
  const [timerValue, setTimerValue] = useState(0);
  const [isTimerRunning, setIsTimerRunning] = useState(false);
  const [timerLoader, setTimerLoader] = useState(false);
  const [reRateReLoad, setReRateReLoad] = useState(false);

  const [timerInterval, setTimerInterval] = useState(null);
  const [pausedTime, setPausedTime] = useState(null);
  const appState = useRef(AppState.currentState);

  useEffect(() => {
    const subscription = AppState.addEventListener('change', (nextAppState) => {
      if (appState.current.match(/inactive|background/) && nextAppState === 'active') {
        const currentDate = new Date();
        const appopentime = Math.floor(currentDate.getTime() / 1000);

        const backgroundTime = pausedTime !== null ? appopentime - pausedTime : 0;
        if (backgroundTime > 0) {
          setTimerValue((prevValue) => (prevValue !== 0 ? prevValue + backgroundTime : 0));
        }
        setTimeout(() => {
          setReRateReLoad(false);
        }, 1200);
        setTimerLoader(false);
        setPausedTime(null);
      } else if (nextAppState === 'background') {
        setTimerLoader(true);
        setReRateReLoad(true);
        const currentDate = new Date();
        const currentSeconds = Math.floor(currentDate.getTime() / 1000);
        setPausedTime(currentSeconds);
      }
      appState.current = nextAppState;
    });
    return () => {
      subscription.remove();
    };
  }, [pausedTime]);

  useEffect(() => {
    if (isTimerRunning) {
      const intervalId = setInterval(() => {
        setTimerValue((prevValue) => prevValue + 1);
      }, 1050);
      setTimerInterval(intervalId);
    } else {
      clearInterval(timerInterval);
    }

    return () => {
      clearInterval(timerInterval);
    };
  }, [isTimerRunning]);

  const startTimer = () => {
    setIsTimerRunning(true);
  };

  const pauseTimer = () => {
    setIsTimerRunning(false);
    setPausedTime(timerValue);
  };

  const stopTimer = () => {
    setTimerValue(0);
    setIsTimerRunning(false);
    setPausedTime(null);
  };

  const restartTimer = () => {
    setTimerValue(0);
    setPausedTime(null);
    setIsTimerRunning(true);
  };
  const reRateLoadStop = () => {
    setReRateReLoad(false);
  };

  return (
    <AppContext.Provider
      value={{
        timerValue,
        isTimerRunning,
        startTimer,
        pauseTimer,
        stopTimer,
        restartTimer,
        pausedTime,
        timerLoader,
        reRateReLoad,
        reRateLoadStop,
      }}
    >
      {children}
    </AppContext.Provider>
  );
};

export { AppContext, AppProvider };
