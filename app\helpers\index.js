import { DrawerContent } from './drawerContent';
import { isEmpty } from './isEmpty';
import RateScreenFooter from './rateScreenFooter';
import { formatTime } from './FormatTimer';
import { launchCamera } from './mediaSelect';
import CustomSwitch from './customSwitch';
import { dynamicSort, alphanumericSort } from './dynamicSort';
import {
  PostFetchWithoutToken,
  PostFetch,
  PostFetchImagehData,
  Postmultiple,
  executeFetch,
} from './fetch-service';
import {
  createTable,
  dropTable,
  getDBConnection,
  getAllData,
  insertIntoTable,
  parseRows,
  markStudyAsCancelled,
  getStudies,
  runSqlQuery,
  // getElements
  deleteTable,
  dropAllTables,
  prepareDataRow,
  getDataByTableAndID,
  getElements,
  getTasks,
  getTaskByID,
  updateTable,
  deleteRecord,
  updateTaskElementsByID,
  markStudyAsSynced,
  getLocationByID,
  deleteById,
  updateOfflineElementInTasks,
  updateOfflineRoleInStudies,
  getRecoverStudies,
  getPhotos,
  getElementsGroupByTask,
  getReoverDataByTableAndID,
  mergeColumnName,
  getStudiesBYProjectID,
  searchElementByName,
  getDataByTableAndName,
  getStudiesBYSyncID,
  getStudyData,
  getAllStudyData,
  updateStudyData,
  getStudyTable,
  updateCreationTable,
} from './sql-db';
import {
  storeData,
  removeStorage,
  getToken,
  getData,
  clearStorage,
  setURL,
  getURL,
} from './storage';
import { isJson } from './isJSON';
import UpdateVesionModal from './UpdateVesionModal';
import NetinfoAlert from './netConeectionError';
import OpenCameraView from './cameraView';
import { useDebounce } from './debounce';
import BiometricModal from './biometricModal';
import ProgressBar from './progressBar';
import LoadingProgressBar from './loadingProgressBar';
import { Formiks } from './formik';

export {
  DrawerContent,
  isEmpty,
  RateScreenFooter,
  formatTime,
  launchCamera,
  CustomSwitch,
  createTable,
  dropTable,
  getDBConnection,
  storeData,
  removeStorage,
  getToken,
  getData,
  clearStorage,
  setURL,
  getURL,
  getAllData,
  insertIntoTable,
  parseRows,
  markStudyAsCancelled,
  getStudies,
  runSqlQuery,
  deleteTable,
  dropAllTables,
  prepareDataRow,
  getDataByTableAndID,
  getElements,
  getTasks,
  getTaskByID,
  updateTable,
  deleteRecord,
  updateTaskElementsByID,
  PostFetchWithoutToken,
  PostFetch,
  PostFetchImagehData,
  Postmultiple,
  markStudyAsSynced,
  getLocationByID,
  dynamicSort,
  deleteById,
  updateOfflineElementInTasks,
  updateOfflineRoleInStudies,
  getRecoverStudies,
  getPhotos,
  getElementsGroupByTask,
  isJson,
  UpdateVesionModal,
  getReoverDataByTableAndID,
  mergeColumnName,
  NetinfoAlert,
  OpenCameraView,
  getStudiesBYProjectID,
  useDebounce,
  searchElementByName,
  BiometricModal,
  ProgressBar,
  LoadingProgressBar,
  getDataByTableAndName,
  getStudiesBYSyncID,
  Formiks,
  alphanumericSort,
  executeFetch,
  getAllStudyData,
  getStudyData,
  updateStudyData,
  getStudyTable,
  updateCreationTable,
};
