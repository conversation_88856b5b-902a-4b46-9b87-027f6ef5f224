import { <PERSON>List, StyleSheet, View } from 'react-native';
import React, { useEffect, useState } from 'react';
import { useTheme } from 'react-native-paper';
import { useDispatch, useSelector } from 'react-redux';

import { AText, NoDataFound, StudyHeader, StudyNextCard } from '_theme_components';
import { FontStyle, INTERNET_ERROR, localimage, tables, windowHeight, windowWidth } from '_utils';
import { isEmpty, dynamicSort, searchElementByName } from '_helpers';
import { canStartStudy, creatingData, getElementDataFetch, isYesOrNo } from '_provider';
import { pullServerData } from '_action';

import { ALERT_ERROR } from '../../../store/reducers/alert';
import { getCategories } from '_action';
import { CreateElementModal, SortingModal } from './components';

const ElementScreen = ({
  navigation,
  showCreateModal,
  setShowCreateModal,
  navigateTo,
  onRefreshPage,
  setLoader,
  loader,
  CanElementContinue,
  navigateScreen,
  setRefreshFalse,
  elementViewNext,
  setElementViewNext,
  closeCanCreateModal,
  setCloseCanCreateModal,
  elementValue,
  setElementValue,
  errorValue,
  setErrorValue,
}) => {
  const { colors } = useTheme();
  const dispatch = useDispatch();

  const project = useSelector((state) => state.createStudy.projectSelect);
  const { taskSelect } = useSelector((state) => state.createStudy);
  const { dbDetail, userDetails } = useSelector((state) => state.user);
  const { netConnection } = useSelector((state) => state.netInfo);
  const { elementList, allCategory } = useSelector((state) => state.serverReducer);

  const [elementData, setElementData] = useState([]);

  const [modalShow, setModalShow] = useState(false);
  const [sortModalShow, setSortModalShow] = useState(false);
  const [showSearch, setShowSearch] = useState(true);
  const [orderElement, setOrderElement] = useState('');
  const [elementSearchInput, setElementSearchInput] = useState('');

  useEffect(() => {
    if (showCreateModal) {
      setModalShow(true);
      if (isEmpty(allCategory)) {
        getCategory();
      }
    }
  }, [showCreateModal]);

  useEffect(() => {
    if (elementList) {
      setElementData(elementList);
      CanElementContinue();
    }
  }, [elementList]);

  const getElementData = async (refresh, task = taskSelect) => {
    if (refresh) {
      await Promise.all([
        dispatch(pullServerData(tables.TASKS_TABLE, project._id, dbDetail)),
        dispatch(pullServerData(tables.ELEMENTS_TABLE, project._id, dbDetail)),
      ]);
    }
    await dispatch(getElementDataFetch(dbDetail, project._id));
    setLoader(false);
  };

  const navigateNext = (selectedItem, ec) => {
    if (isYesOrNo(selectedItem.name)) {
      navigateScreen('YesNoElement', ec, selectedItem, true);
      return;
    } else {
      navigateScreen('nextElement', ec, selectedItem, true);
    }
  };
  const onChangeSearch = (value) => {
    let data = elementList;
    if (value !== '') {
      let filteredData = data.filter((item) =>
        item.name.toLowerCase().includes(value.toLowerCase())
      );
      setElementData(filteredData);
      setElementSearchInput(value);
    } else {
      setElementData(data);
      setElementSearchInput('');
    }
  };

  const ItemView = ({ item, index }) => {
    return (
      <View key={item.id + Math.random() + index}>
        <StudyNextCard
          controllingElementID={
            !isEmpty(taskSelect.controllingElementID) && taskSelect.controllingElementID == item._id
              ? true
              : false
          }
          data={item}
          ShowEC={true}
          isYesOrNoelement={isYesOrNo(item.name) ? true : false}
          icon={
            !isEmpty(taskSelect.controllingElementID) && taskSelect.controllingElementID == item._id
              ? 'target'
              : 'arrow-right'
          }
          navigateNext={(val) => {
            navigateNext(item, val);
          }}
        />
      </View>
    );
  };
  const getCategory = async () => {
    if (netConnection) {
      const payload = project._id;
      var result = await dispatch(getCategories(payload));
      dispatch({
        type: 'STORE_ALL_CATEGORY',
        payload: result,
      });
    }
  };
  const elementCreate = async (elementValue) => {
    const {
      elementName,
      category,
      orderAfter,
      rating,
      count,
      relaxationAllowance,
      contingencyAllowance,
    } = elementValue;

    var payload = {
      name: elementName.trim(),
      projectID: project._id,
      customerID: project.customerID,
      categoryID: category._id,
      relaxationAllowance: relaxationAllowance,
      contingencyAllowance: contingencyAllowance,
      studyTypes: [2, 3],

      addPosition: orderAfter,
      taskID: taskSelect._id,

      type: 2,
      count: count,
      userAdded: true,
      rating: rating,
    };
    try {
      setShowCreateModal();
      setModalShow(false);
      setLoader(true);

      if (!netConnection) {
        let elementFound = await searchElementByName(payload.name, project._id, dbDetail);
        if (!isEmpty(elementFound)) {
          let elemPick = elementFound[0];
          payload = {
            ...elemPick,
            userAdded: true,
            status: 'active',
            addPosition: payload.addPosition,
            projectID: payload.projectID,
            taskID: payload.taskID,
            addedBy: payload.addedBy,
          };
        }
      }
      console.log(payload, 'payload');
      await dispatch(creatingData(elementList, payload, netConnection, dbDetail, 'element'));
      getElementData(netConnection);
      setLoader(false);
    } catch (error) {
      setLoader(false);
    }
  };

  useEffect(() => {
    if (onRefreshPage) {
      onRefresh();
    }
  }, [onRefreshPage]);

  const onRefresh = async () => {
    if (!netConnection) {
      dispatch({
        type: ALERT_ERROR,
        payload: INTERNET_ERROR,
      });
    } else {
      getElementData(true);
      setTimeout(() => {
        setShowSearch(false);
      }, 3000);
    }
    setRefreshFalse();
  };

  const toggleOrdering = (type) => {
    setOrderElement(type);
    var sortedObservations = [];
    let elementListData = elementList.slice();
    if (type === 'asec') {
      sortedObservations = elementListData.sort(dynamicSort('name'));
    } else {
      sortedObservations = elementListData.sort(dynamicSort('-name'));
    }
    if (!isEmpty(sortedObservations)) {
      setElementData(sortedObservations);
    }
  };
  return (
    <>
      <StudyHeader
        ShowMagnify={true}
        OnPressIcon={() => setSortModalShow(true)}
        navigation={navigation}
        Title={'Elements'}
        bachkHandler={() => navigateTo('task')}
        SubTitle={'Select elements'}
        showBack={true}
        searchPress={() => {
          setShowSearch(!showSearch), onChangeSearch('');
        }}
        showSearch={showSearch}
        showSorting={!showSearch}
        OnChangeSearch={(val) => onChangeSearch(val)}
      />

      <View onStartShouldSetResponder={() => true} style={styles.container}>
        {!isEmpty(elementData) && !loader ? (
          <FlatList
            data={elementData}
            scrollEnabled={false}
            showsVerticalScrollIndicator={false}
            showsHorizontalScrollIndicator={false}
            keyExtractor={(key, index) => index.toString()}
            renderItem={ItemView}
          />
        ) : !isEmpty(elementList) && isEmpty(elementData) && !isEmpty(elementSearchInput) ? (
          <NoDataFound
            source={localimage.emptydata}
            text={'Sorry, there is no element with that name'}
            imageStyle={{
              height: windowHeight * 0.22,
              width: windowWidth * 0.6,
              marginTop: 55,
              tintColor: colors.primary,
            }}
          />
        ) : isEmpty(elementList) && !loader ? (
          <AText
            fontSize={'large'}
            lightGray
            styleText={{ paddingTop: 70, textAlign: 'center' }}
            fontWeight={FontStyle.fontBold}
          >
            No Data Found{' '}
          </AText>
        ) : null}
      </View>

      {/* ----------------------------Sort element modal------------------------------------------- */}
      <SortingModal
        closeModal={() => setSortModalShow(false)}
        sortModalShow={sortModalShow}
        title={'elements'}
        toggleOrdering={(val) => {
          toggleOrdering(val);
        }}
        orderTask={orderElement}
      />
      {/* ----------------------------sort element modal end------------------------------------------- */}
      {/* ----------------------------create element modal------------------------------------------- */}
      <CreateElementModal
        modalShow={modalShow}
        allCategory={allCategory}
        elementList={[
          { _id: 'addFirst', name: 'Add first' },
          { _id: 'addLast', name: 'Add last' },
          ...elementList,
        ]}
        closeModal={() => {
          setShowCreateModal();
          setModalShow(false);
        }}
        createElement={(val) => elementCreate(val)}
        errorValue={errorValue}
        elementViewNext={elementViewNext}
        closeCanCreateModal={closeCanCreateModal}
        elementValue={elementValue}
        setErrorValue={(val) => setErrorValue(val)}
        setElementViewNext={(val) => setElementViewNext(val)}
        setCloseCanCreateModal={(val) => setCloseCanCreateModal(val)}
        setElementValue={(val) => setElementValue(val)}
      />
      {/* ----------------------------create element modal end------------------------------------------- */}
    </>
  );
};

export default ElementScreen;

const styles = StyleSheet.create({
  container: {
    width: '87%',
    marginTop: 15,
  },
});
