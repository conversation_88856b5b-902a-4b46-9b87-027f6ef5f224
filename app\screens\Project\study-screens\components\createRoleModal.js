import { View, Keyboard } from 'react-native';
import React, { useState } from 'react';
import { AText, BottomModalUI, LinearGradientButton, Textinputs } from '_theme_components';
import { FontStyle, windowHeight, windowWidth } from '_utils';
import { styles } from './styles';
import { BottomModal, ModalSelect } from '_components';
import { isEmpty } from '_helpers';
const positionData = [
  { id: 1, name: 'Area Manager', value: '1' },
  { id: 2, name: 'Manager', value: '2' },
  { id: 3, name: 'Supervisor/Team Leader', value: '3' },
  { id: 4, name: 'Colleague', value: '4' },
];
const createRoleModal = ({ modalShow, createRole, closeCreateModal }) => {
  const [closeCanCreateModal, setCloseCanCreateModal] = useState(false);
  const [checkCreateButton, setCheckCreateButton] = useState(false);
  const [positionModal, setPositionModal] = useState(false);

  const [roleFieldValue, setRoleFieldValue] = useState({
    roleName: '',
    position: '',
  });

  const [errorsField, setErrorsField] = useState({
    roleName: '',
    position: '',
  });
  const createRoleAction = async () => {
    Keyboard.dismiss();
    setErrorsField({
      roleName: '',
      position: '',
    });

    const errors = {};
    let regex = /[^a-zA-Z0-9&\s]/;

    if (regex.test(roleFieldValue.roleName)) {
      errors.roleName = 'Only Letters, Numbers, Ampersand and spaces are allowed';
    }
    if (isEmpty(roleFieldValue.roleName)) {
      errors.roleName = 'Please enter role name';
    }
    if (isEmpty(roleFieldValue.position)) {
      errors.position = 'Please select position';
    }
    if (!isEmpty(errors)) {
      setErrorsField(errors);
      return;
    }

    createRole(roleFieldValue);
    setErrorsField({
      roleName: '',
      position: '',
    });
    setRoleFieldValue({
      roleName: '',
      position: '',
    });
    setCloseCanCreateModal(false);
  };
  const clearModal = () => {
    setErrorsField({
      roleName: '',
      position: '',
    });
    setRoleFieldValue({
      roleName: '',
      position: '',
    });
  };
  return (
    <BottomModalUI
      width={'100%'}
      closeShow
      showScroll
      closeModal={() => {
        Keyboard.dismiss(), setCloseCanCreateModal(true);
      }}
      height={windowHeight > 1000 ? windowHeight * 0.33 : windowHeight * 0.45}
      ModalClose={false}
      modalShow={modalShow}
    >
      <View style={styles.modalContainer}>
        <AText fontWeight={FontStyle.fontBold} styleText={{ color: '#3C4555' }} fontSize={'title'}>
          Create Role
        </AText>
        <LinearGradientButton
          btnStyle={{ borderRadius: 100, width: windowWidth * 0.32 }}
          contentStyles={{ paddingVertical: 10 }}
          disabled={checkCreateButton}
          fontWeight={FontStyle.fontMedium}
          fontSize={'medium'}
          title={'Create Role'}
          onPress={createRoleAction}
        />
      </View>
      <View style={{ width: windowWidth * 0.87, alignSelf: 'center' }}>
        <Textinputs
          label={'Role name'}
          placeholder="Enter role name"
          value={roleFieldValue.roleName}
          onchange={(val) => {
            let regex = /[^a-zA-Z0-9&\s]/;
            if (regex.test(val)) {
              setCheckCreateButton(true);
            } else {
              setCheckCreateButton(false);
            }
            setRoleFieldValue({
              roleName: val,
              position: roleFieldValue.position,
            });
          }}
          error={errorsField.roleName ?? ''}
        />
        <View style={{ marginTop: 15 }}></View>
        <ModalSelect
          dismissable={false}
          heading={'Position'}
          modalOpen={positionModal}
          modalData={positionData}
          placeholder={'Select position'}
          selectedItem={roleFieldValue.position.name}
          selectedDataIndex={positionData.findIndex((n) => n.name === roleFieldValue.position.name)}
          setModalOpen={() => {
            setPositionModal(!positionModal);
          }}
          setDataSelected={(val) => {
            setRoleFieldValue({
              ...roleFieldValue,
              position: val,
            });
            setPositionModal(false);
          }}
          error={errorsField.position ?? ''}
        />
      </View>
      <BottomModal
        height={windowHeight > 1000 ? windowHeight * 0.2 : windowHeight * 0.3}
        modalShow={closeCanCreateModal}
        confirmPress={() => {
          clearModal();
          closeCreateModal();
        }}
        cancelShow={() => setCloseCanCreateModal(false)}
        title={'Do you want to cancel?'}
        body={''}
        confirm={'okay'}
        reject={' cancel '}
      />
    </BottomModalUI>
  );
};

export default createRoleModal;
