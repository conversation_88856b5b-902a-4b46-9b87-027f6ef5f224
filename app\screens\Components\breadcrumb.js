import React from 'react';
import { useSelector } from 'react-redux';
import { Image, View } from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import { AButton } from '_theme_components';
import { localimage, windowWidth } from '_utils';
import { styles } from './styles';

const BreadCrumb = ({ ratings, navigateToStudyPage, studyPage }) => {
  const { areaSelect, taskSelect, elementSelect, projectSelect } = useSelector(
    (state) => state.createStudy
  );
  const buttonArray = [
    {
      id: 1,
      name: areaSelect.name,
      icon: <Icon name="location-outline" style={styles.iconStyle} color={'#fff'} size={15} />,
      navigation: 'area',
      showHeader: studyPage !== 'area',
    },
    {
      id: 2,
      name: taskSelect.name,
      icon: <Icon name="clipboard-outline" style={styles.iconStyle} color={'#fff'} size={15} />,
      navigation: 'task',
      showHeader: studyPage !== 'area' && studyPage !== 'task',
    },
    {
      id: 3,
      name: elementSelect.name,
      icon: <Icon name="document-outline" style={styles.iconStyle} color={'#fff'} size={15} />,
      navigation: 'element',
      showHeader: studyPage !== 'area' && studyPage !== 'task' && studyPage !== 'element',
    },
    {
      id: 4,
      name: ratings,
      icon: <Image source={localimage.trending} style={{}} />,
      showHeader:
        studyPage !== 'area' &&
        studyPage !== 'task' &&
        studyPage !== 'element' &&
        studyPage == 'RateNFrequencyScreen' &&
        !(projectSelect.rating == 1 && elementSelect.rating == 3),
      navigation: '',
    },
  ];
  return (
    <View
      style={{
        ...styles.breadCrumbContainer,
        width: windowWidth,
      }}
    >
      {buttonArray.map(
        ({ name, icon, navigation, showHeader }) =>
          name &&
          showHeader && (
            <AButton
              mode={'text'}
              icon={icon}
              onPress={() => (navigation ? navigateToStudyPage(navigation) : '')}
              title={name.length > 10 ? name.substring(0, 10) + '...' : name}
              styleText={styles.textStyle}
              btnStyle={styles.btnStyle}
              fontSize={'small'}
            />
          )
      )}
    </View>
  );
};

export default BreadCrumb;
