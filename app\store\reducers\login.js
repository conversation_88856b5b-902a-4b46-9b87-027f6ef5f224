import { removeStorage } from '_helpers';
import {
  CLEAR_LOGIN,
  ERROR_LOGIN,
  LOGIN,
  LOGIN_FAIL,
  LOGIN_LOADING,
  LOGOUT_SUCCESS,
  RESETPASSWORD_SUCCESS,
} from '../actions/loginAction';
export const ALREADY_HAS_LOGIN = 'ALREADY_HAS_LOGIN';

const initialState = {
  login: false,
  token: null,
  loading: false,
  loginFail: '',
  restpsdSuccefull: false,
};

export default (state = initialState, action) => {
  switch (action.type) {
    case LOGIN_LOADING:
      return {
        ...state,
        loading: true,
      };

    case ALREADY_HAS_LOGIN:
      return {
        ...state,
        ...action.payload,
        login: true,
        loading: false,
      };

    case LOGIN:
      return {
        ...state,
        ...action.payload,
        login: true,
        loading: false,
        loginFail: '',
      };
    case LOGOUT_SUCCESS:
      removeStorage();
      return {
        ...state,
        login: false,
        loginFail: '',
        token: null,
        loading: false,
      };

    case LOGIN_FAIL:
      return {
        ...state,
        loading: false,
        loginFail: '',
      };
    case ERROR_LOGIN:
      return {
        ...state,
        loading: false,
        loginFail: action.payload,
      };
    case CLEAR_LOGIN:
      return {
        ...state,
        loading: false,
        loginFail: '',
      };
    case RESETPASSWORD_SUCCESS:
      return {
        ...state,
        loading: false,
      };
    case 'USER_LOGOUT':
      return { ...initialState };
    default: {
      return state;
    }
  }
};
