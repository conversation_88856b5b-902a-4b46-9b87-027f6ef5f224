// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    ext {
          buildToolsVersion = "35.0.0"
        minSdkVersion = 24
        compileSdkVersion = 35
        targetSdkVersion = 35
        ndkVersion = '23.1.7779620'//"21.4.7075529"
        kotlin_version = "1.7.22"
    }
    repositories { 
        google()
        mavenCentral()
    }
    dependencies {
        classpath('com.android.tools.build:gradle')
        classpath("com.facebook.react:react-native-gradle-plugin")
         classpath 'com.google.gms:google-services:4.3.15'
        classpath 'com.google.firebase:firebase-crashlytics-gradle:2.9.2'
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version")
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

// allprojects {
//     repositories {
//         // ------ Fix starts here
//     //     exclusiveContent {
//     //        filter {
//     //            includeGroup "com.facebook.react"
//     //        }
//     //        forRepository {
//     //            maven {
//     //                url "$rootDir/../node_modules/react-native/android"
//     //            }
//     //        }
//     //    }
       
//        // --------- Fix ends here
//         maven {
//             // All of React Native (JS, Obj-C sources, Android binaries) is installed from npm
//             url("$rootDir/../node_modules/react-native/android")
//         }
//         maven {
//             // Android JSC is installed from npm
//             url("$rootDir/../node_modules/jsc-android/dist")
//         }
//         mavenCentral {
//             // We don't want to fetch react-native from Maven Central as there are
//             // older versions over there.
//             content {
//                 excludeGroup "com.facebook.react"
//             }
//         }
//         jcenter()
//         google()
//         maven { url 'https://www.jitpack.io' }
//     }
// }
