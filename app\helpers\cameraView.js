import React, { useState, useEffect, useRef } from 'react';
import { SafeAreaView, StyleSheet, View, Platform, Image, Keyboard } from 'react-native';
import MIcons from 'react-native-vector-icons/MaterialIcons';
import Orientation from 'react-native-orientation-locker';
import { CameraScreen } from 'react-native-camera-kit';
import { IMAGE_CAPTURE_ERROR, localimage, windowHeight, windowWidth } from '_utils';
import { Portal, useTheme } from 'react-native-paper';
import { useDispatch, useSelector } from 'react-redux';
import { Cameraopen } from '_provider';
import { AButton, IconButton } from '_theme_components';
import { ALERT_ERROR } from '../store/reducers/alert';
import * as ImagePicker from 'react-native-image-picker';
import { isEmpty } from './isEmpty';
import { Image as ImageCompressor } from 'react-native-compressor';

const OpenCameraView = ({ cancel, customerID, projectID, ImageName, study }) => {
  const cameraRef = useRef(null);
  // console.log(study, ImageName, customerID, ' studycamera openddd');
  const { colors } = useTheme();
  const [showPreviewImage, setShowPreviewImage] = useState(false);
  const [captureImages, setCaptureImages] = useState('');
  const dispatch = useDispatch();
  const { netConnection } = useSelector((state) => state.netInfo);
  const { dbDetail } = useSelector((state) => state.user);
  const { studyID } = useSelector((state) => state.createStudy);

  useEffect(() => {
    if (Platform.OS === 'android') {
      Keyboard.dismiss();
      Orientation.lockToPortrait();
    }
    return () => {
      cleanup();
    };
  }, []);

  const cleanup = () => {
    setCaptureImages('');
    cameraRef.current = null;
    cancel();
    if (Platform.OS === 'android') {
      Orientation.unlockAllOrientations();
    }
  };

  const onBottomButtonPressed = (event) => {
    if (event.image) {
      if (Platform.OS == 'android') {
        Orientation.unlockAllOrientations();
      }
      setCaptureImages(event.image.uri);
      setShowPreviewImage(true);
    }
    // else {
    //   handleCaptureError();
    // }
  };

  const submitPhoto = async () => {
    try {
      const payload = { customerID, projectID, ImageName, study, studyID };
      const result = await ImageCompressor.compress(captureImages, {
        compressionMethod: 'auto',
      });
      await dispatch(Cameraopen(netConnection, result, payload, dbDetail));
      cleanup();
    } catch (error) {
      console.error('Error uploading photo:', error);
    }
  };

  const handleCaptureError = () => {
    cleanup();
    dispatch({
      type: ALERT_ERROR,
      payload: IMAGE_CAPTURE_ERROR,
    });
  };

  const retryCapture = () => {
    cameraRef.current = null;
    if (Platform.OS === 'android') {
      Orientation.lockToPortrait();
    }
    setShowPreviewImage(false);
    setCaptureImages('');
  };

  // Image Picker Code
  const options = {
    title: 'Select Image',
    customButtons: [{ name: 'reTime', title: 'Capture Image' }],
    storageOptions: {
      skipBackup: true,
      path: 'images',
    },
    quality: 0.7,
    presentationStyle: 'fullScreen',
  };

  useEffect(() => {
    (() => {
      if (Platform.OS === 'ios') {
        try {
          ImagePicker.launchCamera(options, (response) => {
            UploadImage(response);
          });
        } catch (e) {
          console.log(e);
        }
      }
    })();
  }, []);

  const UploadImage = async (response) => {
    console.log(response, ' uploadimage reposne');
    if (response.didCancel) {
      cleanup();
    } else if (response.error) {
      handleCaptureError();
    } else if (!isEmpty(response.assets[0].uri)) {
      try {
        const payload = { customerID, projectID, ImageName, study };
        const result = await ImageCompressor.compress(response.assets[0].uri, {
          compressionMethod: 'auto',
        });
        await dispatch(Cameraopen(netConnection, result, payload)).then(() => {
          cleanup();
        });
      } catch (error) {
        console.error('Error compressing or uploading image:', error);
      }
    }
  };

  return (
    <Portal>
      {showPreviewImage && !isEmpty(captureImages) ? (
        <SafeAreaView style={{ flex: 1, backgroundColor: 'black' }}>
          <Image
            source={{ uri: captureImages }}
            style={[
              styles.imageStyle,
              {
                width: windowWidth,
                height: windowHeight < windowWidth ? windowHeight * 0.85 : windowHeight * 0.9,
              },
            ]}
          />
          <View style={styles.buttonContainer}>
            <AButton
              btnStyle={{ ...styles.btnStyle, width: windowWidth * 0.45 }}
              title="Retry"
              bgColor="transparent"
              onPress={retryCapture}
            />
            <AButton
              btnStyle={[styles.btnStyle, { width: windowWidth * 0.45 }]}
              title="OK"
              bgColor="transparent"
              onPress={submitPhoto}
            />
          </View>
        </SafeAreaView>
      ) : Platform.OS === 'android' ? (
        <>
          <SafeAreaView style={styles.cameraContainer}>
            <View style={{ width: '100%', height: '100%' }}>
              <CameraScreen
                ref={cameraRef}
                onBottomButtonPressed={(event) => onBottomButtonPressed(event)}
                cameraType="back"
                flashImages={{
                  on: localimage.flash,
                  off: localimage.flashoff,
                  auto: localimage.flashauto,
                }}
                cameraFlipImage={localimage.cameraflip}
                captureButtonImage={localimage.dots}
                frameColor="white"
                cameraRatioOverlay="3:1"
                captureButtonImageStyle={[styles.pictureButton, { tintColor: colors.primary }]}
              />
            </View>
          </SafeAreaView>
          <IconButton
            bgColor={colors.secondary}
            btnStyle={styles.cancelbuttonStyle}
            icon={
              <MIcons
                name="cancel"
                style={{ alignSelf: 'center' }}
                color={colors.primary}
                size={24}
              />
            }
            onPress={() => {
              cleanup();
            }}
          />
        </>
      ) : null}
    </Portal>
  );
};

export default OpenCameraView;

const styles = StyleSheet.create({
  imageStyle: {
    width: windowWidth,
    height: windowHeight * 0.75,
    marginTop: 2,
    backgroundColor: '#000',
  },
  pictureButton: {
    width: 90,
    height: 90,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 100,
    backgroundColor: 'white',
    alignSelf: 'center',
  },
  cameraContainer: {
    width: Platform.OS === 'ios' ? '100%' : '65%',
    height: Platform.OS === 'ios' ? '100%' : '95%',
    paddingTop: 15,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    paddingBottom: 30,
  },
  btnStyle: {
    height: 90,
    padding: 15,
  },
  cancelbuttonStyle: {
    width: 50,
    height: 50,
    borderRadius: 70,
    position: 'absolute',
    top: 15,
    right: 15,
    alignSelf: 'flex-end',
    alignItems: 'center',
  },
});
