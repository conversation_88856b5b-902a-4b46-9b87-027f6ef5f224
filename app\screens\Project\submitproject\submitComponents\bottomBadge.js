import { StyleSheet, Text, View } from 'react-native';
import React from 'react';
import { AButton, AText } from '_theme_components';
import { FontStyle } from '_utils';
import { useTheme } from 'react-native-paper';
import Icon from 'react-native-vector-icons/Ionicons';

const BottomBadge = ({
  selectObsCount,
  obsCount,
  handleObservation,
  deleteObservation,
  editObservation,
}) => {
  const { colors, dark } = useTheme();

  return (
    <View style={styles.selectionBotttomBar}>
      <View style={styles.selectionBadgeContainer}>
        <View style={styles.badgecontainer}>
          <AText fontSize={'small'} styleText={{ color: '#000' }} fontWeight={FontStyle.fontBold}>
            {selectObsCount}
          </AText>
        </View>
        <AText fontSize={'small'} styleText={{ color: '#fff' }}>
          Item selected.{' '}
        </AText>
        {selectObsCount !== obsCount && (
          <AButton
            mode="text"
            onPress={() => handleObservation('all')}
            btnStyle={styles.btnStyle}
            fontSize={'small'}
            fontWeight={FontStyle.fontBold}
            styleText={{ color: '#fff' }}
            title={'SELECT ALL'}
          />
        )}
        <View style={styles.badgeButtonConatiner}>
          <AButton
            onPress={() => {
              editObservation();
            }}
            mode="text"
            btnStyle={{ padding: 0, marginEnd: 7, paddingVertical: 0 }}
            title={'Edit'}
            fontSize={'medium'}
            styleText={{ color: colors.primary }}
          />
          <AButton
            onPress={() => {
              deleteObservation();
            }}
            mode="text"
            btnStyle={{ padding: 0, marginStart: 7, paddingVertical: 0 }}
            title={'Delete'}
            fontSize={'medium'}
            styleText={{ color: colors.primary }}
          />
        </View>
      </View>
      <AButton
        onPress={() => {
          handleObservation('cancel');
        }}
        mode="text"
        btnStyle={styles.cancelbtnStyle}
        icon={<Icon name="close" style={styles.iconStyle} color={'#fff'} size={22} />}
      />
    </View>
  );
};

export default BottomBadge;

const styles = StyleSheet.create({
  selectionBotttomBar: {
    alignSelf: 'center',
    position: 'absolute',
    bottom: 40,
    backgroundColor: 'rgba(60, 69, 86,0.5)',
    // backgroundColor: 'rgba(80, 88, 101,1)',
    borderRadius: 40,
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  selectionBadgeContainer: {
    backgroundColor: 'rgba(60, 69, 86,0.6)',
    alignSelf: 'center',
    alignItems: 'center',
    paddingStart: 15,
    borderTopLeftRadius: 40,
    borderBottomLeftRadius: 40,
    paddingEnd: 15,
    flexWrap: 'wrap',
    paddingVertical: 15,
    flexDirection: 'row',
  },
  badgecontainer: {
    backgroundColor: '#fff',
    borderRadius: 175,
    paddingHorizontal: 7,
    paddingVertical: 2,
    alignSelf: 'center',
    marginHorizontal: 4,
    justifyContent: 'center',
    alignItems: 'center',
  },
  badgeButtonConatiner: {
    flexDirection: 'row',
    alignSelf: 'center',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 8,
  },
  cancelbtnStyle: {
    flexDirection: 'row',
    alignSelf: 'center',
    alignItems: 'center',
    position: 'relative',
    left: 3,
    paddingVertical: 0,
    padding: 0,
  },
  iconStyle: {
    alignSelf: 'center',
    marginRight: 10,
  },
  btnStyle: {
    flexWrap: 'wrap',
    borderBottomWidth: 0.9,
    borderBottomColor: '#fff',
    padding: 0,
    paddingVertical: 0,
  },
});
