import moment from 'moment';
import { dynamicSort, getDataByTableAndID, isEmpty } from '_helpers';
import { tables } from '_utils';
const ReminderTypes = {
  TIME_OF_DAY: 'TIME_OF_DAY',
  TIME_IN_STUDY: 'TIME_IN_STUDY',
  SHOW_IN_TASK_ELEMENT: 'SHOW_IN_TASK_ELEMENT',
  SHOW_ON_DAYS: 'SHOW_ON_DAYS',
};
export const reminderinitForProject = async (projectID, db) => {
  try {
    const reminders = await getDataByTableAndID(tables.REMINDERS_TABLE, projectID, db);

    if (isEmpty(reminders)) {
      return;
    }

    var sortedReminder = {
      show_on_task_elements: [],
      show_byTime_reminder: [],
    };
    // --divide reminders by type
    reminders.forEach((item) => {
      let { triggerTime, triggerType, showOnDays } = item;
      item.title = 'Reminder';
      item.answerRequired = false;
      item.photo = item.photo == 1 ? true : false;
      item.numericPads = false;
      item.repeat = !isEmpty(item.repeat) && item.repeat == 1 ? true : false;
      let duration, asMinutes;
      if (item.repeat) {
        // keep a copy of the original trigger time to increase the trigger time on every repetition
        item.originalTriggerTime = {
          hours: triggerTime.hours,
          minutes: triggerTime.minutes,
        };
      }
      switch (triggerType) {
        case ReminderTypes.TIME_OF_DAY:
          let triggerTimeOfDay = moment({ H: triggerTime.hours, m: triggerTime.minutes });
          let currentTime = moment(new Date(), 'HH:mm');
          let isAfterCurrentTime = triggerTimeOfDay.isAfter(currentTime);

          triggerTimeOfDay = triggerTimeOfDay.format('HH:mm');
          currentTime = moment(currentTime, 'HH:mm:ss');
          triggerTimeOfDay = moment(triggerTimeOfDay, 'HH:mm:ss');

          duration = moment.duration(triggerTimeOfDay.diff(currentTime));
          asMinutes = duration.asMinutes();
          item.time = 60000 * asMinutes;

          if (isAfterCurrentTime) {
            sortedReminder.show_byTime_reminder.push(item);
          }
          break;
        case ReminderTypes.SHOW_ON_DAYS:
          let triggeringTime;
          let showOnDaysResult = showOnDays.some((key) => {
            const time = moment(key);
            // Replace hour and minute
            time.set({ hour: triggerTime.hours, minute: triggerTime.minutes });
            // let time = `${key} ${triggerTime.hours}:${triggerTime.minutes}:00`;
            let curentTime = moment().format(); //moment(new Date(), 'DD MM YYYY HH:mm:ss');
            triggeringTime = moment(time, 'DD/MM/YYYY HH:mm:ss');

            duration = moment.duration(triggeringTime.diff(curentTime));
            hours = duration.asHours().toFixed(2);
            asMinutes = duration.asMinutes();
            item.time = 60000 * asMinutes;
            return 0 < hours && hours <= 10;
          });

          if (showOnDaysResult) {
            // let timeToDisplay = moment({ H: triggerTime.hours, m: triggerTime.minutes });
            // item.time = moment(timeToDisplay).format("HH:mm");
            sortedReminder.show_byTime_reminder.push(item);
          }
          break;
        case ReminderTypes.SHOW_IN_TASK_ELEMENT:
          sortedReminder.show_on_task_elements.push(item);
          break;
        case ReminderTypes.TIME_IN_STUDY:
          item.time = 60000 * (triggerTime.hours * 60 + triggerTime.minutes);
          sortedReminder.show_byTime_reminder.push(item);
          break;
      }
    });

    return sortedReminder;
  } catch (error) {
    console.error('Error fetching reminders: ', error);
    // Optionally, return some error response
    return { error: 'Failed to initialize reminders' };
  }
};

export const checkTaskReminderTrigger = (reminder, elementID, taskID) => {
  let reminderToAsked = [];
  if (!isEmpty(reminder)) {
    reminder.map((item) => {
      item.title = 'Reminder';
      if (item.taskID == taskID) {
        if (item.elementID.findIndex((n) => n === elementID) !== -1) {
          reminderToAsked.push(item);
        }
      }
    });
  }
  return reminderToAsked;
};
